"""
📊 Signal Database Model

Beanie ODM model for trading signals following DATABASE_PATTERNS.md
with proper indexing, validation, and optimization.
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from beanie import Document, Indexed
from pydantic import Field, validator
from pymongo import IndexModel, ASCENDING, DESCENDING

from .base import BaseDocument
from ...shared.types import SignalType, SignalStrength


class Signal(BaseDocument):
    """
    📊 Trading Signal Model
    
    Stores trading signals with technical analysis data, risk assessment,
    and performance tracking following V2 architecture patterns.
    """
    
    # 🎯 Core Signal Data
    token_address: Indexed(str) = Field(..., description="Target token address")
    signal_type: SignalType = Field(..., description="Signal type (BUY/SELL/HOLD)")
    strength: SignalStrength = Field(..., description="Signal strength (1-4)")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    
    # 💰 Price and Position Data
    entry_price: Decimal = Field(..., description="Recommended entry price")
    stop_loss: Optional[Decimal] = Field(None, description="Stop loss price")
    take_profit: Optional[Decimal] = Field(None, description="Take profit price")
    position_size_usd: Decimal = Field(..., description="Recommended position size")
    
    # 📈 Analysis Data
    reasoning: str = Field(..., description="Signal reasoning and analysis")
    technical_indicators: Dict[str, float] = Field(default_factory=dict, description="Technical indicators")
    volume_analysis: Dict[str, float] = Field(default_factory=dict, description="Volume analysis data")
    liquidity_analysis: Dict[str, float] = Field(default_factory=dict, description="Liquidity analysis")
    market_sentiment: Dict[str, float] = Field(default_factory=dict, description="Market sentiment data")
    
    # 🔍 Risk Assessment
    risk_score: float = Field(..., ge=0.0, le=1.0, description="Risk assessment score")
    risk_factors: List[str] = Field(default_factory=list, description="Identified risk factors")
    supporting_factors: List[str] = Field(default_factory=list, description="Supporting factors")
    
    # ⏰ Timing Data
    expires_at: datetime = Field(..., description="Signal expiration time")
    generated_by: str = Field(default="system", description="Signal generator source")
    
    # 📊 Performance Tracking
    is_active: bool = Field(default=True, description="Signal active status")
    is_executed: bool = Field(default=False, description="Signal execution status")
    execution_price: Optional[Decimal] = Field(None, description="Actual execution price")
    execution_time: Optional[datetime] = Field(None, description="Execution timestamp")
    performance_pnl: Optional[Decimal] = Field(None, description="Signal performance P&L")
    
    # 🔗 Related Data
    related_signals: List[str] = Field(default_factory=list, description="Related signal IDs")
    source_data: Dict[str, Any] = Field(default_factory=dict, description="Source data references")
    
    @validator("confidence", "risk_score")
    def validate_percentage(cls, v):
        """Validate percentage values are between 0 and 1"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Value must be between 0.0 and 1.0")
        return v
    
    @validator("entry_price", "position_size_usd")
    def validate_positive_values(cls, v):
        """Validate positive decimal values"""
        if v <= 0:
            raise ValueError("Value must be positive")
        return v
    
    @validator("expires_at")
    def validate_expiration(cls, v):
        """Validate expiration is in the future"""
        if v <= datetime.utcnow():
            raise ValueError("Expiration time must be in the future")
        return v
    
    @property
    def is_expired(self) -> bool:
        """Check if signal has expired"""
        return datetime.utcnow() > self.expires_at
    
    @property
    def time_to_expiry(self) -> timedelta:
        """Calculate time remaining until expiration"""
        return self.expires_at - datetime.utcnow()
    
    @property
    def signal_age(self) -> timedelta:
        """Calculate signal age since creation"""
        return datetime.utcnow() - self.created_at
    
    def calculate_performance(self, current_price: Decimal) -> Dict[str, Any]:
        """
        Calculate signal performance metrics
        
        Args:
            current_price: Current token price
            
        Returns:
            Performance metrics dictionary
        """
        if not self.execution_price:
            return {"status": "not_executed"}
        
        # Calculate P&L based on signal type
        if self.signal_type == SignalType.BUY:
            pnl_percent = ((current_price - self.execution_price) / self.execution_price) * 100
        elif self.signal_type == SignalType.SELL:
            pnl_percent = ((self.execution_price - current_price) / self.execution_price) * 100
        else:
            pnl_percent = Decimal("0")
        
        pnl_usd = (pnl_percent / 100) * self.position_size_usd
        
        return {
            "status": "executed",
            "entry_price": self.execution_price,
            "current_price": current_price,
            "pnl_percent": float(pnl_percent),
            "pnl_usd": float(pnl_usd),
            "position_size_usd": float(self.position_size_usd),
            "execution_time": self.execution_time,
            "signal_age": self.signal_age.total_seconds()
        }
    
    class Settings:
        name = "signals"
        indexes = [
            # Core query indexes
            IndexModel([("token_address", ASCENDING), ("created_at", DESCENDING)]),
            IndexModel([("signal_type", ASCENDING), ("is_active", ASCENDING)]),
            IndexModel([("strength", DESCENDING), ("confidence", DESCENDING)]),
            
            # Performance indexes
            IndexModel([("is_executed", ASCENDING), ("execution_time", DESCENDING)]),
            IndexModel([("expires_at", ASCENDING), ("is_active", ASCENDING)]),
            
            # Analysis indexes
            IndexModel([("risk_score", ASCENDING), ("confidence", DESCENDING)]),
            IndexModel([("generated_by", ASCENDING), ("created_at", DESCENDING)]),
            
            # Compound indexes for complex queries
            IndexModel([
                ("token_address", ASCENDING),
                ("signal_type", ASCENDING), 
                ("is_active", ASCENDING),
                ("created_at", DESCENDING)
            ]),
            IndexModel([
                ("strength", DESCENDING),
                ("confidence", DESCENDING),
                ("risk_score", ASCENDING),
                ("created_at", DESCENDING)
            ])
        ]
