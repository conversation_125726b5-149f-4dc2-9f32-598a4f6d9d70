# 🚀 DEPLOYMENT & SCALING STRATEGIES - Zero to Production

## 🏗️ DEPLOYMENT ARCHITECTURE

### 🌐 MULTI-ENVIRONMENT SETUP
```
Environments:
├── Development (Local)     # Local development with hot reload
├── Staging                 # Production-like environment for testing
├── Production             # Live production environment
└── Testing                # Automated testing environment
```

### 🔧 ENVIRONMENT CONFIGURATION
```javascript
// config/environments/index.js
const environments = {
  development: require('./development'),
  staging: require('./staging'),
  production: require('./production'),
  test: require('./test')
};

const currentEnv = process.env.NODE_ENV || 'development';

module.exports = {
  ...environments[currentEnv],
  environment: currentEnv,
  isProduction: currentEnv === 'production',
  isDevelopment: currentEnv === 'development'
};
```

### 🐳 DOCKER CONFIGURATION
```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Development image
FROM base AS dev
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]

# Production image
FROM base AS production
WORKDIR /app

# Copy dependencies
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

EXPOSE 3000
CMD ["npm", "start"]
```

### 📦 DOCKER COMPOSE
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      target: dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongo:27017/myapp
      - REDIS_URL=redis://redis:6379
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mongo
      - redis

  mongo:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mongo_data:
  redis_data:
```

## 🔄 CI/CD PIPELINE

### 🚀 GITHUB ACTIONS WORKFLOW
```yaml
# .github/workflows/deploy.yml
name: Deploy Application

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:6
        ports:
          - 27017:27017
      redis:
        image: redis:7
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run unit tests
        run: npm run test:unit
        env:
          MONGODB_URI: mongodb://localhost:27017/test
          REDIS_URL: redis://localhost:6379
      
      - name: Run integration tests
        run: npm run test:integration
        env:
          MONGODB_URI: mongodb://localhost:27017/test
          REDIS_URL: redis://localhost:6379
      
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Log in to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          target: production
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment"
          # Add your staging deployment commands here

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Deploy to production
        run: |
          echo "Deploying to production environment"
          # Add your production deployment commands here
```

## ☁️ CLOUD DEPLOYMENT STRATEGIES

### 🌊 KUBERNETES DEPLOYMENT
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: myapp
  labels:
    app: myapp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: myapp
  template:
    metadata:
      labels:
        app: myapp
    spec:
      containers:
      - name: myapp
        image: ghcr.io/username/myapp:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: mongodb-uri
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: redis-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: myapp-service
spec:
  selector:
    app: myapp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: LoadBalancer
```

### 🔧 HORIZONTAL POD AUTOSCALER
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: myapp-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: myapp
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 📊 MONITORING & OBSERVABILITY

### 🔍 HEALTH CHECK ENDPOINTS
```javascript
// routes/health.js
const express = require('express');
const mongoose = require('mongoose');
const redis = require('../config/redis');

const router = express.Router();

// Basic health check
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV
  });
});

// Detailed readiness check
router.get('/ready', async (req, res) => {
  const checks = {
    database: false,
    cache: false,
    memory: false
  };

  try {
    // Check database connection
    if (mongoose.connection.readyState === 1) {
      checks.database = true;
    }

    // Check Redis connection
    if (redis.status === 'ready') {
      checks.cache = true;
    }

    // Check memory usage
    const memUsage = process.memoryUsage();
    const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    checks.memory = memUsagePercent < 90;

    const allHealthy = Object.values(checks).every(check => check === true);

    res.status(allHealthy ? 200 : 503).json({
      status: allHealthy ? 'ready' : 'not ready',
      checks,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      error: error.message,
      checks,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
```

### 📈 PROMETHEUS METRICS
```javascript
// middleware/metrics.js
const promClient = require('prom-client');

// Create a Registry
const register = new promClient.Registry();

// Add default metrics
promClient.collectDefaultMetrics({ register });

// Custom metrics
const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status'],
  buckets: [0.1, 0.5, 1, 2, 5]
});

const httpRequestTotal = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status']
});

const activeConnections = new promClient.Gauge({
  name: 'active_connections',
  help: 'Number of active connections'
});

register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(activeConnections);

// Middleware to collect metrics
const metricsMiddleware = (req, res, next) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route ? req.route.path : req.path;
    
    httpRequestDuration
      .labels(req.method, route, res.statusCode)
      .observe(duration);
    
    httpRequestTotal
      .labels(req.method, route, res.statusCode)
      .inc();
  });

  next();
};

// Metrics endpoint
const metricsEndpoint = (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(register.metrics());
};

module.exports = {
  metricsMiddleware,
  metricsEndpoint,
  register
};
```

## 🔄 BLUE-GREEN DEPLOYMENT

### 🎯 DEPLOYMENT STRATEGY
```javascript
// scripts/deploy.js
class BlueGreenDeployment {
  constructor(config) {
    this.config = config;
    this.currentEnvironment = null;
    this.targetEnvironment = null;
  }

  async deploy() {
    console.log('Starting blue-green deployment...');
    
    // 1. Determine current and target environments
    await this.determineEnvironments();
    
    // 2. Deploy to target environment
    await this.deployToTarget();
    
    // 3. Run health checks
    await this.runHealthChecks();
    
    // 4. Switch traffic
    await this.switchTraffic();
    
    // 5. Cleanup old environment
    await this.cleanup();
    
    console.log('Blue-green deployment completed successfully!');
  }

  async determineEnvironments() {
    // Logic to determine which environment is currently active
    this.currentEnvironment = await this.getCurrentEnvironment();
    this.targetEnvironment = this.currentEnvironment === 'blue' ? 'green' : 'blue';
    
    console.log(`Current: ${this.currentEnvironment}, Target: ${this.targetEnvironment}`);
  }

  async deployToTarget() {
    console.log(`Deploying to ${this.targetEnvironment} environment...`);
    // Deployment logic here
  }

  async runHealthChecks() {
    console.log('Running health checks...');
    // Health check logic here
  }

  async switchTraffic() {
    console.log(`Switching traffic to ${this.targetEnvironment}...`);
    // Traffic switching logic here
  }

  async cleanup() {
    console.log(`Cleaning up ${this.currentEnvironment} environment...`);
    // Cleanup logic here
  }
}

module.exports = BlueGreenDeployment;
```

## 📋 DEPLOYMENT CHECKLIST

### ✅ PRE-DEPLOYMENT CHECKLIST
- [ ] All tests passing (unit, integration, e2e)
- [ ] Security scan completed
- [ ] Performance testing completed
- [ ] Database migrations prepared
- [ ] Environment variables configured
- [ ] Monitoring and alerting set up
- [ ] Rollback plan prepared
- [ ] Documentation updated

### 🚀 DEPLOYMENT CHECKLIST
- [ ] Deploy to staging environment
- [ ] Run smoke tests in staging
- [ ] Deploy to production
- [ ] Verify health checks
- [ ] Monitor application metrics
- [ ] Verify database connectivity
- [ ] Check external service integrations
- [ ] Validate user-facing functionality

### 📊 POST-DEPLOYMENT CHECKLIST
- [ ] Monitor error rates and performance
- [ ] Verify all features working correctly
- [ ] Check log aggregation
- [ ] Validate monitoring alerts
- [ ] Update deployment documentation
- [ ] Communicate deployment status
- [ ] Plan next iteration improvements
