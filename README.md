# Dune Analytics Token Tracker

A production-ready Python service that tracks profitable token trades using Dune Analytics data and sends updates to a Telegram channel.

## Features

- Automated Dune Analytics query execution every 30 minutes
- MongoDB Atlas integration for data storage
- Telegram channel notifications for new token trades
- Caching mechanism to avoid unnecessary query executions
- Comprehensive test coverage
- Error handling and reporting

## Prerequisites

- Python 3.8+
- MongoDB Atlas account
- Dune Analytics API key
- Telegram Bot Token and Channel ID

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd dune-analytics-tracker
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create a `.env` file based on `.env.example`:
```bash
cp .env.example .env
```

5. Edit the `.env` file with your credentials:
```
MONGODB_URI=your_mongodb_atlas_uri_here
DUNE_API_KEY=your_dune_api_key_here
DUNE_QUERY_ID=4625658
DUNE_EXECUTION_ID=7705504
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHANNEL_ID=your_telegram_channel_id_here
QUERY_INTERVAL_MINUTES=30
CACHE_DURATION_MINUTES=60
```

## Usage

1. Run the service:
```bash
python src/main.py
```

2. Run tests:
```bash
pytest tests/ -v --cov=src
```

## Project Structure

```
.
├── src/
│   ├── __init__.py
│   ├── main.py
│   ├── dune_client.py
│   ├── mongo_client.py
│   └── telegram_client.py
├── tests/
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_dune_client.py
│   ├── test_mongo_client.py
│   └── test_telegram_client.py
├── requirements.txt
├── .env.example
└── README.md
```

## Testing

The project includes comprehensive tests for all components:

- Dune Analytics client tests
- MongoDB client tests
- Telegram client tests
- Integration tests

Run the test suite with coverage:
```bash
pytest tests/ -v --cov=src
```

## Error Handling

The service includes robust error handling:

- API request failures
- Database connection issues
- Telegram message delivery failures
- Query execution timeouts

All errors are logged and reported to the Telegram channel.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details. 