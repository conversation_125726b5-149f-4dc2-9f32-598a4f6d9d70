"""
📝 Logging Configuration Module

Structured logging configuration following LOG_RULES.md guidelines
with proper levels, formatting, and environment-aware settings.
"""

import sys
import logging
from typing import Dict, Any
from pathlib import Path
import structlog
from structlog.stdlib import LoggerFactory
from .settings import get_settings


class LoggingConfig:
    """
    📝 Centralized logging configuration following V2 instructions
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.log_level = self._get_log_level()
        
    def _get_log_level(self) -> int:
        """Get numeric log level from string"""
        level_map = {
            "trace": 5,
            "debug": logging.DEBUG,
            "info": logging.INFO,
            "warn": logging.WARNING,
            "error": logging.ERROR,
            "fatal": logging.CRITICAL
        }
        return level_map.get(self.settings.log_level, logging.INFO)
    
    def configure_logging(self) -> None:
        """
        🔧 Configure structured logging with proper formatting
        """
        # Configure structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                self._get_processor(),
            ],
            context_class=dict,
            logger_factory=LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        # Configure standard library logging
        logging.basicConfig(
            format="%(message)s",
            stream=sys.stdout,
            level=self.log_level,
        )
        
        # Set third-party library log levels
        self._configure_third_party_loggers()
    
    def _get_processor(self):
        """Get appropriate processor based on environment and format"""
        if self.settings.log_format == "json":
            return structlog.processors.JSONRenderer()
        else:
            return structlog.dev.ConsoleRenderer(colors=not self.settings.is_production)
    
    def _configure_third_party_loggers(self) -> None:
        """Configure log levels for third-party libraries"""
        third_party_loggers = {
            "httpx": logging.WARNING,
            "urllib3": logging.WARNING,
            "aiohttp": logging.WARNING,
            "websockets": logging.WARNING,
            "motor": logging.INFO,
            "pymongo": logging.WARNING,
            "redis": logging.WARNING,
            "telegram": logging.INFO,
            "solana": logging.INFO,
            "ccxt": logging.WARNING,
        }
        
        for logger_name, level in third_party_loggers.items():
            logging.getLogger(logger_name).setLevel(level)
    
    def get_logger(self, name: str) -> structlog.stdlib.BoundLogger:
        """
        📝 Get a configured logger instance
        
        Args:
            name: Logger name (usually __name__)
            
        Returns:
            Configured structlog logger
        """
        return structlog.get_logger(name)
    
    def create_request_logger(self, request_id: str, user_id: str = None) -> structlog.stdlib.BoundLogger:
        """
        🔍 Create logger with request context
        
        Args:
            request_id: Unique request identifier
            user_id: Optional user identifier
            
        Returns:
            Logger with request context
        """
        logger = self.get_logger("request")
        context = {"request_id": request_id}
        if user_id:
            context["user_id"] = user_id
        return logger.bind(**context)
    
    def create_feature_logger(self, feature: str, **context) -> structlog.stdlib.BoundLogger:
        """
        🎯 Create logger for specific feature
        
        Args:
            feature: Feature name
            **context: Additional context
            
        Returns:
            Logger with feature context
        """
        logger = self.get_logger(f"feature.{feature}")
        return logger.bind(feature=feature, **context)


def setup_logging() -> LoggingConfig:
    """
    🚀 Initialize logging configuration
    
    Returns:
        Configured logging instance
    """
    config = LoggingConfig()
    config.configure_logging()
    return config


# Global logger instance
logger_config = setup_logging()
logger = logger_config.get_logger(__name__)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    📝 Get logger instance for module
    
    Args:
        name: Module name (usually __name__)
        
    Returns:
        Configured logger
    """
    return logger_config.get_logger(name)
