"""
🌊 Raydium API Client

Raydium DEX client for pool information, liquidity data, trading pair validation,
and pool analytics following V2 architecture patterns.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from decimal import Decimal
import httpx
import websockets
import json
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import API_CONSTANTS, ERROR_CONSTANTS
from ...shared.types import APIResponse, TokenData, TokenMetrics

logger = get_logger(__name__)


class RaydiumClient:
    """
    🌊 Raydium API Client for pool and liquidity data
    
    Provides comprehensive Raydium DEX integration with:
    - Pool information fetching
    - Liquidity data retrieval
    - Trading pair validation
    - Pool analytics and metrics
    """
    
    def __init__(self, project_id: str = "v2"):
        self.settings = get_settings()
        self.project_id = project_id
        self.base_url = self.settings.raydium_api_url
        
        # Rate limiting
        self.rate_limit_requests = 60  # requests per minute
        self.rate_limit_window = 60  # seconds
        self.request_timestamps = []
        
        # HTTP client with proper configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(
                connect=10.0,
                read=30.0,
                write=10.0,
                pool=30.0
            ),
            limits=httpx.Limits(
                max_keepalive_connections=10,
                max_connections=20
            ),
            headers={
                "Content-Type": "application/json",
                "User-Agent": f"TokenTracker-V2/{self.project_id}",
                "Accept": "application/json"
            }
        )
        
        # WebSocket connection for real-time updates
        self.ws_connection = None
        self.ws_subscriptions = set()
        
        logger.info(
            "Raydium client initialized",
            project_id=self.project_id,
            base_url=self.base_url
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def close(self):
        """Close HTTP client and WebSocket connections"""
        try:
            if self.ws_connection:
                await self.ws_connection.close()
                self.ws_connection = None
            
            await self.client.aclose()
            
            logger.info("Raydium client closed successfully")
        except Exception as e:
            logger.error(
                "Error closing Raydium client",
                error=str(e),
                error_type=type(e).__name__
            )
    
    def _check_rate_limit(self) -> bool:
        """Check if request is within rate limits"""
        now = time.time()
        
        # Remove old timestamps outside the window
        self.request_timestamps = [
            ts for ts in self.request_timestamps 
            if now - ts < self.rate_limit_window
        ]
        
        # Check if we're within limits
        if len(self.request_timestamps) >= self.rate_limit_requests:
            logger.warning(
                "Rate limit reached",
                current_requests=len(self.request_timestamps),
                limit=self.rate_limit_requests,
                window_seconds=self.rate_limit_window
            )
            return False
        
        # Add current timestamp
        self.request_timestamps.append(now)
        return True
    
    async def _wait_for_rate_limit(self):
        """Wait if rate limit is exceeded"""
        if not self._check_rate_limit():
            # Calculate wait time
            oldest_timestamp = min(self.request_timestamps)
            wait_time = self.rate_limit_window - (time.time() - oldest_timestamp)
            
            if wait_time > 0:
                logger.info(
                    "Waiting for rate limit reset",
                    wait_seconds=wait_time
                )
                await asyncio.sleep(wait_time)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
        retry=retry_if_exception_type((httpx.RequestError, httpx.HTTPStatusError))
    )
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with retry logic and rate limiting"""
        await self._wait_for_rate_limit()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = await self.client.request(method, url, **kwargs)
            response.raise_for_status()
            
            logger.debug(
                "Raydium API request successful",
                method=method,
                endpoint=endpoint,
                status_code=response.status_code
            )
            
            return response.json()
            
        except httpx.HTTPStatusError as e:
            logger.error(
                "HTTP error in Raydium API request",
                status_code=e.response.status_code,
                url=url,
                method=method,
                response_text=e.response.text[:500] if e.response else None
            )
            raise
        except httpx.RequestError as e:
            logger.error(
                "Request error in Raydium API",
                error=str(e),
                url=url,
                method=method
            )
            raise
    
    async def get_pool_info(self, pool_id: str) -> Optional[Dict[str, Any]]:
        """
        🏊 Get pool information from Raydium
        
        Args:
            pool_id: Pool ID or token pair address
            
        Returns:
            Pool information dictionary
        """
        try:
            logger.info(
                "Fetching pool info from Raydium",
                pool_id=pool_id
            )
            
            response = await self._make_request(
                "GET",
                f"pools/{pool_id}"
            )
            
            if not response:
                logger.warning(
                    "No pool info returned from Raydium",
                    pool_id=pool_id
                )
                return None
            
            logger.info(
                "Pool info fetched successfully",
                pool_id=pool_id,
                base_mint=response.get("baseMint"),
                quote_mint=response.get("quoteMint")
            )
            
            return response
            
        except Exception as e:
            logger.error(
                "Error fetching pool info from Raydium",
                pool_id=pool_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def get_pool_list(self, page: int = 1, page_size: int = 100) -> Optional[Dict[str, Any]]:
        """
        📋 Get list of pools from Raydium
        
        Args:
            page: Page number (default: 1)
            page_size: Number of pools per page (default: 100)
            
        Returns:
            Dictionary with pools list and pagination info
        """
        try:
            logger.info(
                "Fetching pool list from Raydium",
                page=page,
                page_size=page_size
            )
            
            params = {
                "page": page,
                "pageSize": page_size
            }
            
            response = await self._make_request(
                "GET",
                "pools",
                params=params
            )
            
            if not response:
                logger.warning("No pool list returned from Raydium")
                return None
            
            logger.info(
                "Pool list fetched successfully",
                page=page,
                total_pools=response.get("count", 0)
            )
            
            return response
            
        except Exception as e:
            logger.error(
                "Error fetching pool list from Raydium",
                page=page,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def get_token_pools(self, token_address: str) -> Optional[List[Dict[str, Any]]]:
        """
        🎯 Get all pools containing a specific token
        
        Args:
            token_address: Token mint address
            
        Returns:
            List of pools containing the token
        """
        try:
            logger.info(
                "Fetching token pools from Raydium",
                token_address=token_address
            )
            
            response = await self._make_request(
                "GET",
                f"pools/token/{token_address}"
            )
            
            if not response:
                logger.warning(
                    "No pools found for token",
                    token_address=token_address
                )
                return None
            
            # Response might be a list or dict with data field
            pools = response if isinstance(response, list) else response.get("data", [])
            
            logger.info(
                "Token pools fetched successfully",
                token_address=token_address,
                pool_count=len(pools)
            )
            
            return pools
            
        except Exception as e:
            logger.error(
                "Error fetching token pools from Raydium",
                token_address=token_address,
                error=str(e),
                error_type=type(e).__name__
            )
            return None

    async def get_liquidity_info(self, pool_id: str) -> Optional[Dict[str, Any]]:
        """
        💧 Get liquidity information for a pool

        Args:
            pool_id: Pool ID

        Returns:
            Liquidity information dictionary
        """
        try:
            logger.info(
                "Fetching liquidity info from Raydium",
                pool_id=pool_id
            )

            response = await self._make_request(
                "GET",
                f"pools/{pool_id}/liquidity"
            )

            if not response:
                logger.warning(
                    "No liquidity info returned from Raydium",
                    pool_id=pool_id
                )
                return None

            logger.info(
                "Liquidity info fetched successfully",
                pool_id=pool_id,
                total_liquidity=response.get("totalLiquidity")
            )

            return response

        except Exception as e:
            logger.error(
                "Error fetching liquidity info from Raydium",
                pool_id=pool_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return None

    async def validate_trading_pair(self, base_token: str, quote_token: str) -> bool:
        """
        ✅ Validate if a trading pair exists on Raydium

        Args:
            base_token: Base token mint address
            quote_token: Quote token mint address

        Returns:
            True if trading pair exists
        """
        try:
            logger.info(
                "Validating trading pair on Raydium",
                base_token=base_token,
                quote_token=quote_token
            )

            # Get pools for base token
            base_pools = await self.get_token_pools(base_token)

            if not base_pools:
                logger.info(
                    "No pools found for base token",
                    base_token=base_token
                )
                return False

            # Check if any pool has the quote token
            for pool in base_pools:
                pool_base = pool.get("baseMint")
                pool_quote = pool.get("quoteMint")

                # Check both directions
                if ((pool_base == base_token and pool_quote == quote_token) or
                    (pool_base == quote_token and pool_quote == base_token)):

                    logger.info(
                        "Trading pair validated successfully",
                        base_token=base_token,
                        quote_token=quote_token,
                        pool_id=pool.get("id")
                    )
                    return True

            logger.info(
                "Trading pair not found on Raydium",
                base_token=base_token,
                quote_token=quote_token
            )
            return False

        except Exception as e:
            logger.error(
                "Error validating trading pair on Raydium",
                base_token=base_token,
                quote_token=quote_token,
                error=str(e),
                error_type=type(e).__name__
            )
            return False

    async def get_pool_analytics(self, pool_id: str, timeframe: str = "24h") -> Optional[Dict[str, Any]]:
        """
        📊 Get pool analytics and metrics

        Args:
            pool_id: Pool ID
            timeframe: Time frame for analytics (24h, 7d, 30d)

        Returns:
            Pool analytics dictionary
        """
        try:
            logger.info(
                "Fetching pool analytics from Raydium",
                pool_id=pool_id,
                timeframe=timeframe
            )

            params = {"timeframe": timeframe}

            response = await self._make_request(
                "GET",
                f"pools/{pool_id}/analytics",
                params=params
            )

            if not response:
                logger.warning(
                    "No analytics returned from Raydium",
                    pool_id=pool_id
                )
                return None

            logger.info(
                "Pool analytics fetched successfully",
                pool_id=pool_id,
                timeframe=timeframe,
                volume=response.get("volume")
            )

            return response

        except Exception as e:
            logger.error(
                "Error fetching pool analytics from Raydium",
                pool_id=pool_id,
                timeframe=timeframe,
                error=str(e),
                error_type=type(e).__name__
            )
            return None

    async def get_top_pools(self, sort_by: str = "volume", limit: int = 50) -> Optional[List[Dict[str, Any]]]:
        """
        🏆 Get top performing pools

        Args:
            sort_by: Sort criteria (volume, liquidity, apy)
            limit: Number of pools to return

        Returns:
            List of top pools
        """
        try:
            logger.info(
                "Fetching top pools from Raydium",
                sort_by=sort_by,
                limit=limit
            )

            params = {
                "sortBy": sort_by,
                "limit": limit
            }

            response = await self._make_request(
                "GET",
                "pools/top",
                params=params
            )

            if not response:
                logger.warning("No top pools returned from Raydium")
                return None

            pools = response if isinstance(response, list) else response.get("data", [])

            logger.info(
                "Top pools fetched successfully",
                sort_by=sort_by,
                pool_count=len(pools)
            )

            return pools

        except Exception as e:
            logger.error(
                "Error fetching top pools from Raydium",
                sort_by=sort_by,
                error=str(e),
                error_type=type(e).__name__
            )
            return None

    async def health_check(self) -> bool:
        """
        🏥 Check Raydium API health

        Returns:
            True if API is healthy
        """
        try:
            # Try to fetch pools list with minimal data
            response = await self._make_request(
                "GET",
                "pools",
                params={"page": 1, "pageSize": 1},
                timeout=5
            )

            if response:
                logger.info("Raydium API health check passed")
                return True
            else:
                logger.warning("Raydium API health check failed - no response")
                return False

        except Exception as e:
            logger.error(
                "Raydium API health check failed",
                error=str(e),
                error_type=type(e).__name__
            )
            return False
