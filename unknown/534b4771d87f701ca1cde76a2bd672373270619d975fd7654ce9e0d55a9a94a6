"""
✅ Data Validator Module

Data validation service for token address validation, price consistency checks,
liquidity threshold validation, and data freshness monitoring following V2 architecture patterns.
"""

import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, Tuple
from decimal import Decimal
from dataclasses import dataclass

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import API_CONSTANTS, ERROR_CONSTANTS, TRADING_CONSTANTS
from ...shared.types import TokenData, TokenMetrics

logger = get_logger(__name__)


@dataclass
class ValidationResult:
    """Result of data validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    confidence_score: float
    
    def add_error(self, error: str):
        """Add validation error"""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str):
        """Add validation warning"""
        self.warnings.append(warning)
    
    def has_errors(self) -> bool:
        """Check if validation has errors"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if validation has warnings"""
        return len(self.warnings) > 0


class DataValidator:
    """
    ✅ Data Validator for comprehensive data validation
    
    Provides validation services for:
    - Token address validation
    - Price data consistency checks
    - Liquidity threshold validation
    - Data freshness monitoring
    """
    
    def __init__(self, project_id: str = "v2"):
        self.settings = get_settings()
        self.project_id = project_id
        
        # Validation thresholds
        self.min_liquidity_usd = self.settings.min_liquidity_usd
        self.max_price_deviation = 0.10  # 10% max deviation between sources
        self.max_data_age_minutes = 30  # Data older than 30 minutes is stale
        
        # Solana address pattern
        self.solana_address_pattern = re.compile(r'^[1-9A-HJ-NP-Za-km-z]{32,44}$')
        
        logger.info(
            "Data validator initialized",
            project_id=self.project_id,
            min_liquidity_usd=self.min_liquidity_usd
        )
    
    def validate_token_address(self, token_address: str) -> ValidationResult:
        """
        🏷️ Validate Solana token address format
        
        Args:
            token_address: Token mint address to validate
            
        Returns:
            ValidationResult with validation status
        """
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            confidence_score=1.0
        )
        
        try:
            logger.debug(
                "Validating token address",
                token_address=token_address
            )
            
            # Check if address is provided
            if not token_address:
                result.add_error("Token address is required")
                return result
            
            # Check address format
            if not self.solana_address_pattern.match(token_address):
                result.add_error("Invalid Solana address format")
                return result
            
            # Check address length
            if len(token_address) < 32 or len(token_address) > 44:
                result.add_error("Invalid Solana address length")
                return result
            
            # Check for common invalid addresses
            invalid_addresses = [
                "11111111111111111111111111111111",  # System program
                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",  # Token program
            ]
            
            if token_address in invalid_addresses:
                result.add_error("Address is a system program, not a token")
                return result
            
            logger.debug(
                "Token address validation passed",
                token_address=token_address
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "Error validating token address",
                token_address=token_address,
                error=str(e),
                error_type=type(e).__name__
            )
            result.add_error(f"Validation error: {str(e)}")
            return result
    
    def validate_price_data(self, price_data: List[TokenData]) -> ValidationResult:
        """
        💰 Validate price data consistency across sources
        
        Args:
            price_data: List of TokenData from different sources
            
        Returns:
            ValidationResult with consistency check results
        """
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            confidence_score=1.0
        )
        
        try:
            if not price_data:
                result.add_error("No price data provided")
                return result
            
            # Filter out data without prices
            valid_prices = [data for data in price_data if data.price is not None and data.price > 0]
            
            if len(valid_prices) == 0:
                result.add_error("No valid price data found")
                return result
            
            if len(valid_prices) == 1:
                result.add_warning("Only one price source available")
                result.confidence_score = 0.7
                return result
            
            # Calculate price statistics
            prices = [float(data.price) for data in valid_prices]
            avg_price = sum(prices) / len(prices)
            min_price = min(prices)
            max_price = max(prices)
            
            # Check price deviation
            if avg_price > 0:
                max_deviation = max(
                    abs(price - avg_price) / avg_price for price in prices
                )
                
                if max_deviation > self.max_price_deviation:
                    result.add_warning(
                        f"High price deviation detected: {max_deviation:.2%}"
                    )
                    result.confidence_score *= 0.8
                
                # Check for extreme outliers
                if max_price / min_price > 2.0:  # 100% difference
                    result.add_error(
                        f"Extreme price difference detected: {max_price/min_price:.2f}x"
                    )
            
            # Check data freshness
            now = datetime.utcnow()
            stale_data_count = 0
            
            for data in valid_prices:
                if data.timestamp:
                    age = now - data.timestamp
                    if age > timedelta(minutes=self.max_data_age_minutes):
                        stale_data_count += 1
            
            if stale_data_count > 0:
                result.add_warning(
                    f"{stale_data_count} price sources have stale data"
                )
                result.confidence_score *= 0.9
            
            logger.debug(
                "Price data validation completed",
                source_count=len(valid_prices),
                avg_price=avg_price,
                max_deviation=max_deviation if 'max_deviation' in locals() else 0
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "Error validating price data",
                error=str(e),
                error_type=type(e).__name__
            )
            result.add_error(f"Price validation error: {str(e)}")
            return result
    
    def validate_liquidity_threshold(self, liquidity_usd: Optional[Decimal], token_address: str = None) -> ValidationResult:
        """
        💧 Validate liquidity meets minimum threshold
        
        Args:
            liquidity_usd: Total liquidity in USD
            token_address: Optional token address for logging
            
        Returns:
            ValidationResult with liquidity validation
        """
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            confidence_score=1.0
        )
        
        try:
            logger.debug(
                "Validating liquidity threshold",
                liquidity_usd=str(liquidity_usd) if liquidity_usd else None,
                token_address=token_address
            )
            
            if liquidity_usd is None:
                result.add_warning("No liquidity data available")
                result.confidence_score = 0.5
                return result
            
            if liquidity_usd <= 0:
                result.add_error("Invalid liquidity value")
                return result
            
            # Check minimum liquidity threshold
            if liquidity_usd < self.min_liquidity_usd:
                result.add_error(
                    f"Liquidity ${liquidity_usd:,.2f} below minimum threshold ${self.min_liquidity_usd:,.2f}"
                )
                return result
            
            # Warning for low liquidity
            warning_threshold = self.min_liquidity_usd * 2
            if liquidity_usd < warning_threshold:
                result.add_warning(
                    f"Low liquidity detected: ${liquidity_usd:,.2f}"
                )
                result.confidence_score = 0.8
            
            logger.debug(
                "Liquidity validation passed",
                liquidity_usd=str(liquidity_usd),
                threshold=self.min_liquidity_usd
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "Error validating liquidity threshold",
                liquidity_usd=str(liquidity_usd) if liquidity_usd else None,
                error=str(e),
                error_type=type(e).__name__
            )
            result.add_error(f"Liquidity validation error: {str(e)}")
            return result

    def validate_token_metrics(self, metrics: TokenMetrics) -> ValidationResult:
        """
        📊 Validate token metrics for reasonableness

        Args:
            metrics: TokenMetrics to validate

        Returns:
            ValidationResult with metrics validation
        """
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            confidence_score=1.0
        )

        try:
            logger.debug(
                "Validating token metrics",
                token_address=metrics.token_address
            )

            # Validate price changes
            if metrics.price_change_24h is not None:
                if abs(metrics.price_change_24h) > 1000:  # 1000% change
                    result.add_warning(
                        f"Extreme 24h price change: {metrics.price_change_24h:.2f}%"
                    )
                    result.confidence_score *= 0.8

            # Validate volume changes
            if metrics.volume_change_24h is not None:
                if abs(metrics.volume_change_24h) > 500:  # 500% volume change
                    result.add_warning(
                        f"Extreme 24h volume change: {metrics.volume_change_24h:.2f}%"
                    )
                    result.confidence_score *= 0.9

            # Validate transaction counts
            if metrics.transaction_count_24h is not None:
                if metrics.transaction_count_24h < 0:
                    result.add_error("Invalid transaction count")
                elif metrics.transaction_count_24h == 0:
                    result.add_warning("No transactions in 24h")
                    result.confidence_score *= 0.7

            # Validate holder count
            if metrics.holder_count is not None:
                if metrics.holder_count < 0:
                    result.add_error("Invalid holder count")
                elif metrics.holder_count < 10:
                    result.add_warning("Very low holder count")
                    result.confidence_score *= 0.8

            return result

        except Exception as e:
            logger.error(
                "Error validating token metrics",
                error=str(e),
                error_type=type(e).__name__
            )
            result.add_error(f"Metrics validation error: {str(e)}")
            return result

    def validate_comprehensive(self, token_address: str, price_data: List[TokenData],
                             liquidity_usd: Optional[Decimal], metrics: Optional[TokenMetrics] = None) -> ValidationResult:
        """
        🔍 Comprehensive validation combining all checks

        Args:
            token_address: Token address to validate
            price_data: Price data from multiple sources
            liquidity_usd: Total liquidity in USD
            metrics: Optional token metrics

        Returns:
            Combined ValidationResult
        """
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            confidence_score=1.0
        )

        try:
            logger.info(
                "Performing comprehensive validation",
                token_address=token_address
            )

            # Validate token address
            address_result = self.validate_token_address(token_address)
            result.errors.extend(address_result.errors)
            result.warnings.extend(address_result.warnings)
            result.confidence_score *= address_result.confidence_score

            if address_result.has_errors():
                result.is_valid = False
                return result

            # Validate price data
            if price_data:
                price_result = self.validate_price_data(price_data)
                result.errors.extend(price_result.errors)
                result.warnings.extend(price_result.warnings)
                result.confidence_score *= price_result.confidence_score

                if price_result.has_errors():
                    result.is_valid = False
            else:
                result.add_warning("No price data provided")
                result.confidence_score *= 0.8

            # Validate liquidity
            liquidity_result = self.validate_liquidity_threshold(liquidity_usd, token_address)
            result.errors.extend(liquidity_result.errors)
            result.warnings.extend(liquidity_result.warnings)
            result.confidence_score *= liquidity_result.confidence_score

            if liquidity_result.has_errors():
                result.is_valid = False

            # Validate metrics if provided
            if metrics:
                metrics_result = self.validate_token_metrics(metrics)
                result.errors.extend(metrics_result.errors)
                result.warnings.extend(metrics_result.warnings)
                result.confidence_score *= metrics_result.confidence_score

                if metrics_result.has_errors():
                    result.is_valid = False

            # Final validation summary
            logger.info(
                "Comprehensive validation completed",
                token_address=token_address,
                is_valid=result.is_valid,
                error_count=len(result.errors),
                warning_count=len(result.warnings),
                confidence_score=result.confidence_score
            )

            return result

        except Exception as e:
            logger.error(
                "Error in comprehensive validation",
                token_address=token_address,
                error=str(e),
                error_type=type(e).__name__
            )
            result.add_error(f"Comprehensive validation error: {str(e)}")
            return result

    def get_validation_summary(self, results: List[ValidationResult]) -> Dict[str, Any]:
        """
        📋 Get summary of multiple validation results

        Args:
            results: List of ValidationResult objects

        Returns:
            Summary dictionary
        """
        try:
            total_results = len(results)
            valid_results = len([r for r in results if r.is_valid])

            all_errors = []
            all_warnings = []
            confidence_scores = []

            for result in results:
                all_errors.extend(result.errors)
                all_warnings.extend(result.warnings)
                confidence_scores.append(result.confidence_score)

            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0

            summary = {
                "total_validations": total_results,
                "valid_count": valid_results,
                "invalid_count": total_results - valid_results,
                "success_rate": valid_results / total_results if total_results > 0 else 0.0,
                "total_errors": len(all_errors),
                "total_warnings": len(all_warnings),
                "average_confidence": avg_confidence,
                "unique_errors": list(set(all_errors)),
                "unique_warnings": list(set(all_warnings))
            }

            logger.info(
                "Validation summary generated",
                **summary
            )

            return summary

        except Exception as e:
            logger.error(
                "Error generating validation summary",
                error=str(e),
                error_type=type(e).__name__
            )
            return {
                "total_validations": 0,
                "valid_count": 0,
                "invalid_count": 0,
                "success_rate": 0.0,
                "total_errors": 0,
                "total_warnings": 0,
                "average_confidence": 0.0,
                "unique_errors": [],
                "unique_warnings": []
            }
    
    def validate_data_freshness(self, timestamp: datetime, max_age_minutes: Optional[int] = None) -> ValidationResult:
        """
        🕐 Validate data freshness
        
        Args:
            timestamp: Data timestamp
            max_age_minutes: Maximum age in minutes (uses default if None)
            
        Returns:
            ValidationResult with freshness validation
        """
        result = ValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            confidence_score=1.0
        )
        
        try:
            if max_age_minutes is None:
                max_age_minutes = self.max_data_age_minutes
            
            if not timestamp:
                result.add_error("No timestamp provided")
                return result
            
            now = datetime.utcnow()
            age = now - timestamp
            max_age = timedelta(minutes=max_age_minutes)
            
            logger.debug(
                "Validating data freshness",
                timestamp=timestamp.isoformat(),
                age_minutes=age.total_seconds() / 60,
                max_age_minutes=max_age_minutes
            )
            
            if age > max_age:
                result.add_error(
                    f"Data is stale: {age.total_seconds()/60:.1f} minutes old"
                )
                return result
            
            # Warning for data approaching staleness
            warning_threshold = max_age * 0.8
            if age > warning_threshold:
                result.add_warning(
                    f"Data approaching staleness: {age.total_seconds()/60:.1f} minutes old"
                )
                result.confidence_score = 0.9
            
            return result
            
        except Exception as e:
            logger.error(
                "Error validating data freshness",
                timestamp=timestamp.isoformat() if timestamp else None,
                error=str(e),
                error_type=type(e).__name__
            )
            result.add_error(f"Freshness validation error: {str(e)}")
            return result
