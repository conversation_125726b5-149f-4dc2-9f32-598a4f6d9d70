"""
🔄 Data Aggregator Service

Multi-source data consolidation service that aggregates data from Jupiter, Raydium,
Solana RPC, and Dune Analytics following V2 architecture patterns.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from decimal import Decimal
import json
from dataclasses import dataclass, asdict

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import API_CONSTANTS, ERROR_CONSTANTS
from ...shared.types import TokenData, TokenMetrics, APIResponse

from .dune_client import DuneClient
from .jupiter_client import JupiterClient
from .raydium_client import RaydiumClient
from .solana_client import SolanaClient

logger = get_logger(__name__)


@dataclass
class AggregatedTokenData:
    """Aggregated token data from multiple sources"""
    token_address: str
    symbol: str
    name: str
    
    # Price data (primarily from Jupiter)
    price: Optional[Decimal] = None
    price_source: Optional[str] = None
    price_timestamp: Optional[datetime] = None
    
    # Market data
    market_cap: Optional[Decimal] = None
    volume_24h: Optional[Decimal] = None
    
    # Raydium pool data
    pools: Optional[List[Dict[str, Any]]] = None
    total_liquidity: Optional[Decimal] = None
    largest_pool_id: Optional[str] = None
    
    # Solana blockchain data
    token_supply: Optional[Decimal] = None
    holder_count: Optional[int] = None
    
    # Dune Analytics data
    trading_activity: Optional[Dict[str, Any]] = None
    
    # Aggregation metadata
    last_updated: datetime = None
    data_sources: List[str] = None
    confidence_score: float = 0.0
    
    def __post_init__(self):
        if self.last_updated is None:
            self.last_updated = datetime.utcnow()
        if self.data_sources is None:
            self.data_sources = []


class DataAggregator:
    """
    🔄 Data Aggregator for multi-source data consolidation
    
    Provides comprehensive data aggregation with:
    - Multi-source data consolidation
    - Data validation and cleaning
    - Cache management with Redis
    - Real-time data streaming
    """
    
    def __init__(self, project_id: str = "v2"):
        self.settings = get_settings()
        self.project_id = project_id
        
        # Initialize clients
        self.dune_client = DuneClient(project_id)
        self.jupiter_client = JupiterClient(project_id)
        self.raydium_client = RaydiumClient(project_id)
        self.solana_client = SolanaClient(project_id)
        
        # Cache for aggregated data
        self.cache = {}
        self.cache_ttl = timedelta(minutes=5)  # 5-minute cache
        
        logger.info(
            "Data aggregator initialized",
            project_id=self.project_id
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def close(self):
        """Close all client connections"""
        try:
            await self.dune_client.close()
            await self.jupiter_client.close()
            await self.raydium_client.close()
            await self.solana_client.close()
            
            logger.info("Data aggregator closed successfully")
        except Exception as e:
            logger.error(
                "Error closing data aggregator",
                error=str(e),
                error_type=type(e).__name__
            )
    
    def _is_cache_valid(self, token_address: str) -> bool:
        """Check if cached data is still valid"""
        if token_address not in self.cache:
            return False
        
        cached_data = self.cache[token_address]
        cache_time = cached_data.last_updated
        
        return datetime.utcnow() - cache_time < self.cache_ttl
    
    def _calculate_confidence_score(self, data: AggregatedTokenData) -> float:
        """Calculate confidence score based on available data sources"""
        score = 0.0
        max_score = 100.0
        
        # Price data (30 points)
        if data.price is not None:
            score += 30.0
        
        # Pool/liquidity data (25 points)
        if data.pools and data.total_liquidity:
            score += 25.0
        elif data.pools or data.total_liquidity:
            score += 15.0
        
        # Blockchain data (20 points)
        if data.token_supply is not None:
            score += 20.0
        
        # Trading activity (15 points)
        if data.trading_activity:
            score += 15.0
        
        # Multiple sources bonus (10 points)
        if len(data.data_sources) >= 3:
            score += 10.0
        elif len(data.data_sources) >= 2:
            score += 5.0
        
        return min(score / max_score, 1.0)
    
    async def get_aggregated_token_data(self, token_address: str, force_refresh: bool = False) -> Optional[AggregatedTokenData]:
        """
        📊 Get aggregated token data from all sources
        
        Args:
            token_address: Token mint address
            force_refresh: Force refresh even if cached data exists
            
        Returns:
            Aggregated token data
        """
        try:
            # Check cache first
            if not force_refresh and self._is_cache_valid(token_address):
                logger.debug(
                    "Returning cached aggregated data",
                    token_address=token_address
                )
                return self.cache[token_address]
            
            logger.info(
                "Aggregating token data from all sources",
                token_address=token_address
            )
            
            # Initialize aggregated data
            aggregated = AggregatedTokenData(
                token_address=token_address,
                symbol="UNKNOWN",
                name="Unknown Token",
                data_sources=[],
                last_updated=datetime.utcnow()
            )
            
            # Fetch data from all sources concurrently
            tasks = [
                self._fetch_jupiter_data(token_address),
                self._fetch_raydium_data(token_address),
                self._fetch_solana_data(token_address),
                self._fetch_dune_data(token_address)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            jupiter_data, raydium_data, solana_data, dune_data = results
            
            # Aggregate Jupiter data
            if isinstance(jupiter_data, TokenData):
                aggregated.symbol = jupiter_data.symbol
                aggregated.name = jupiter_data.name
                aggregated.price = jupiter_data.price
                aggregated.price_source = "Jupiter"
                aggregated.price_timestamp = jupiter_data.timestamp
                aggregated.market_cap = jupiter_data.market_cap
                aggregated.volume_24h = jupiter_data.volume_24h
                aggregated.data_sources.append("Jupiter")
            
            # Aggregate Raydium data
            if isinstance(raydium_data, dict) and raydium_data.get("pools"):
                aggregated.pools = raydium_data["pools"]
                aggregated.total_liquidity = raydium_data.get("total_liquidity")
                aggregated.largest_pool_id = raydium_data.get("largest_pool_id")
                aggregated.data_sources.append("Raydium")
            
            # Aggregate Solana data
            if isinstance(solana_data, dict):
                aggregated.token_supply = solana_data.get("token_supply")
                aggregated.holder_count = solana_data.get("holder_count")
                aggregated.data_sources.append("Solana")
            
            # Aggregate Dune data
            if isinstance(dune_data, dict):
                aggregated.trading_activity = dune_data
                aggregated.data_sources.append("Dune")
            
            # Calculate confidence score
            aggregated.confidence_score = self._calculate_confidence_score(aggregated)
            
            # Cache the result
            self.cache[token_address] = aggregated
            
            logger.info(
                "Token data aggregated successfully",
                token_address=token_address,
                data_sources=aggregated.data_sources,
                confidence_score=aggregated.confidence_score
            )
            
            return aggregated
            
        except Exception as e:
            logger.error(
                "Error aggregating token data",
                token_address=token_address,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def _fetch_jupiter_data(self, token_address: str) -> Optional[TokenData]:
        """Fetch data from Jupiter API"""
        try:
            return await self.jupiter_client.get_token_price(token_address)
        except Exception as e:
            logger.warning(
                "Failed to fetch Jupiter data",
                token_address=token_address,
                error=str(e)
            )
            return None
    
    async def _fetch_raydium_data(self, token_address: str) -> Optional[Dict[str, Any]]:
        """Fetch data from Raydium API"""
        try:
            pools = await self.raydium_client.get_token_pools(token_address)
            
            if not pools:
                return None
            
            # Calculate total liquidity and find largest pool
            total_liquidity = Decimal("0")
            largest_pool = None
            largest_liquidity = Decimal("0")
            
            for pool in pools:
                pool_liquidity = Decimal(str(pool.get("liquidity", 0)))
                total_liquidity += pool_liquidity
                
                if pool_liquidity > largest_liquidity:
                    largest_liquidity = pool_liquidity
                    largest_pool = pool.get("id")
            
            return {
                "pools": pools,
                "total_liquidity": total_liquidity,
                "largest_pool_id": largest_pool
            }
            
        except Exception as e:
            logger.warning(
                "Failed to fetch Raydium data",
                token_address=token_address,
                error=str(e)
            )
            return None
    
    async def _fetch_solana_data(self, token_address: str) -> Optional[Dict[str, Any]]:
        """Fetch data from Solana RPC"""
        try:
            account_info = await self.solana_client.get_token_account_info(token_address)
            
            if not account_info:
                return None
            
            # Extract token supply
            token_supply = None
            if "supply" in account_info and account_info["supply"].value:
                supply_info = account_info["supply"].value
                token_supply = Decimal(str(supply_info.ui_amount or 0))
            
            return {
                "token_supply": token_supply,
                "holder_count": None  # Would need additional RPC calls
            }
            
        except Exception as e:
            logger.warning(
                "Failed to fetch Solana data",
                token_address=token_address,
                error=str(e)
            )
            return None
    
    async def _fetch_dune_data(self, token_address: str) -> Optional[Dict[str, Any]]:
        """Fetch data from Dune Analytics"""
        try:
            # This would be customized based on specific Dune queries
            # For now, return None as Dune queries are project-specific
            return None
            
        except Exception as e:
            logger.warning(
                "Failed to fetch Dune data",
                token_address=token_address,
                error=str(e)
            )
            return None

    async def get_multiple_aggregated_data(self, token_addresses: List[str], force_refresh: bool = False) -> Dict[str, Optional[AggregatedTokenData]]:
        """
        📊 Get aggregated data for multiple tokens

        Args:
            token_addresses: List of token mint addresses
            force_refresh: Force refresh even if cached data exists

        Returns:
            Dictionary mapping token addresses to aggregated data
        """
        try:
            logger.info(
                "Aggregating data for multiple tokens",
                token_count=len(token_addresses)
            )

            # Process tokens concurrently
            tasks = [
                self.get_aggregated_token_data(addr, force_refresh)
                for addr in token_addresses
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Build result dictionary
            aggregated_data = {}
            for i, token_address in enumerate(token_addresses):
                result = results[i]
                if isinstance(result, AggregatedTokenData):
                    aggregated_data[token_address] = result
                else:
                    aggregated_data[token_address] = None
                    if isinstance(result, Exception):
                        logger.warning(
                            "Failed to aggregate data for token",
                            token_address=token_address,
                            error=str(result)
                        )

            successful_count = len([v for v in aggregated_data.values() if v is not None])

            logger.info(
                "Multiple token aggregation completed",
                requested_count=len(token_addresses),
                successful_count=successful_count
            )

            return aggregated_data

        except Exception as e:
            logger.error(
                "Error aggregating multiple token data",
                token_count=len(token_addresses),
                error=str(e),
                error_type=type(e).__name__
            )
            return {addr: None for addr in token_addresses}

    async def validate_token_availability(self, token_address: str) -> Dict[str, bool]:
        """
        ✅ Validate token availability across all data sources

        Args:
            token_address: Token mint address

        Returns:
            Dictionary showing availability per source
        """
        try:
            logger.info(
                "Validating token availability",
                token_address=token_address
            )

            # Check availability concurrently
            tasks = [
                self._check_jupiter_availability(token_address),
                self._check_raydium_availability(token_address),
                self._check_solana_availability(token_address)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)
            jupiter_available, raydium_available, solana_available = results

            availability = {
                "jupiter": isinstance(jupiter_available, bool) and jupiter_available,
                "raydium": isinstance(raydium_available, bool) and raydium_available,
                "solana": isinstance(solana_available, bool) and solana_available,
                "overall": False
            }

            # Overall availability if at least 2 sources have the token
            available_count = sum(availability.values())
            availability["overall"] = available_count >= 2

            logger.info(
                "Token availability validated",
                token_address=token_address,
                availability=availability
            )

            return availability

        except Exception as e:
            logger.error(
                "Error validating token availability",
                token_address=token_address,
                error=str(e),
                error_type=type(e).__name__
            )
            return {
                "jupiter": False,
                "raydium": False,
                "solana": False,
                "overall": False
            }
