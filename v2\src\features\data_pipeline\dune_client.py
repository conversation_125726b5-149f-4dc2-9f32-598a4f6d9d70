"""
🔍 Enhanced Dune Analytics Client

Improved Dune client addressing the multi-project execution issues
with better state management and isolated execution tracking.
"""

import asyncio
import os
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional, List
from pathlib import Path
import httpx
import dateutil.parser
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import API_CONSTANTS, ERROR_CONSTANTS
from ...shared.types import APIResponse

logger = get_logger(__name__)


class DuneClient:
    """
    🔍 Enhanced Dune Analytics client with isolated execution tracking
    """
    
    def __init__(self, project_id: str = "v2"):
        self.settings = get_settings()
        self.project_id = project_id
        self.base_url = self.settings.dune_base_url
        self.api_key = self.settings.dune_api_key
        self.query_id = self.settings.dune_query_id
        
        # Project-specific execution tracking
        self.execution_file = f".last_execution_id_{project_id}"
        
        # HTTP client with proper configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(
                connect=10.0,
                read=120.0,
                write=10.0,
                pool=120.0
            ),
            limits=httpx.Limits(
                max_keepalive_connections=5,
                max_connections=10
            ),
            headers={
                "x-dune-api-key": self.api_key,
                "Content-Type": "application/json",
                "User-Agent": f"TokenTracker-V2/{self.project_id}"
            }
        )
        
        logger.info(
            "Dune client initialized",
            project_id=project_id,
            query_id=self.query_id,
            execution_file=self.execution_file
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def close(self):
        """Close HTTP client"""
        await self.client.aclose()
    
    def _read_last_execution_id(self) -> Optional[str]:
        """Read project-specific execution ID"""
        try:
            if os.path.exists(self.execution_file):
                with open(self.execution_file, 'r') as f:
                    execution_id = f.read().strip()
                    logger.debug(
                        "Read execution ID from file",
                        execution_id=execution_id,
                        file=self.execution_file
                    )
                    return execution_id
        except IOError as e:
            logger.error(
                "Error reading execution ID file",
                error=str(e),
                file=self.execution_file
            )
        return None
    
    def _write_last_execution_id(self, execution_id: str) -> None:
        """Write project-specific execution ID"""
        try:
            with open(self.execution_file, 'w') as f:
                f.write(execution_id)
            logger.debug(
                "Wrote execution ID to file",
                execution_id=execution_id,
                file=self.execution_file
            )
        except IOError as e:
            logger.error(
                "Error writing execution ID file",
                error=str(e),
                file=self.execution_file,
                execution_id=execution_id
            )
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, httpx.HTTPStatusError))
    )
    async def _make_request(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with retry logic"""
        try:
            response = await self.client.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(
                "HTTP error in Dune API request",
                status_code=e.response.status_code,
                url=url,
                method=method,
                response_text=e.response.text[:500] if e.response else None
            )
            raise
        except httpx.RequestError as e:
            logger.error(
                "Request error in Dune API",
                error=str(e),
                url=url,
                method=method
            )
            raise
    
    async def _get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get execution status with enhanced error handling"""
        status_url = f"{self.base_url}/execution/{execution_id}/status"
        
        try:
            response_data = await self._make_request("GET", status_url)
            
            state = response_data.get("state")
            ended_at_str = response_data.get("execution_ended_at")
            ended_at_dt = None
            
            if ended_at_str:
                try:
                    ended_at_dt = dateutil.parser.isoparse(ended_at_str)
                    if ended_at_dt.tzinfo is None:
                        ended_at_dt = ended_at_dt.replace(tzinfo=timezone.utc)
                except ValueError:
                    logger.warning(
                        "Could not parse execution_ended_at timestamp",
                        timestamp=ended_at_str,
                        execution_id=execution_id
                    )
            
            logger.debug(
                "Retrieved execution status",
                execution_id=execution_id,
                state=state,
                ended_at=ended_at_dt
            )
            
            return {"state": state, "ended_at": ended_at_dt}
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                logger.warning(
                    "Execution ID not found (404)",
                    execution_id=execution_id
                )
                return None
            else:
                logger.error(
                    "HTTP error getting execution status",
                    execution_id=execution_id,
                    status_code=e.response.status_code
                )
                return None
        except Exception as e:
            logger.error(
                "Unexpected error getting execution status",
                execution_id=execution_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def _get_execution_results(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Fetch results for completed execution"""
        results_url = f"{self.base_url}/execution/{execution_id}/results"
        
        try:
            logger.info(
                "Fetching execution results",
                execution_id=execution_id
            )
            
            results = await self._make_request("GET", results_url)
            
            # Validate results structure
            if not results.get('result', {}).get('rows'):
                logger.warning(
                    "Execution completed but returned no result rows",
                    execution_id=execution_id
                )
                return None
            
            row_count = len(results['result']['rows'])
            logger.info(
                "Successfully retrieved execution results",
                execution_id=execution_id,
                row_count=row_count
            )
            
            return results
            
        except Exception as e:
            logger.error(
                "Error fetching execution results",
                execution_id=execution_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def _start_new_execution(self) -> Optional[str]:
        """Start new query execution"""
        execute_url = f"{self.base_url}/query/{self.query_id}/execute"
        
        try:
            logger.info(
                "Starting new query execution",
                query_id=self.query_id,
                project_id=self.project_id
            )
            
            response_data = await self._make_request(
                "POST",
                execute_url,
                json={"query_parameters": {}}
            )
            
            execution_id = response_data.get("execution_id")
            if execution_id:
                self._write_last_execution_id(execution_id)
                logger.info(
                    "New execution started successfully",
                    execution_id=execution_id,
                    query_id=self.query_id
                )
                return execution_id
            else:
                logger.error(
                    "No execution ID in response",
                    response=response_data
                )
                return None
                
        except Exception as e:
            logger.error(
                "Error starting new execution",
                query_id=self.query_id,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def _poll_execution_status(self, execution_id: str, timeout_minutes: int = 60) -> Optional[Dict[str, Any]]:
        """Poll execution status until completion"""
        logger.info(
            "Starting execution polling",
            execution_id=execution_id,
            timeout_minutes=timeout_minutes
        )
        
        timeout_seconds = timeout_minutes * 60
        start_time = time.time()
        poll_interval = 30  # Poll every 30 seconds
        
        while True:
            # Check timeout
            if time.time() - start_time > timeout_seconds:
                logger.error(
                    "Execution polling timed out",
                    execution_id=execution_id,
                    timeout_minutes=timeout_minutes
                )
                raise TimeoutError(f"Polling timed out for execution {execution_id}")
            
            # Get status
            status_info = await self._get_execution_status(execution_id)
            
            if status_info is None:
                logger.warning(
                    "Could not determine execution status, retrying",
                    execution_id=execution_id
                )
                await asyncio.sleep(poll_interval)
                continue
            
            state = status_info.get("state")
            logger.debug(
                "Execution status check",
                execution_id=execution_id,
                state=state
            )
            
            # Handle different states
            if state == "QUERY_STATE_COMPLETED":
                logger.info(
                    "Execution completed successfully",
                    execution_id=execution_id
                )
                return await self._get_execution_results(execution_id)
            
            elif state in ["QUERY_STATE_FAILED", "QUERY_STATE_CANCELLED", "QUERY_STATE_EXPIRED"]:
                logger.error(
                    "Execution failed",
                    execution_id=execution_id,
                    state=state
                )
                return None
            
            elif state in ["QUERY_STATE_PENDING", "QUERY_STATE_EXECUTING"]:
                logger.debug(
                    "Execution still running",
                    execution_id=execution_id,
                    state=state
                )
                await asyncio.sleep(poll_interval)
            
            else:
                logger.warning(
                    "Unknown execution state",
                    execution_id=execution_id,
                    state=state
                )
                await asyncio.sleep(poll_interval)
    
    async def execute_query(self) -> Optional[Dict[str, Any]]:
        """
        🚀 Execute Dune query with enhanced isolation and error handling
        """
        logger.info(
            "Starting query execution process",
            project_id=self.project_id,
            query_id=self.query_id
        )
        
        execution_to_poll = None
        min_completion_age = timedelta(hours=2)
        
        # Check for existing execution
        last_execution_id = self._read_last_execution_id()
        
        if last_execution_id:
            logger.info(
                "Found existing execution ID",
                execution_id=last_execution_id
            )
            
            status_info = await self._get_execution_status(last_execution_id)
            
            if status_info:
                state = status_info.get("state")
                ended_at = status_info.get("ended_at")
                
                if state in ["QUERY_STATE_PENDING", "QUERY_STATE_EXECUTING"]:
                    logger.info(
                        "Resuming existing execution",
                        execution_id=last_execution_id,
                        state=state
                    )
                    execution_to_poll = last_execution_id
                
                elif state == "QUERY_STATE_COMPLETED" and ended_at:
                    time_since_completion = datetime.now(timezone.utc) - ended_at
                    
                    if time_since_completion < min_completion_age:
                        logger.info(
                            "Recent execution found, fetching results",
                            execution_id=last_execution_id,
                            completed_ago=str(time_since_completion)
                        )
                        return await self._get_execution_results(last_execution_id)
                    else:
                        logger.info(
                            "Execution too old, starting new one",
                            execution_id=last_execution_id,
                            completed_ago=str(time_since_completion)
                        )
                
                else:
                    logger.info(
                        "Previous execution in terminal state, starting new one",
                        execution_id=last_execution_id,
                        state=state
                    )
        
        # Start new execution if needed
        if execution_to_poll is None:
            execution_to_poll = await self._start_new_execution()
            if not execution_to_poll:
                logger.error("Failed to start new execution")
                return None
        
        # Poll for results with retry logic
        max_attempts = 2
        for attempt in range(max_attempts):
            try:
                logger.info(
                    "Polling execution",
                    execution_id=execution_to_poll,
                    attempt=attempt + 1,
                    max_attempts=max_attempts
                )
                
                results = await self._poll_execution_status(execution_to_poll)
                
                if results:
                    logger.info(
                        "Query execution completed successfully",
                        execution_id=execution_to_poll,
                        row_count=len(results.get('result', {}).get('rows', []))
                    )
                    return results
                else:
                    logger.warning(
                        "Execution completed but no results",
                        execution_id=execution_to_poll
                    )
                    
            except TimeoutError:
                if attempt < max_attempts - 1:
                    logger.warning(
                        "Execution timed out, starting new one",
                        execution_id=execution_to_poll,
                        attempt=attempt + 1
                    )
                    execution_to_poll = await self._start_new_execution()
                    if not execution_to_poll:
                        break
                else:
                    logger.error(
                        "All execution attempts failed",
                        final_execution_id=execution_to_poll
                    )
            
            except Exception as e:
                logger.error(
                    "Unexpected error during execution polling",
                    execution_id=execution_to_poll,
                    error=str(e),
                    error_type=type(e).__name__
                )
                break
        
        logger.error(
            "Query execution failed after all attempts",
            project_id=self.project_id
        )
        return None
    
    async def get_query_info(self) -> Optional[Dict[str, Any]]:
        """Get query information"""
        query_url = f"{self.base_url}/query/{self.query_id}"
        
        try:
            return await self._make_request("GET", query_url)
        except Exception as e:
            logger.error(
                "Error fetching query info",
                query_id=self.query_id,
                error=str(e)
            )
            return None
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            query_info = await self.get_query_info()
            
            return {
                "status": "healthy" if query_info else "unhealthy",
                "query_id": self.query_id,
                "project_id": self.project_id,
                "api_accessible": query_info is not None,
                "execution_file_exists": os.path.exists(self.execution_file)
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "query_id": self.query_id,
                "project_id": self.project_id
            }
