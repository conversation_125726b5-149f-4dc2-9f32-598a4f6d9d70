import os
from typing import Dict, Any, List
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv
from logger import setup_logger

load_dotenv()

class DuneMongoClient:
    def __init__(self):
        self.logger = setup_logger('mongo_client')
        self.client = MongoClient(os.getenv('MONGODB_URI'))
        self.db = self.client.dune_analytics
        self.collection = self.db.query_results
        self.logger.info("Initialized MongoDB connection")

    def save_query_results(self, results: Dict[str, Any]) -> str:
        """Save query results to MongoDB."""
        self.logger.info("Saving query results to MongoDB")
        try:
            document = {
                "timestamp": datetime.utcnow(),
                "results": results,
                "processed": False
            }
            result = self.collection.insert_one(document)
            doc_id = str(result.inserted_id)
            self.logger.debug(f"Saved document with ID: {doc_id}")
            return doc_id
        except Exception as e:
            self.logger.error(f"Failed to save query results: {str(e)}")
            raise

    def get_unprocessed_results(self) -> List[Dict[str, Any]]:
        """Get all unprocessed results."""
        self.logger.info("Fetching unprocessed results")
        try:
            results = list(self.collection.find({"processed": False}))
            self.logger.debug(f"Found {len(results)} unprocessed results")
            return results
        except Exception as e:
            self.logger.error(f"Failed to fetch unprocessed results: {str(e)}")
            raise

    def mark_as_processed(self, document_id: str) -> None:
        """Mark a document as processed."""
        self.logger.info(f"Marking document {document_id} as processed")
        try:
            self.collection.update_one(
                {"_id": document_id},
                {"$set": {"processed": True}}
            )
            self.logger.debug(f"Successfully marked document {document_id} as processed")
        except Exception as e:
            self.logger.error(f"Failed to mark document as processed: {str(e)}")
            raise

    def get_latest_results(self) -> Dict[str, Any]:
        """Get the most recent query results."""
        self.logger.info("Fetching latest results")
        try:
            result = self.collection.find_one(
                sort=[("timestamp", -1)]
            )
            if result:
                self.logger.debug(f"Found latest results from {result.get('timestamp')}")
            else:
                self.logger.debug("No results found")
            return result
        except Exception as e:
            self.logger.error(f"Failed to fetch latest results: {str(e)}")
            raise

    def close(self):
        """Close the MongoDB connection."""
        self.logger.info("Closing MongoDB connection")
        try:
            self.client.close()
            self.logger.debug("Successfully closed MongoDB connection")
        except Exception as e:
            self.logger.error(f"Error closing MongoDB connection: {str(e)}")
            raise 