import os
from typing import Dict, Any
from telegram import <PERSON><PERSON>
from dotenv import load_dotenv
from logger import setup_logger

load_dotenv()

class TelegramClient:
    def __init__(self):
        self.logger = setup_logger('telegram_client')
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.channel_id = os.getenv('TELEGRAM_CHANNEL_ID')
        self.bot = Bot(token=self.bot_token)
        self.logger.info("Initialized Telegram client")

    async def send_token_update(self, token_data: Dict[str, Any]) -> None:
        """Send a formatted message about a token update."""
        self.logger.info(f"Sending token update for {token_data.get('token_bought_symbol', 'Unknown')}")
        try:
            message = self._format_token_message(token_data)
            await self.bot.send_message(
                chat_id=self.channel_id,
                text=message,
                parse_mode='HTML'
            )
            self.logger.debug(f"Successfully sent token update for {token_data.get('token_bought_symbol', 'Unknown')}")
        except Exception as e:
            self.logger.error(f"Failed to send token update: {str(e)}")
            raise

    def _format_token_message(self, token_data: Dict[str, Any]) -> str:
        """Format token data into a readable message."""
        self.logger.debug("Formatting token message")
        try:
            message = f"""
🔔 <b>New Token Trade Alert</b>

Trader: <code>{token_data.get('trader_id', 'Unknown')}</code>
Token: {token_data.get('token_bought_symbol', 'Unknown')}
Token Address: <code>{token_data.get('token_bought_mint_address', 'Unknown')}</code>

Amount: {token_data.get('total_token_bought', 'Unknown')} tokens
USD Spent: ${token_data.get('total_usd_spent', 'Unknown')}
Unique Traders: {token_data.get('unique_traders_per_token', 'Unknown')}

First Trade: {token_data.get('first_trade', 'Unknown')}
Last Trade: {token_data.get('last_trade', 'Unknown')}
            """.strip()
            self.logger.debug("Successfully formatted token message")
            return message
        except Exception as e:
            self.logger.error(f"Failed to format token message: {str(e)}")
            raise

    async def send_error_message(self, error_message: str) -> None:
        """Send an error message to the channel."""
        self.logger.info("Sending error message to Telegram")
        try:
            message = f"⚠️ <b>Error Alert</b>\n\n{error_message}"
            await self.bot.send_message(
                chat_id=self.channel_id,
                text=message,
                parse_mode='HTML'
            )
            self.logger.debug("Successfully sent error message")
        except Exception as e:
            self.logger.error(f"Failed to send error message: {str(e)}")
            raise

    async def send_status_update(self, message: str) -> None:
        """Send a general status update message."""
        self.logger.info(f"Sending status update: {message}")
        try:
            await self.bot.send_message(
                chat_id=self.channel_id,
                text=f"ℹ️ Status: {message}",
                parse_mode='HTML'
            )
            self.logger.debug("Successfully sent status update")
        except Exception as e:
            self.logger.error(f"Failed to send status update: {str(e)}")
            # Don't raise here, as status updates are less critical 