"""
💬 Message Formatter

Advanced message formatting and templating for notifications
with rich formatting, emoji support, and localization.
"""

import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
from enum import Enum

from ...config.logging_config import get_logger
from ...shared.types import SignalData, SignalType, SignalStrength

logger = get_logger(__name__)


class MessageStyle(Enum):
    """Message formatting styles"""
    MINIMAL = "minimal"
    STANDARD = "standard"
    DETAILED = "detailed"
    RICH = "rich"


class MessageFormatter:
    """
    💬 Message Formatter
    
    Provides advanced message formatting with:
    - Rich HTML and Markdown formatting
    - Emoji and visual indicators
    - Customizable templates
    - Localization support
    - Dynamic content adaptation
    """
    
    def __init__(self):
        self.logger = logger
        
        # Emoji mappings
        self.signal_emojis = {
            SignalType.BUY: "🟢",
            SignalType.SELL: "🔴", 
            SignalType.HOLD: "🟡"
        }
        
        self.strength_emojis = {
            SignalStrength.WEAK: "🟡",
            SignalStrength.MODERATE: "🟠",
            SignalStrength.STRONG: "🔴",
            SignalStrength.VERY_STRONG: "🚨"
        }
        
        self.trend_emojis = {
            "strong_uptrend": "📈🚀",
            "uptrend": "📈",
            "sideways": "➡️",
            "downtrend": "📉",
            "strong_downtrend": "📉💥"
        }
        
        # Number formatting
        self.price_precision = 6
        self.percentage_precision = 2
        self.usd_precision = 2
    
    def format_signal_alert(
        self,
        signal: SignalData,
        style: MessageStyle = MessageStyle.STANDARD,
        include_technical: bool = True,
        include_risk: bool = True
    ) -> str:
        """
        Format signal alert message
        
        Args:
            signal: Signal data
            style: Formatting style
            include_technical: Include technical analysis
            include_risk: Include risk assessment
            
        Returns:
            Formatted message string
        """
        try:
            if style == MessageStyle.MINIMAL:
                return self._format_signal_minimal(signal)
            elif style == MessageStyle.DETAILED:
                return self._format_signal_detailed(signal, include_technical, include_risk)
            elif style == MessageStyle.RICH:
                return self._format_signal_rich(signal, include_technical, include_risk)
            else:
                return self._format_signal_standard(signal, include_technical, include_risk)
                
        except Exception as e:
            self.logger.error(f"Error formatting signal alert: {str(e)}")
            return f"Signal Alert: {signal.signal_type.value} {signal.token_address}"
    
    def format_portfolio_update(
        self,
        portfolio_data: Dict[str, Any],
        style: MessageStyle = MessageStyle.STANDARD,
        include_positions: bool = True
    ) -> str:
        """Format portfolio update message"""
        try:
            if style == MessageStyle.MINIMAL:
                return self._format_portfolio_minimal(portfolio_data)
            elif style == MessageStyle.DETAILED:
                return self._format_portfolio_detailed(portfolio_data, include_positions)
            elif style == MessageStyle.RICH:
                return self._format_portfolio_rich(portfolio_data, include_positions)
            else:
                return self._format_portfolio_standard(portfolio_data, include_positions)
                
        except Exception as e:
            self.logger.error(f"Error formatting portfolio update: {str(e)}")
            return "Portfolio Update"
    
    def format_trade_notification(
        self,
        trade_data: Dict[str, Any],
        style: MessageStyle = MessageStyle.STANDARD
    ) -> str:
        """Format trade execution notification"""
        try:
            if style == MessageStyle.MINIMAL:
                return self._format_trade_minimal(trade_data)
            elif style == MessageStyle.DETAILED:
                return self._format_trade_detailed(trade_data)
            elif style == MessageStyle.RICH:
                return self._format_trade_rich(trade_data)
            else:
                return self._format_trade_standard(trade_data)
                
        except Exception as e:
            self.logger.error(f"Error formatting trade notification: {str(e)}")
            return "Trade Executed"
    
    def _format_signal_minimal(self, signal: SignalData) -> str:
        """Format minimal signal message"""
        type_emoji = self.signal_emojis.get(signal.signal_type, "⚪")
        strength_emoji = self.strength_emojis.get(signal.strength, "⚪")
        
        return f"""
{type_emoji} <b>{signal.signal_type.value}</b> {strength_emoji}
🪙 {self._format_token_address(signal.token_address)}
💰 ${self._format_price(signal.entry_price)} | {signal.confidence:.0%}
"""
    
    def _format_signal_standard(
        self,
        signal: SignalData,
        include_technical: bool = True,
        include_risk: bool = True
    ) -> str:
        """Format standard signal message"""
        type_emoji = self.signal_emojis.get(signal.signal_type, "⚪")
        strength_emoji = self.strength_emojis.get(signal.strength, "⚪")
        
        # Calculate risk/reward ratio
        if signal.stop_loss and signal.take_profit:
            risk = abs(signal.entry_price - signal.stop_loss)
            reward = abs(signal.take_profit - signal.entry_price)
            rr_ratio = reward / risk if risk > 0 else 0
        else:
            rr_ratio = 0
        
        message = f"""
{type_emoji} <b>{signal.signal_type.value} SIGNAL</b> {strength_emoji}

🪙 <b>Token:</b> <code>{self._format_token_address(signal.token_address)}</code>
💪 <b>Strength:</b> {signal.strength.value}
🎯 <b>Confidence:</b> {signal.confidence:.1%}
💰 <b>Entry:</b> ${self._format_price(signal.entry_price)}
📊 <b>Size:</b> ${self._format_usd(signal.position_size_usd)}

🛡️ <b>Risk Management:</b>
• Stop Loss: ${self._format_price(signal.stop_loss)} ({self._format_percentage_change(signal.stop_loss, signal.entry_price)})
• Take Profit: ${self._format_price(signal.take_profit)} ({self._format_percentage_change(signal.take_profit, signal.entry_price)})
• R/R Ratio: {rr_ratio:.1f}:1
"""

        if include_risk:
            risk_emoji = "🟢" if signal.risk_score < 0.3 else "🟡" if signal.risk_score < 0.7 else "🔴"
            message += f"• Risk Score: {risk_emoji} {signal.risk_score:.1%}\n"
        
        message += f"\n📝 <b>Analysis:</b>\n{signal.reasoning}\n"
        
        if include_technical and hasattr(signal, 'indicators') and signal.indicators:
            message += f"\n📊 <b>Technical:</b>\n"
            if signal.indicators.get('rsi'):
                message += f"• RSI: {signal.indicators['rsi']:.1f}\n"
            if signal.indicators.get('macd'):
                message += f"• MACD: {signal.indicators['macd']:.4f}\n"
        
        message += f"\n⏰ <b>Expires:</b> {self._format_datetime(signal.expires_at)}"
        
        return message.strip()
    
    def _format_signal_detailed(
        self,
        signal: SignalData,
        include_technical: bool = True,
        include_risk: bool = True
    ) -> str:
        """Format detailed signal message"""
        # Start with standard format
        message = self._format_signal_standard(signal, include_technical, include_risk)
        
        # Add detailed technical analysis
        if include_technical and hasattr(signal, 'indicators') and signal.indicators:
            message += f"\n\n🔍 <b>Detailed Analysis:</b>\n"
            
            indicators = signal.indicators
            if indicators.get('bb_upper') and indicators.get('bb_lower'):
                bb_position = self._calculate_bb_position(
                    signal.entry_price,
                    indicators['bb_upper'],
                    indicators['bb_lower']
                )
                message += f"• Bollinger Position: {bb_position:.1%}\n"
            
            if indicators.get('volume_ratio'):
                volume_status = "High" if indicators['volume_ratio'] > 2 else "Normal" if indicators['volume_ratio'] > 0.5 else "Low"
                message += f"• Volume: {volume_status} ({indicators['volume_ratio']:.1f}x)\n"
            
            if indicators.get('trend_direction'):
                trend_emoji = self.trend_emojis.get(indicators['trend_direction'], "➡️")
                message += f"• Trend: {trend_emoji} {indicators['trend_direction'].replace('_', ' ').title()}\n"
            
            if indicators.get('momentum_score'):
                momentum_emoji = "🚀" if indicators['momentum_score'] > 70 else "📈" if indicators['momentum_score'] > 50 else "📉"
                message += f"• Momentum: {momentum_emoji} {indicators['momentum_score']:.0f}/100\n"
        
        return message
    
    def _format_signal_rich(
        self,
        signal: SignalData,
        include_technical: bool = True,
        include_risk: bool = True
    ) -> str:
        """Format rich signal message with advanced formatting"""
        # Use detailed format as base
        message = self._format_signal_detailed(signal, include_technical, include_risk)
        
        # Add visual elements
        message += f"\n\n{'═' * 30}\n"
        message += f"🎯 <b>QUICK ACTIONS</b>\n"
        message += f"{'═' * 30}\n"
        message += f"📊 View Chart | 💼 Execute Trade\n"
        message += f"🔍 Risk Analysis | ⭐ Save Signal"
        
        return message
    
    def _format_portfolio_standard(
        self,
        portfolio_data: Dict[str, Any],
        include_positions: bool = True
    ) -> str:
        """Format standard portfolio message"""
        portfolio = portfolio_data["portfolio"]
        value_breakdown = portfolio_data.get("value_breakdown", {})
        
        # Performance indicators
        pnl_emoji = "🟢" if portfolio["total_pnl"] > 0 else "🔴" if portfolio["total_pnl"] < 0 else "⚪"
        performance_emoji = "📈" if portfolio["total_return_percent"] > 0 else "📉" if portfolio["total_return_percent"] < 0 else "➡️"
        
        message = f"""
💼 <b>Portfolio: {portfolio['name']}</b>

💰 <b>Balance Overview:</b>
• Total Value: ${self._format_usd(portfolio['total_value'])}
• Cash: ${self._format_usd(value_breakdown.get('cash_balance', 0))}
• Positions: ${self._format_usd(value_breakdown.get('position_value', 0))}

{performance_emoji} <b>Performance:</b>
{pnl_emoji} P&L: ${self._format_usd(portfolio['total_pnl'])} ({portfolio['total_return_percent']:+.2f}%)
• Realized: ${self._format_usd(value_breakdown.get('realized_pnl', 0))}
• Unrealized: ${self._format_usd(value_breakdown.get('unrealized_pnl', 0))}

📊 <b>Statistics:</b>
• Trades: {portfolio['total_trades']} | Win Rate: {portfolio['win_rate']:.1f}%
• Positions: {portfolio['position_count']} active
"""
        
        if include_positions and portfolio_data.get("positions"):
            message += f"\n💼 <b>Top Positions:</b>\n"
            positions = portfolio_data["positions"][:3]  # Show top 3
            for pos in positions:
                pnl_emoji = "🟢" if pos["unrealized_pnl"] > 0 else "🔴"
                message += f"• {self._format_token_address(pos['token_address'])}: {pnl_emoji} {pos['unrealized_pnl_percent']:+.1f}%\n"
        
        message += f"\n🕐 <b>Updated:</b> {self._format_datetime(portfolio['last_updated_at'])}"
        
        return message.strip()
    
    def _format_trade_standard(self, trade_data: Dict[str, Any]) -> str:
        """Format standard trade message"""
        side_emoji = "🟢" if trade_data["side"] == "BUY" else "🔴"
        status_emoji = "✅" if trade_data.get("status") == "EXECUTED" else "⏳"
        
        message = f"""
{status_emoji} <b>Trade {trade_data.get('status', 'EXECUTED')}</b>

{side_emoji} <b>{trade_data['side']}</b> {self._format_token_address(trade_data['token_address'])}
💰 <b>Quantity:</b> {self._format_number(trade_data['quantity'])}
💵 <b>Price:</b> ${self._format_price(trade_data['price'])}
💸 <b>Value:</b> ${self._format_usd(trade_data['value_usd'])}
🏦 <b>Fees:</b> ${self._format_usd(trade_data['fees'])}
"""
        
        if trade_data.get("pnl") is not None:
            pnl_emoji = "🟢" if trade_data["pnl"] > 0 else "🔴"
            message += f"{pnl_emoji} <b>P&L:</b> ${self._format_usd(trade_data['pnl'])}\n"
        
        message += f"\n🕐 <b>Executed:</b> {self._format_datetime(trade_data['execution_time'])}"
        
        return message.strip()
    
    # Minimal format methods
    def _format_portfolio_minimal(self, portfolio_data: Dict[str, Any]) -> str:
        """Format minimal portfolio message"""
        portfolio = portfolio_data["portfolio"]
        pnl_emoji = "🟢" if portfolio["total_pnl"] > 0 else "🔴"
        
        return f"""
💼 {portfolio['name']}
{pnl_emoji} ${self._format_usd(portfolio['total_value'])} ({portfolio['total_return_percent']:+.1f}%)
"""
    
    def _format_trade_minimal(self, trade_data: Dict[str, Any]) -> str:
        """Format minimal trade message"""
        side_emoji = "🟢" if trade_data["side"] == "BUY" else "🔴"
        
        return f"""
{side_emoji} {trade_data['side']} {self._format_token_address(trade_data['token_address'])}
💰 ${self._format_usd(trade_data['value_usd'])} @ ${self._format_price(trade_data['price'])}
"""
    
    # Detailed format methods (similar to standard but with more info)
    def _format_portfolio_detailed(self, portfolio_data: Dict[str, Any], include_positions: bool) -> str:
        """Format detailed portfolio message"""
        return self._format_portfolio_standard(portfolio_data, include_positions)
    
    def _format_trade_detailed(self, trade_data: Dict[str, Any]) -> str:
        """Format detailed trade message"""
        return self._format_trade_standard(trade_data)
    
    # Rich format methods (with visual enhancements)
    def _format_portfolio_rich(self, portfolio_data: Dict[str, Any], include_positions: bool) -> str:
        """Format rich portfolio message"""
        message = self._format_portfolio_standard(portfolio_data, include_positions)
        message += f"\n\n{'═' * 25}\n📊 View Full Report | ⚙️ Settings"
        return message
    
    def _format_trade_rich(self, trade_data: Dict[str, Any]) -> str:
        """Format rich trade message"""
        message = self._format_trade_standard(trade_data)
        message += f"\n\n{'─' * 20}\n📈 View Chart | 📋 Trade History"
        return message
    
    # Utility formatting methods
    def _format_token_address(self, address: str) -> str:
        """Format token address for display"""
        if len(address) > 16:
            return f"{address[:8]}...{address[-8:]}"
        return address
    
    def _format_price(self, price: Union[Decimal, float]) -> str:
        """Format price with appropriate precision"""
        price_float = float(price)
        if price_float >= 1:
            return f"{price_float:,.{min(4, self.price_precision)}f}"
        else:
            return f"{price_float:.{self.price_precision}f}"
    
    def _format_usd(self, amount: Union[Decimal, float]) -> str:
        """Format USD amount"""
        amount_float = float(amount)
        if abs(amount_float) >= 1000000:
            return f"{amount_float/1000000:.1f}M"
        elif abs(amount_float) >= 1000:
            return f"{amount_float/1000:.1f}K"
        else:
            return f"{amount_float:.{self.usd_precision}f}"
    
    def _format_number(self, number: Union[Decimal, float]) -> str:
        """Format generic number"""
        number_float = float(number)
        if number_float >= 1000000:
            return f"{number_float/1000000:.2f}M"
        elif number_float >= 1000:
            return f"{number_float/1000:.2f}K"
        else:
            return f"{number_float:.6f}"
    
    def _format_percentage_change(self, new_price: Decimal, old_price: Decimal) -> str:
        """Format percentage change"""
        if old_price == 0:
            return "N/A"
        
        change = ((new_price - old_price) / old_price) * 100
        return f"{change:+.1f}%"
    
    def _format_datetime(self, dt: Union[datetime, str]) -> str:
        """Format datetime for display"""
        if isinstance(dt, str):
            try:
                dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
            except:
                return dt[:19] + " UTC"
        
        return dt.strftime("%m/%d %H:%M UTC")
    
    def _calculate_bb_position(self, price: Decimal, bb_upper: float, bb_lower: float) -> float:
        """Calculate position within Bollinger Bands"""
        if bb_upper == bb_lower:
            return 0.5
        
        return (float(price) - bb_lower) / (bb_upper - bb_lower)
