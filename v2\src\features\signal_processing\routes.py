"""
🛣️ Signal Processing Routes

FastAPI routes for signal processing endpoints including signal generation,
validation, and management following V2 architecture patterns.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, Query, Path
from pydantic import BaseModel, Field

from ...config.logging_config import get_logger
from ...shared.types import APIResponse, SignalType, SignalStrength
from ...database.models import Signal
from ...features.data_pipeline import DataAggregator

from .signal_generator import SignalGenerator
from .signal_validator import SignalValidator
from .technical_analyzer import TechnicalAnalyzer
from .risk_assessor import RiskAssessor

logger = get_logger(__name__)
router = APIRouter()

# Initialize services
signal_generator = SignalGenerator()
signal_validator = SignalValidator()
technical_analyzer = TechnicalAnalyzer()
risk_assessor = RiskAssessor()
data_aggregator = DataAggregator()


# Request/Response Models
class GenerateSignalRequest(BaseModel):
    """Request model for signal generation"""
    token_address: str = Field(..., description="Token contract address")
    force_generation: bool = Field(default=False, description="Force signal generation even if recent signals exist")


class SignalResponse(BaseModel):
    """Response model for signal data"""
    id: str
    token_address: str
    signal_type: SignalType
    strength: SignalStrength
    confidence: float
    entry_price: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    position_size_usd: float
    reasoning: str
    risk_score: float
    expires_at: datetime
    created_at: datetime
    is_active: bool


class TechnicalAnalysisResponse(BaseModel):
    """Response model for technical analysis"""
    token_address: str
    rsi: Optional[float]
    macd: Optional[float]
    macd_signal: Optional[float]
    bollinger_upper: Optional[float]
    bollinger_lower: Optional[float]
    volume_ratio: Optional[float]
    trend_direction: Optional[str]
    momentum_score: Optional[float]
    analysis_timestamp: datetime


class RiskAssessmentResponse(BaseModel):
    """Response model for risk assessment"""
    token_address: str
    overall_risk_score: float
    risk_level: str
    liquidity_risk: float
    volatility_risk: float
    market_risk: float
    risk_factors: List[str]
    assessment_timestamp: datetime


@router.post("/generate", response_model=APIResponse)
async def generate_signal(request: GenerateSignalRequest):
    """
    Generate trading signal for a token
    
    - **token_address**: Token contract address
    - **force_generation**: Force generation even if recent signals exist
    """
    try:
        logger.info(f"Generating signal for token: {request.token_address}")
        
        # Get market data
        market_data = await data_aggregator.get_token_data(request.token_address)
        if not market_data:
            raise HTTPException(
                status_code=404,
                detail=f"Market data not found for token {request.token_address}"
            )
        
        # Get price history
        price_history = await data_aggregator.get_price_history(
            request.token_address,
            hours=168  # 7 days
        )
        
        if len(price_history) < 24:
            raise HTTPException(
                status_code=400,
                detail="Insufficient price history for signal generation"
            )
        
        # Generate signal
        signal_data = await signal_generator.generate_signal(
            request.token_address,
            price_history,
            market_data
        )
        
        if not signal_data:
            return APIResponse(
                success=True,
                message="No signal generated - conditions not met",
                data=None
            )
        
        # Validate signal
        validation_result = await signal_validator.validate_signal(
            signal_data,
            market_data,
            price_history
        )
        
        if not validation_result.is_valid:
            return APIResponse(
                success=False,
                message="Signal validation failed",
                data={
                    "rejection_reasons": validation_result.rejection_reasons,
                    "warnings": validation_result.warnings
                }
            )
        
        # Adjust confidence based on validation
        adjusted_confidence = min(1.0, max(0.0, 
            signal_data.confidence + validation_result.confidence_adjustment
        ))
        
        # Update signal in database with adjusted confidence
        signal_doc = await Signal.find_one({"id": signal_data.id})
        if signal_doc:
            signal_doc.confidence = adjusted_confidence
            await signal_doc.save()
        
        response_data = SignalResponse(
            id=signal_data.id,
            token_address=signal_data.token_address,
            signal_type=signal_data.signal_type,
            strength=signal_data.strength,
            confidence=adjusted_confidence,
            entry_price=float(signal_data.entry_price),
            stop_loss=float(signal_data.stop_loss) if signal_data.stop_loss else None,
            take_profit=float(signal_data.take_profit) if signal_data.take_profit else None,
            position_size_usd=float(signal_data.position_size_usd),
            reasoning=signal_data.reasoning,
            risk_score=signal_data.risk_score,
            expires_at=signal_data.expires_at,
            created_at=signal_data.created_at,
            is_active=True
        )
        
        return APIResponse(
            success=True,
            message="Signal generated successfully",
            data=response_data.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating signal: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/active", response_model=APIResponse)
async def get_active_signals(
    token_address: Optional[str] = Query(None, description="Filter by token address"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of signals to return")
):
    """Get active trading signals"""
    try:
        query = {"is_active": True, "expires_at": {"$gt": datetime.utcnow()}}
        if token_address:
            query["token_address"] = token_address
        
        signals = await Signal.find(query).sort("-created_at").limit(limit).to_list()
        
        response_signals = []
        for signal in signals:
            response_signals.append(SignalResponse(
                id=str(signal.id),
                token_address=signal.token_address,
                signal_type=signal.signal_type,
                strength=signal.strength,
                confidence=signal.confidence,
                entry_price=float(signal.entry_price),
                stop_loss=float(signal.stop_loss) if signal.stop_loss else None,
                take_profit=float(signal.take_profit) if signal.take_profit else None,
                position_size_usd=float(signal.position_size_usd),
                reasoning=signal.reasoning,
                risk_score=signal.risk_score,
                expires_at=signal.expires_at,
                created_at=signal.created_at,
                is_active=signal.is_active
            ))
        
        return APIResponse(
            success=True,
            message=f"Retrieved {len(response_signals)} active signals",
            data=response_signals
        )
        
    except Exception as e:
        logger.error(f"Error retrieving active signals: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/technical-analysis/{token_address}", response_model=APIResponse)
async def get_technical_analysis(
    token_address: str = Path(..., description="Token contract address")
):
    """Get technical analysis for a token"""
    try:
        # Get price history
        price_history = await data_aggregator.get_price_history(
            token_address,
            hours=168  # 7 days
        )
        
        if len(price_history) < 24:
            raise HTTPException(
                status_code=400,
                detail="Insufficient price history for technical analysis"
            )
        
        # Perform technical analysis
        indicators = await technical_analyzer.analyze_token(price_history)
        
        response_data = TechnicalAnalysisResponse(
            token_address=token_address,
            rsi=indicators.rsi,
            macd=indicators.macd,
            macd_signal=indicators.macd_signal,
            bollinger_upper=indicators.bb_upper,
            bollinger_lower=indicators.bb_lower,
            volume_ratio=indicators.volume_ratio,
            trend_direction=indicators.trend_direction,
            momentum_score=indicators.momentum_score,
            analysis_timestamp=datetime.utcnow()
        )
        
        return APIResponse(
            success=True,
            message="Technical analysis completed",
            data=response_data.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing technical analysis: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/risk-assessment/{token_address}", response_model=APIResponse)
async def get_risk_assessment(
    token_address: str = Path(..., description="Token contract address")
):
    """Get risk assessment for a token"""
    try:
        # Get market data
        market_data = await data_aggregator.get_token_data(token_address)
        if not market_data:
            raise HTTPException(
                status_code=404,
                detail=f"Market data not found for token {token_address}"
            )
        
        # Get price history
        price_history = await data_aggregator.get_price_history(
            token_address,
            hours=168  # 7 days
        )
        
        # Perform risk assessment
        risk_assessment = await risk_assessor.assess_token_risk(
            token_address,
            market_data,
            price_history
        )
        
        response_data = RiskAssessmentResponse(
            token_address=token_address,
            overall_risk_score=risk_assessment['overall_risk_score'],
            risk_level=risk_assessment['risk_level'],
            liquidity_risk=risk_assessment['liquidity_risk'],
            volatility_risk=risk_assessment['volatility_risk'],
            market_risk=risk_assessment['market_risk'],
            risk_factors=risk_assessment['risk_factors'],
            assessment_timestamp=risk_assessment['assessment_timestamp']
        )
        
        return APIResponse(
            success=True,
            message="Risk assessment completed",
            data=response_data.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing risk assessment: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{signal_id}", response_model=APIResponse)
async def get_signal(
    signal_id: str = Path(..., description="Signal ID")
):
    """Get specific signal by ID"""
    try:
        signal = await Signal.find_one({"id": signal_id})
        if not signal:
            raise HTTPException(status_code=404, detail="Signal not found")
        
        response_data = SignalResponse(
            id=str(signal.id),
            token_address=signal.token_address,
            signal_type=signal.signal_type,
            strength=signal.strength,
            confidence=signal.confidence,
            entry_price=float(signal.entry_price),
            stop_loss=float(signal.stop_loss) if signal.stop_loss else None,
            take_profit=float(signal.take_profit) if signal.take_profit else None,
            position_size_usd=float(signal.position_size_usd),
            reasoning=signal.reasoning,
            risk_score=signal.risk_score,
            expires_at=signal.expires_at,
            created_at=signal.created_at,
            is_active=signal.is_active
        )
        
        return APIResponse(
            success=True,
            message="Signal retrieved successfully",
            data=response_data.dict()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving signal: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/expire-old", response_model=APIResponse)
async def expire_old_signals():
    """Expire old signals that have passed their expiration time"""
    try:
        expired_count = await signal_generator.expire_old_signals()
        
        return APIResponse(
            success=True,
            message=f"Expired {expired_count} old signals",
            data={"expired_count": expired_count}
        )
        
    except Exception as e:
        logger.error(f"Error expiring old signals: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
