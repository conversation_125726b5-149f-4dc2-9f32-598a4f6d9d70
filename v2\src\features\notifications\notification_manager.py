"""
📢 Notification Manager

Centralized notification orchestration with multi-channel support,
user preferences, and intelligent notification filtering.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass
from decimal import Decimal

from ...config.logging_config import get_logger
from ...shared.types import SignalData, SignalType, SignalStrength
from ...database.models import Signal, Portfolio, Trade, User

from .telegram_client import TelegramClient
from .subscription_manager import SubscriptionManager

logger = get_logger(__name__)


class NotificationType(Enum):
    """Notification types"""
    SIGNAL_ALERT = "signal_alert"
    PORTFOLIO_UPDATE = "portfolio_update"
    TRADE_EXECUTION = "trade_execution"
    RISK_WARNING = "risk_warning"
    SYSTEM_ALERT = "system_alert"
    PERFORMANCE_REPORT = "performance_report"


class NotificationPriority(Enum):
    """Notification priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class NotificationRequest:
    """Notification request data structure"""
    type: NotificationType
    priority: NotificationPriority
    recipients: List[str]  # User IDs or chat IDs
    data: Dict[str, Any]
    scheduled_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    channels: List[str] = None  # telegram, email, etc.


class NotificationManager:
    """
    📢 Notification Manager
    
    Orchestrates notifications across multiple channels with:
    - Intelligent filtering and deduplication
    - User preference management
    - Rate limiting and throttling
    - Priority-based delivery
    - Delivery tracking and analytics
    """
    
    def __init__(self):
        self.logger = logger
        self.telegram_client = TelegramClient()
        self.subscription_manager = SubscriptionManager()
        
        # Notification tracking
        self.sent_notifications = {}  # Track sent notifications for deduplication
        self.rate_limits = {}  # Track rate limits per user
        self.failed_deliveries = {}  # Track failed deliveries for retry
        
        # Configuration
        self.max_notifications_per_hour = 20
        self.deduplication_window_minutes = 30
        self.retry_attempts = 3
        self.retry_delay_seconds = 60
    
    async def send_signal_alert(
        self,
        signal: SignalData,
        recipients: Optional[List[str]] = None,
        priority: NotificationPriority = NotificationPriority.HIGH
    ) -> Dict[str, Any]:
        """
        Send signal alert notification
        
        Args:
            signal: Signal data
            recipients: Specific recipients (if None, use subscribers)
            priority: Notification priority
            
        Returns:
            Delivery results
        """
        try:
            self.logger.info(f"Sending signal alert for {signal.token_address}")
            
            # Get recipients if not specified
            if recipients is None:
                recipients = await self.subscription_manager.get_signal_subscribers()
            
            # Filter recipients based on preferences
            filtered_recipients = await self._filter_recipients_for_signals(
                recipients, signal
            )
            
            # Check for duplicate notifications
            notification_key = f"signal_{signal.id}"
            if self._is_duplicate_notification(notification_key):
                self.logger.info(f"Skipping duplicate signal notification: {signal.id}")
                return {"status": "skipped", "reason": "duplicate"}
            
            # Create notification request
            request = NotificationRequest(
                type=NotificationType.SIGNAL_ALERT,
                priority=priority,
                recipients=filtered_recipients,
                data={
                    "signal": signal,
                    "include_actions": True
                },
                expires_at=signal.expires_at,
                channels=["telegram"]
            )
            
            # Send notifications
            results = await self._send_notification(request)
            
            # Mark as sent for deduplication
            self._mark_notification_sent(notification_key)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending signal alert: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def send_portfolio_update(
        self,
        portfolio_data: Dict[str, Any],
        recipients: Optional[List[str]] = None,
        priority: NotificationPriority = NotificationPriority.MEDIUM
    ) -> Dict[str, Any]:
        """Send portfolio update notification"""
        try:
            portfolio_id = portfolio_data["portfolio"]["id"]
            self.logger.info(f"Sending portfolio update for {portfolio_id}")
            
            # Get recipients if not specified
            if recipients is None:
                recipients = await self.subscription_manager.get_portfolio_subscribers(portfolio_id)
            
            # Filter recipients based on preferences
            filtered_recipients = await self._filter_recipients_for_portfolio(
                recipients, portfolio_data
            )
            
            # Check rate limits for portfolio updates
            if not self._check_portfolio_update_rate_limit(portfolio_id):
                self.logger.info(f"Rate limit exceeded for portfolio updates: {portfolio_id}")
                return {"status": "rate_limited"}
            
            # Create notification request
            request = NotificationRequest(
                type=NotificationType.PORTFOLIO_UPDATE,
                priority=priority,
                recipients=filtered_recipients,
                data={"portfolio_data": portfolio_data},
                channels=["telegram"]
            )
            
            # Send notifications
            results = await self._send_notification(request)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending portfolio update: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def send_trade_notification(
        self,
        trade_data: Dict[str, Any],
        recipients: Optional[List[str]] = None,
        priority: NotificationPriority = NotificationPriority.HIGH
    ) -> Dict[str, Any]:
        """Send trade execution notification"""
        try:
            trade_id = trade_data["trade_id"]
            self.logger.info(f"Sending trade notification for {trade_id}")
            
            # Get recipients if not specified
            if recipients is None:
                portfolio_id = trade_data.get("portfolio_id")
                if portfolio_id:
                    recipients = await self.subscription_manager.get_trade_subscribers(portfolio_id)
                else:
                    recipients = await self.subscription_manager.get_trade_subscribers()
            
            # Create notification request
            request = NotificationRequest(
                type=NotificationType.TRADE_EXECUTION,
                priority=priority,
                recipients=recipients,
                data={"trade_data": trade_data},
                channels=["telegram"]
            )
            
            # Send notifications
            results = await self._send_notification(request)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending trade notification: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def send_risk_warning(
        self,
        warning_data: Dict[str, Any],
        recipients: List[str],
        priority: NotificationPriority = NotificationPriority.CRITICAL
    ) -> Dict[str, Any]:
        """Send risk warning notification"""
        try:
            self.logger.warning(f"Sending risk warning: {warning_data.get('type', 'unknown')}")
            
            # Create notification request
            request = NotificationRequest(
                type=NotificationType.RISK_WARNING,
                priority=priority,
                recipients=recipients,
                data={"warning_data": warning_data},
                channels=["telegram"]
            )
            
            # Send notifications immediately (bypass rate limits for critical warnings)
            results = await self._send_notification(request, bypass_rate_limit=True)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending risk warning: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def send_performance_report(
        self,
        report_data: Dict[str, Any],
        recipients: Optional[List[str]] = None,
        priority: NotificationPriority = NotificationPriority.LOW
    ) -> Dict[str, Any]:
        """Send periodic performance report"""
        try:
            self.logger.info("Sending performance report")
            
            # Get recipients if not specified
            if recipients is None:
                recipients = await self.subscription_manager.get_report_subscribers()
            
            # Create notification request
            request = NotificationRequest(
                type=NotificationType.PERFORMANCE_REPORT,
                priority=priority,
                recipients=recipients,
                data={"report_data": report_data},
                channels=["telegram"]
            )
            
            # Send notifications
            results = await self._send_notification(request)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending performance report: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _send_notification(
        self,
        request: NotificationRequest,
        bypass_rate_limit: bool = False
    ) -> Dict[str, Any]:
        """Send notification to all recipients"""
        try:
            results = {
                "total_recipients": len(request.recipients),
                "successful_deliveries": 0,
                "failed_deliveries": 0,
                "rate_limited": 0,
                "details": []
            }
            
            # Send to each recipient
            for recipient in request.recipients:
                try:
                    # Check rate limits
                    if not bypass_rate_limit and not self._check_rate_limit(recipient):
                        results["rate_limited"] += 1
                        results["details"].append({
                            "recipient": recipient,
                            "status": "rate_limited"
                        })
                        continue
                    
                    # Send via appropriate channel
                    delivery_result = None
                    if "telegram" in (request.channels or ["telegram"]):
                        delivery_result = await self._send_telegram_notification(
                            recipient, request
                        )
                    
                    if delivery_result:
                        results["successful_deliveries"] += 1
                        results["details"].append({
                            "recipient": recipient,
                            "status": "delivered",
                            "channel": "telegram"
                        })
                    else:
                        results["failed_deliveries"] += 1
                        results["details"].append({
                            "recipient": recipient,
                            "status": "failed",
                            "channel": "telegram"
                        })
                    
                    # Update rate limit tracking
                    if not bypass_rate_limit:
                        self._update_rate_limit(recipient)
                    
                except Exception as e:
                    self.logger.error(f"Error sending to {recipient}: {str(e)}")
                    results["failed_deliveries"] += 1
                    results["details"].append({
                        "recipient": recipient,
                        "status": "error",
                        "error": str(e)
                    })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in _send_notification: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _send_telegram_notification(
        self,
        chat_id: str,
        request: NotificationRequest
    ) -> Optional[Dict[str, Any]]:
        """Send notification via Telegram"""
        try:
            async with self.telegram_client as client:
                if request.type == NotificationType.SIGNAL_ALERT:
                    signal = request.data["signal"]
                    include_actions = request.data.get("include_actions", True)
                    return await client.send_signal_alert(chat_id, signal, include_actions)
                
                elif request.type == NotificationType.PORTFOLIO_UPDATE:
                    portfolio_data = request.data["portfolio_data"]
                    return await client.send_portfolio_update(chat_id, portfolio_data)
                
                elif request.type == NotificationType.TRADE_EXECUTION:
                    trade_data = request.data["trade_data"]
                    return await client.send_trade_notification(chat_id, trade_data)
                
                elif request.type == NotificationType.RISK_WARNING:
                    warning_data = request.data["warning_data"]
                    message = self._format_risk_warning(warning_data)
                    return await client.send_message(chat_id, message)
                
                elif request.type == NotificationType.PERFORMANCE_REPORT:
                    report_data = request.data["report_data"]
                    message = self._format_performance_report(report_data)
                    return await client.send_message(chat_id, message)
                
                else:
                    self.logger.warning(f"Unknown notification type: {request.type}")
                    return None
            
        except Exception as e:
            self.logger.error(f"Error sending Telegram notification: {str(e)}")
            return None
    
    async def _filter_recipients_for_signals(
        self,
        recipients: List[str],
        signal: SignalData
    ) -> List[str]:
        """Filter recipients based on signal preferences"""
        try:
            filtered = []
            
            for recipient in recipients:
                # Check user preferences
                preferences = await self.subscription_manager.get_user_preferences(recipient)
                
                # Check if user wants this type of signal
                if not preferences.get("signal_alerts", True):
                    continue
                
                # Check signal strength filter
                min_strength = preferences.get("min_signal_strength", "WEAK")
                if signal.strength.value < min_strength:
                    continue
                
                # Check confidence filter
                min_confidence = preferences.get("min_confidence", 0.0)
                if signal.confidence < min_confidence:
                    continue
                
                # Check token filters
                blocked_tokens = preferences.get("blocked_tokens", [])
                if signal.token_address in blocked_tokens:
                    continue
                
                filtered.append(recipient)
            
            return filtered
            
        except Exception as e:
            self.logger.error(f"Error filtering signal recipients: {str(e)}")
            return recipients  # Return all if filtering fails
    
    async def _filter_recipients_for_portfolio(
        self,
        recipients: List[str],
        portfolio_data: Dict[str, Any]
    ) -> List[str]:
        """Filter recipients based on portfolio preferences"""
        try:
            filtered = []
            
            for recipient in recipients:
                preferences = await self.subscription_manager.get_user_preferences(recipient)
                
                # Check if user wants portfolio updates
                if not preferences.get("portfolio_updates", True):
                    continue
                
                # Check update frequency
                last_update = preferences.get("last_portfolio_update")
                min_interval = preferences.get("portfolio_update_interval_hours", 1)
                
                if last_update:
                    time_since_last = (datetime.utcnow() - last_update).total_seconds() / 3600
                    if time_since_last < min_interval:
                        continue
                
                filtered.append(recipient)
            
            return filtered
            
        except Exception as e:
            self.logger.error(f"Error filtering portfolio recipients: {str(e)}")
            return recipients
    
    def _is_duplicate_notification(self, notification_key: str) -> bool:
        """Check if notification is a duplicate"""
        current_time = datetime.utcnow()
        
        if notification_key in self.sent_notifications:
            last_sent = self.sent_notifications[notification_key]
            time_diff = (current_time - last_sent).total_seconds() / 60
            
            return time_diff < self.deduplication_window_minutes
        
        return False
    
    def _mark_notification_sent(self, notification_key: str) -> None:
        """Mark notification as sent for deduplication"""
        self.sent_notifications[notification_key] = datetime.utcnow()
        
        # Clean up old entries
        cutoff_time = datetime.utcnow() - timedelta(minutes=self.deduplication_window_minutes * 2)
        self.sent_notifications = {
            k: v for k, v in self.sent_notifications.items()
            if v > cutoff_time
        }
    
    def _check_rate_limit(self, recipient: str) -> bool:
        """Check if recipient is within rate limits"""
        current_time = datetime.utcnow()
        
        if recipient not in self.rate_limits:
            self.rate_limits[recipient] = []
        
        # Clean up old entries
        cutoff_time = current_time - timedelta(hours=1)
        self.rate_limits[recipient] = [
            timestamp for timestamp in self.rate_limits[recipient]
            if timestamp > cutoff_time
        ]
        
        # Check if under limit
        return len(self.rate_limits[recipient]) < self.max_notifications_per_hour
    
    def _update_rate_limit(self, recipient: str) -> None:
        """Update rate limit tracking for recipient"""
        if recipient not in self.rate_limits:
            self.rate_limits[recipient] = []
        
        self.rate_limits[recipient].append(datetime.utcnow())
    
    def _check_portfolio_update_rate_limit(self, portfolio_id: str) -> bool:
        """Check rate limit for portfolio updates"""
        # Allow max 1 portfolio update per 15 minutes
        rate_limit_key = f"portfolio_{portfolio_id}"
        
        if rate_limit_key in self.sent_notifications:
            last_sent = self.sent_notifications[rate_limit_key]
            time_diff = (datetime.utcnow() - last_sent).total_seconds() / 60
            
            return time_diff >= 15  # 15 minutes
        
        return True
    
    def _format_risk_warning(self, warning_data: Dict[str, Any]) -> str:
        """Format risk warning message"""
        warning_type = warning_data.get("type", "Unknown")
        message = warning_data.get("message", "Risk warning")
        severity = warning_data.get("severity", "medium")
        
        emoji = "🚨" if severity == "critical" else "⚠️"
        
        return f"""
{emoji} <b>RISK WARNING</b> {emoji}

<b>Type:</b> {warning_type}
<b>Severity:</b> {severity.upper()}

{message}

Please review your positions and risk management settings.
"""
    
    def _format_performance_report(self, report_data: Dict[str, Any]) -> str:
        """Format performance report message"""
        period = report_data.get("period", "Daily")
        
        return f"""
📊 <b>{period} Performance Report</b>

{report_data.get("summary", "Performance summary not available")}

📈 <b>Key Metrics:</b>
{report_data.get("metrics", "Metrics not available")}

Use /portfolio for detailed analysis.
"""
