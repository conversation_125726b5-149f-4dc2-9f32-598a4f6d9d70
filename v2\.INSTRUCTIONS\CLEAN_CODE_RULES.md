/*
===========================
🔥 CLEAN CODE INSTRUCTION SET FOR AI-GENERATED CODE
===========================

📁 1. FILE & FOLDER STRUCTURE
- Each file must contain only ONE responsibility (single class, function, or role).
- Use feature-based folder structure (not type-based).
  Example (correct):
    src/
      features/
        auth/
          controller.js
          service.js
          model.js
          routes.js
      shared/
        utils/
        constants/
- Keep files small and logically separated.

🔧 2. FUNCTION & CLASS RULES
- No function should exceed **40 lines**.
- Maximum **3 nested levels** inside functions.
- Break down large functions into smaller, reusable ones.
- Classes must follow Single Responsibility Principle (SRP).
- Prefer pure functions without side-effects when possible.

🧠 3. SEPARATION OF CONCERNS
- Separate project into logical layers:
  - controllers: handle requests
  - services: handle business logic
  - models: handle data
  - utils/helpers: shared utilities
- No cross-layer logic mixing.
- Use interface/abstraction where needed.

📦 4. MODULARITY
- Use `import/export` to keep modules independent and testable.
- Avoid tight coupling. No file should depend on more than 2–3 others.
- Code should be structured in a scalable, modular way.

✍️ 5. NAMING CONVENTIONS
- Use clear, descriptive names. Avoid vague names like `data`, `temp`, `foo`.
  ❌ `getData()` → ✅ `fetchUserProfile()`
- Use consistent casing:
  - camelCase → variables and functions
  - PascalCase → classes and components
  - UPPER_SNAKE_CASE → constants

📐 6. COMMENTS & DOCUMENTATION
- Every function must have a clear docstring or JSDoc-style comment.
- Prefer self-explanatory code over excessive comments.
- If using a non-trivial algorithm, briefly describe it at the start of the function.

🧪 7. TESTABILITY & SCALABILITY
- All services/modules must be independently testable.
- Use dependency injection where necessary.
- Avoid hardcoded values; extract them to config/constants.

🎯 8. CONFIGURATION
- All dynamic or environment-specific settings (URLs, keys, limits) must go in:
  - `config.js` / `.env` / `settings.py` / etc.
- No hardcoded values inside logic.

🔄 9. REUSABILITY
- Never repeat code. If two blocks are similar, abstract into a reusable function.
- Create shared modules: `utils`, `hooks`, `helpers`, etc.

🧹 10. CLEANUP & PRODUCTION READINESS
- No commented-out, unused or debug code.
- No `console.log()` in production; use a proper logger instead.
- Delete dead code and obsolete files regularly.

===========================
📌 TL;DR FOR AI PROMPTING:
"Generate clean, modular code with max 40-line functions, clear separation of concerns, no hardcoding, and a well-organized folder structure. All code must be readable, scalable, and production-ready."
===========================
*/
