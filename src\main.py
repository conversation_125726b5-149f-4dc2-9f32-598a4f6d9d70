import asyncio
import os
from datetime import datetime
from dotenv import load_dotenv
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from dune_client import DuneClient
from mongo_client import DuneMongoClient
from telegram_client import Telegram<PERSON>lient
from logger import setup_logger

load_dotenv()

class DuneService:
    def __init__(self):
        self.logger = setup_logger('dune_service')
        self.dune_client = DuneClient()
        self.mongo_client = DuneMongoClient()
        self.telegram_client = TelegramClient()
        self.scheduler = AsyncIOScheduler()
        self.logger.info("Initialized Dune service")

    async def process_query_results(self):
        """Process query results and send updates."""
        self.logger.info("Starting query results processing")
        try:
            # Execute query in a separate thread to avoid blocking async loop
            self.logger.debug("Executing Dune query in background thread...")
            results_data = await asyncio.to_thread(self.dune_client.execute_query)
            self.logger.debug("Dune query execution finished.")

            if not results_data:
                self.logger.warning("Dune query failed or returned no results.")
                return

            # Assuming results are under 'result' -> 'rows'
            rows = results_data.get('result', {}).get('rows', [])
            if not rows:
                 self.logger.warning("Query completed but results contained no rows.")
                 return

            # Save raw results to MongoDB (consider saving only rows or the whole structure)
            # For simplicity, saving the whole structure for now.
            # Run synchronous mongo operations in thread
            saved_doc_id = await asyncio.to_thread(self.mongo_client.save_query_results, results_data)
            self.logger.info(f"Saved raw results to MongoDB with ID: {saved_doc_id}")

            # Process each row for potential updates
            processed_count = 0
            for row_data in rows:
                # Add a check to see if this specific token trade has been processed
                # This requires a more sophisticated check in MongoDB, e.g., based on a unique identifier from the row
                # For now, let's assume we process all rows found in the latest query
                # and rely on the 'processed' flag on the *entire document* in subsequent runs.
                # A more robust implementation would track individual trades.
                try:
                    await self.telegram_client.send_token_update(row_data)
                    processed_count += 1
                    # Mark the entire document as processed *after* all rows are sent
                    # This logic needs refinement if individual row processing matters more
                except Exception as e:
                    self.logger.error(f"Error sending Telegram update for row: {str(row_data)[:100]}... Error: {e}")
                    # Decide if one failed update should stop others

            if processed_count > 0:
                 self.logger.info(f"Sent {processed_count} token updates to Telegram.")
                 # Mark the whole document as processed after attempting all rows
                 # Run synchronous mongo operation in thread
                 await asyncio.to_thread(self.mongo_client.mark_as_processed, saved_doc_id)
                 self.logger.info(f"Marked result document {saved_doc_id} as processed.")
            else:
                 self.logger.info("No token updates sent in this cycle.")

        except Exception as e:
            # Catch broader errors during the process_query_results flow
            self.logger.error(f"Critical error in process_query_results: {e}", exc_info=True)
            # Avoid sending the generic error if it was just a query failure (handled above)
            # This generic error message will now only be sent for unexpected errors during processing.
            await self.telegram_client.send_error_message(f"A critical error occurred in the service: {e}")

    async def start(self):
        """Start the service."""
        self.logger.info("Starting Dune service")
        try:
            # Schedule the query execution
            self.scheduler.add_job(
                self.process_query_results,
                trigger=IntervalTrigger(minutes=30),
                next_run_time=datetime.now()
            )
            self.scheduler.start()
            self.logger.info("Scheduler started successfully")

            # Keep the service running
            while True:
                await asyncio.sleep(1)

        except Exception as e:
            self.logger.error(f"Error starting service: {str(e)}")
            raise

    async def stop(self):
        """Stop the service."""
        self.logger.info("Stopping Dune service")
        try:
            if self.scheduler.running:
                self.scheduler.shutdown()
                self.logger.info("Scheduler stopped successfully")

            # Run synchronous mongo operation in thread
            await asyncio.to_thread(self.mongo_client.close)
            self.logger.info("MongoDB connection closed")

        except Exception as e:
            self.logger.error(f"Error stopping service: {str(e)}")
            raise

async def main():
    service = DuneService()
    try:
        await service.start()
    except KeyboardInterrupt:
        print("\nShutting down...")
    finally:
        await service.stop()

if __name__ == "__main__":
    asyncio.run(main())