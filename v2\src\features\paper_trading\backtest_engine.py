"""
🔄 Backtest Engine

Historical data replay and strategy performance testing with
parameter optimization and results visualization.
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import pandas as pd

from ...config.logging_config import get_logger
from ...shared.types import SignalType, MarketData
from ...features.data_pipeline import DataAggregator
from ...features.signal_processing import SignalGenerator, TechnicalAnalyzer

from .portfolio_manager import PortfolioManager
from .trade_executor import TradeExecutor
from .performance_tracker import PerformanceTracker

logger = get_logger(__name__)


@dataclass
class BacktestConfig:
    """Backtest configuration"""
    start_date: datetime
    end_date: datetime
    initial_balance: Decimal
    tokens: List[str]
    strategy_params: Dict[str, Any]
    commission: Decimal = Decimal("0.003")
    slippage: Decimal = Decimal("0.001")
    max_positions: int = 10


@dataclass
class BacktestResult:
    """Backtest results"""
    config: BacktestConfig
    total_return: Decimal
    total_return_percent: Decimal
    sharpe_ratio: Optional[Decimal]
    max_drawdown: Decimal
    win_rate: Decimal
    total_trades: int
    profitable_trades: int
    final_balance: Decimal
    trade_history: List[Dict[str, Any]]
    daily_returns: List[float]
    performance_metrics: Dict[str, Any]


class BacktestEngine:
    """
    🔄 Backtest Engine
    
    Provides historical strategy testing with:
    - Historical data replay
    - Strategy performance testing
    - Parameter optimization
    - Risk analysis
    - Performance visualization
    """
    
    def __init__(self):
        self.logger = logger
        self.data_aggregator = DataAggregator()
        self.signal_generator = SignalGenerator()
        self.technical_analyzer = TechnicalAnalyzer()
        self.portfolio_manager = PortfolioManager()
        self.trade_executor = TradeExecutor()
        self.performance_tracker = PerformanceTracker()
    
    async def run_backtest(
        self,
        config: BacktestConfig,
        strategy_function: Optional[Callable] = None
    ) -> BacktestResult:
        """
        Run a backtest with given configuration
        
        Args:
            config: Backtest configuration
            strategy_function: Custom strategy function (optional)
            
        Returns:
            BacktestResult with performance metrics
        """
        try:
            self.logger.info(f"Starting backtest from {config.start_date} to {config.end_date}")
            
            # Create temporary portfolio for backtest
            portfolio = await self.portfolio_manager.create_portfolio(
                name=f"Backtest_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                initial_balance=config.initial_balance,
                description="Backtest portfolio"
            )
            
            # Get historical data for all tokens
            historical_data = await self._get_historical_data(config)
            
            if not historical_data:
                raise ValueError("No historical data available for backtest")
            
            # Run simulation
            trade_history = await self._simulate_trading(
                portfolio.id,
                historical_data,
                config,
                strategy_function
            )
            
            # Calculate final performance
            final_value_data = await self.portfolio_manager.calculate_portfolio_value(portfolio.id)
            final_balance = Decimal(str(final_value_data["total_value"]))
            
            # Calculate performance metrics
            performance_snapshot = await self.performance_tracker.calculate_performance_metrics(portfolio.id)
            
            # Create result
            result = BacktestResult(
                config=config,
                total_return=final_balance - config.initial_balance,
                total_return_percent=performance_snapshot.total_return_percent,
                sharpe_ratio=performance_snapshot.sharpe_ratio,
                max_drawdown=performance_snapshot.max_drawdown,
                win_rate=performance_snapshot.win_rate,
                total_trades=len(trade_history),
                profitable_trades=len([t for t in trade_history if t.get("pnl", 0) > 0]),
                final_balance=final_balance,
                trade_history=trade_history,
                daily_returns=await self.performance_tracker._get_daily_returns(portfolio.id),
                performance_metrics=performance_snapshot.__dict__
            )
            
            self.logger.info(
                f"Backtest completed. Return: {result.total_return_percent:.2f}%, "
                f"Trades: {result.total_trades}, Win rate: {result.win_rate:.1f}%"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error running backtest: {str(e)}")
            raise
    
    async def _get_historical_data(self, config: BacktestConfig) -> Dict[str, List[MarketData]]:
        """Get historical market data for backtest"""
        try:
            historical_data = {}
            
            for token in config.tokens:
                # Calculate hours between start and end date
                time_diff = config.end_date - config.start_date
                hours = int(time_diff.total_seconds() / 3600)
                
                # Get historical data
                token_data = await self.data_aggregator.get_price_history(
                    token,
                    hours=hours,
                    start_time=config.start_date
                )
                
                if token_data:
                    historical_data[token] = token_data
                    self.logger.info(f"Retrieved {len(token_data)} data points for {token}")
                else:
                    self.logger.warning(f"No historical data found for {token}")
            
            return historical_data
            
        except Exception as e:
            self.logger.error(f"Error getting historical data: {str(e)}")
            return {}
    
    async def _simulate_trading(
        self,
        portfolio_id: str,
        historical_data: Dict[str, List[MarketData]],
        config: BacktestConfig,
        strategy_function: Optional[Callable]
    ) -> List[Dict[str, Any]]:
        """Simulate trading over historical data"""
        try:
            trade_history = []
            
            # Create time series from all tokens
            all_timestamps = set()
            for token_data in historical_data.values():
                for data_point in token_data:
                    all_timestamps.add(data_point.timestamp)
            
            # Sort timestamps
            sorted_timestamps = sorted(all_timestamps)
            
            # Filter timestamps within backtest period
            filtered_timestamps = [
                ts for ts in sorted_timestamps
                if config.start_date <= ts <= config.end_date
            ]
            
            self.logger.info(f"Simulating {len(filtered_timestamps)} time periods")
            
            # Simulate trading for each timestamp
            for i, timestamp in enumerate(filtered_timestamps):
                # Get market data for this timestamp
                current_market_data = {}
                for token, token_data in historical_data.items():
                    # Find closest data point
                    closest_data = min(
                        token_data,
                        key=lambda x: abs((x.timestamp - timestamp).total_seconds())
                    )
                    current_market_data[token] = closest_data
                
                # Generate signals for each token
                for token, market_data in current_market_data.items():
                    # Get price history up to current timestamp
                    price_history = [
                        data for data in historical_data[token]
                        if data.timestamp <= timestamp
                    ]
                    
                    if len(price_history) < 50:  # Need sufficient history
                        continue
                    
                    # Use custom strategy or default signal generation
                    if strategy_function:
                        signal = await strategy_function(token, price_history, market_data, config.strategy_params)
                    else:
                        signal = await self._default_strategy(token, price_history, market_data)
                    
                    if signal:
                        # Execute trade
                        trade = await self.trade_executor.execute_market_order(
                            portfolio_id=portfolio_id,
                            token_address=token,
                            side=signal["side"],
                            quantity=signal["quantity"],
                            signal_id=signal.get("signal_id")
                        )
                        
                        if trade:
                            trade_record = {
                                "timestamp": timestamp,
                                "token": token,
                                "side": trade.side,
                                "quantity": float(trade.quantity),
                                "price": float(trade.price),
                                "value": float(trade.value_usd),
                                "fees": float(trade.fees),
                                "pnl": float(trade.pnl) if trade.pnl else 0
                            }
                            trade_history.append(trade_record)
                
                # Update portfolio snapshot periodically
                if i % 24 == 0:  # Daily snapshots
                    portfolio = await self.portfolio_manager.get_portfolio(portfolio_id)
                    if portfolio:
                        portfolio.add_daily_snapshot()
                        await portfolio.save()
                
                # Progress logging
                if i % 100 == 0:
                    progress = (i / len(filtered_timestamps)) * 100
                    self.logger.info(f"Backtest progress: {progress:.1f}%")
            
            return trade_history
            
        except Exception as e:
            self.logger.error(f"Error simulating trading: {str(e)}")
            return []
    
    async def _default_strategy(
        self,
        token: str,
        price_history: List[MarketData],
        current_market_data: MarketData
    ) -> Optional[Dict[str, Any]]:
        """Default trading strategy based on technical analysis"""
        try:
            # Perform technical analysis
            indicators = await self.technical_analyzer.analyze_token(price_history)
            
            # Simple strategy: RSI + MACD
            signal = None
            
            if indicators.rsi and indicators.macd and indicators.macd_signal:
                # Buy signal: RSI oversold + MACD bullish crossover
                if (indicators.rsi < 30 and 
                    indicators.macd > indicators.macd_signal and
                    indicators.macd_histogram and indicators.macd_histogram > 0):
                    
                    signal = {
                        "side": SignalType.BUY,
                        "quantity": Decimal("100") / current_market_data.price,  # $100 position
                        "reason": "RSI oversold + MACD bullish"
                    }
                
                # Sell signal: RSI overbought + MACD bearish crossover
                elif (indicators.rsi > 70 and 
                      indicators.macd < indicators.macd_signal and
                      indicators.macd_histogram and indicators.macd_histogram < 0):
                    
                    signal = {
                        "side": SignalType.SELL,
                        "quantity": Decimal("100") / current_market_data.price,  # $100 position
                        "reason": "RSI overbought + MACD bearish"
                    }
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Error in default strategy: {str(e)}")
            return None
    
    async def optimize_parameters(
        self,
        base_config: BacktestConfig,
        parameter_ranges: Dict[str, List[Any]],
        optimization_metric: str = "sharpe_ratio"
    ) -> Dict[str, Any]:
        """
        Optimize strategy parameters using grid search
        
        Args:
            base_config: Base backtest configuration
            parameter_ranges: Dictionary of parameter names and their ranges
            optimization_metric: Metric to optimize for
            
        Returns:
            Best parameters and results
        """
        try:
            self.logger.info(f"Starting parameter optimization for {optimization_metric}")
            
            best_result = None
            best_params = None
            best_score = float('-inf')
            
            # Generate parameter combinations
            param_combinations = self._generate_parameter_combinations(parameter_ranges)
            
            self.logger.info(f"Testing {len(param_combinations)} parameter combinations")
            
            for i, params in enumerate(param_combinations):
                # Update config with new parameters
                test_config = BacktestConfig(
                    start_date=base_config.start_date,
                    end_date=base_config.end_date,
                    initial_balance=base_config.initial_balance,
                    tokens=base_config.tokens,
                    strategy_params=params,
                    commission=base_config.commission,
                    slippage=base_config.slippage,
                    max_positions=base_config.max_positions
                )
                
                # Run backtest
                try:
                    result = await self.run_backtest(test_config)
                    
                    # Get optimization score
                    score = self._get_optimization_score(result, optimization_metric)
                    
                    if score > best_score:
                        best_score = score
                        best_result = result
                        best_params = params
                    
                    self.logger.info(f"Combination {i+1}/{len(param_combinations)}: {optimization_metric}={score:.4f}")
                    
                except Exception as e:
                    self.logger.warning(f"Backtest failed for params {params}: {str(e)}")
                    continue
            
            return {
                "best_parameters": best_params,
                "best_score": best_score,
                "best_result": best_result,
                "optimization_metric": optimization_metric,
                "total_combinations_tested": len(param_combinations)
            }
            
        except Exception as e:
            self.logger.error(f"Error optimizing parameters: {str(e)}")
            return {}
    
    def _generate_parameter_combinations(self, parameter_ranges: Dict[str, List[Any]]) -> List[Dict[str, Any]]:
        """Generate all combinations of parameters"""
        import itertools
        
        param_names = list(parameter_ranges.keys())
        param_values = list(parameter_ranges.values())
        
        combinations = []
        for combination in itertools.product(*param_values):
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _get_optimization_score(self, result: BacktestResult, metric: str) -> float:
        """Get optimization score for a given metric"""
        try:
            if metric == "sharpe_ratio":
                return float(result.sharpe_ratio) if result.sharpe_ratio else -999
            elif metric == "total_return":
                return float(result.total_return_percent)
            elif metric == "win_rate":
                return float(result.win_rate)
            elif metric == "profit_factor":
                # Calculate profit factor from trade history
                profits = sum(t["pnl"] for t in result.trade_history if t["pnl"] > 0)
                losses = abs(sum(t["pnl"] for t in result.trade_history if t["pnl"] < 0))
                return profits / losses if losses > 0 else 0
            else:
                return float(result.total_return_percent)  # Default to total return
                
        except Exception as e:
            self.logger.error(f"Error calculating optimization score: {str(e)}")
            return -999
    
    async def compare_strategies(
        self,
        strategies: Dict[str, Callable],
        config: BacktestConfig
    ) -> Dict[str, BacktestResult]:
        """Compare multiple strategies on the same data"""
        try:
            results = {}
            
            for strategy_name, strategy_function in strategies.items():
                self.logger.info(f"Testing strategy: {strategy_name}")
                
                result = await self.run_backtest(config, strategy_function)
                results[strategy_name] = result
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error comparing strategies: {str(e)}")
            return {}
    
    def generate_backtest_report(self, result: BacktestResult) -> Dict[str, Any]:
        """Generate comprehensive backtest report"""
        try:
            return {
                "summary": {
                    "total_return": float(result.total_return),
                    "total_return_percent": float(result.total_return_percent),
                    "final_balance": float(result.final_balance),
                    "initial_balance": float(result.config.initial_balance),
                    "sharpe_ratio": float(result.sharpe_ratio) if result.sharpe_ratio else None,
                    "max_drawdown": float(result.max_drawdown),
                    "win_rate": float(result.win_rate)
                },
                "trading_stats": {
                    "total_trades": result.total_trades,
                    "profitable_trades": result.profitable_trades,
                    "losing_trades": result.total_trades - result.profitable_trades,
                    "win_rate": float(result.win_rate),
                    "avg_trade_return": sum(t["pnl"] for t in result.trade_history) / len(result.trade_history) if result.trade_history else 0
                },
                "config": {
                    "start_date": result.config.start_date.isoformat(),
                    "end_date": result.config.end_date.isoformat(),
                    "tokens": result.config.tokens,
                    "initial_balance": float(result.config.initial_balance),
                    "commission": float(result.config.commission),
                    "slippage": float(result.config.slippage)
                },
                "performance_metrics": result.performance_metrics
            }
            
        except Exception as e:
            self.logger.error(f"Error generating backtest report: {str(e)}")
            return {}
