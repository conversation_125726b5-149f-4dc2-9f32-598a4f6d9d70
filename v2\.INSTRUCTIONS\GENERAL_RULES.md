# 🚀 GENERAL AUGMENT RULES - Zero to Scale Development

## 🔄 WORKFLOW MANAGEMENT
- **Branch Strategy**: Always checkout to main branch, create feature branches for new work
- **Documentation**: Update CHANGELOG.md and TODO.md for every significant change
- **Verification**: If request is already implemented, provide clear guidance instead of duplicating
- **Clarification**: When requirements are unclear, propose better alternatives but always ask for verification first

## 🎯 DEVELOPMENT PRINCIPLES
- **Modular Architecture**: Every implementation must be modular with maximum clean code adherence
- **Database**: Use MongoDB Atlas with online credentials (stored securely in environment variables)
- **AI Integration**: Implement cost-efficient AI integration strategies to minimize tokens while maximizing quality
- **Script Structure**: Each function needs accompanying scripts - maintain modular structure throughout

## 📚 CONSISTENCY & SCALABILITY
- **Project Structure**: Maintain consistent coding patterns and scalable architecture for future growth
- **Memory Management**: Update AI memory to read project structure, security rules, log rules, and clean code rules at start of each session
- **Cleanup**: Remove temporary files after use to maintain clean workspace
- **Extensibility**: Design each module to be easily changeable and extendable

## 🏗️ ARCHITECTURE PATTERNS
- **Design Pattern**: Choose and consistently apply a coding pattern throughout the project
- **Version Control**: Commit changes with meaningful messages following conventional commit standards
- **Testing Strategy**: Implement comprehensive testing at unit, integration, and e2e levels

## 🔐 SECURITY & PERFORMANCE
- **Environment Variables**: Never hardcode credentials or sensitive data
- **Error Handling**: Implement robust error handling and logging
- **Performance**: Optimize for scalability from day one
- **Monitoring**: Include observability and monitoring capabilities

## 📋 QUALITY ASSURANCE
- **Code Review**: Self-review code against all established rules before committing
- **Documentation**: Maintain up-to-date technical documentation
- **Dependencies**: Keep dependencies minimal and well-justified
- **Backwards Compatibility**: Consider migration strategies for breaking changes