"""
📋 Subscription Manager

User subscription and preference management for notifications
with granular control and intelligent filtering.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass, asdict
from decimal import Decimal

from ...config.logging_config import get_logger
from ...database.models import User
from ...shared.types import SignalStrength

logger = get_logger(__name__)


class SubscriptionType(Enum):
    """Subscription types"""
    SIGNAL_ALERTS = "signal_alerts"
    PORTFOLIO_UPDATES = "portfolio_updates"
    TRADE_NOTIFICATIONS = "trade_notifications"
    RISK_WARNINGS = "risk_warnings"
    PERFORMANCE_REPORTS = "performance_reports"
    SYSTEM_ALERTS = "system_alerts"


@dataclass
class NotificationPreferences:
    """User notification preferences"""
    # Signal preferences
    signal_alerts: bool = True
    min_signal_strength: str = "MODERATE"
    min_confidence: float = 0.6
    blocked_tokens: List[str] = None
    preferred_signal_types: List[str] = None  # BUY, SELL, HOLD
    
    # Portfolio preferences
    portfolio_updates: bool = True
    portfolio_update_interval_hours: int = 6
    min_portfolio_change_percent: float = 5.0
    
    # Trade preferences
    trade_notifications: bool = True
    trade_notification_types: List[str] = None  # executed, failed, cancelled
    
    # Risk preferences
    risk_warnings: bool = True
    risk_warning_threshold: float = 0.7
    
    # Report preferences
    performance_reports: bool = True
    report_frequency: str = "daily"  # daily, weekly, monthly
    
    # System preferences
    system_alerts: bool = True
    quiet_hours_start: Optional[str] = None  # "22:00"
    quiet_hours_end: Optional[str] = None    # "08:00"
    timezone: str = "UTC"
    
    # Delivery preferences
    telegram_enabled: bool = True
    email_enabled: bool = False
    max_notifications_per_hour: int = 10
    
    def __post_init__(self):
        if self.blocked_tokens is None:
            self.blocked_tokens = []
        if self.preferred_signal_types is None:
            self.preferred_signal_types = ["BUY", "SELL"]
        if self.trade_notification_types is None:
            self.trade_notification_types = ["executed", "failed"]


class SubscriptionManager:
    """
    📋 Subscription Manager
    
    Manages user subscriptions and preferences with:
    - Granular notification control
    - User preference storage and retrieval
    - Subscription analytics and insights
    - Intelligent filtering and routing
    """
    
    def __init__(self):
        self.logger = logger
        
        # In-memory cache for frequently accessed preferences
        self.preferences_cache = {}
        self.cache_ttl_minutes = 30
        
        # Default subscribers for system-wide notifications
        self.default_subscribers = {
            SubscriptionType.SIGNAL_ALERTS: set(),
            SubscriptionType.PORTFOLIO_UPDATES: set(),
            SubscriptionType.TRADE_NOTIFICATIONS: set(),
            SubscriptionType.RISK_WARNINGS: set(),
            SubscriptionType.PERFORMANCE_REPORTS: set(),
            SubscriptionType.SYSTEM_ALERTS: set()
        }
    
    async def subscribe_user(
        self,
        user_id: str,
        subscription_types: List[SubscriptionType],
        preferences: Optional[NotificationPreferences] = None
    ) -> bool:
        """
        Subscribe user to notification types
        
        Args:
            user_id: User identifier
            subscription_types: List of subscription types
            preferences: User notification preferences
            
        Returns:
            Success status
        """
        try:
            self.logger.info(f"Subscribing user {user_id} to {len(subscription_types)} types")
            
            # Get or create user
            user = await User.find_one({"telegram_user_id": user_id})
            if not user:
                # Create new user with default preferences
                user = User(
                    telegram_user_id=user_id,
                    username=f"user_{user_id}",
                    email=f"{user_id}@telegram.local",
                    password_hash="telegram_user",
                    notification_preferences={}
                )
                await user.save()
            
            # Update notification preferences
            current_prefs = user.notification_preferences or {}
            
            for sub_type in subscription_types:
                current_prefs[sub_type.value] = True
                
                # Add to default subscribers
                self.default_subscribers[sub_type].add(user_id)
            
            # Update user preferences if provided
            if preferences:
                prefs_dict = asdict(preferences)
                current_prefs.update(prefs_dict)
            
            user.notification_preferences = current_prefs
            await user.save()
            
            # Clear cache for this user
            self._clear_user_cache(user_id)
            
            self.logger.info(f"User {user_id} subscribed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error subscribing user {user_id}: {str(e)}")
            return False
    
    async def unsubscribe_user(
        self,
        user_id: str,
        subscription_types: List[SubscriptionType]
    ) -> bool:
        """Unsubscribe user from notification types"""
        try:
            self.logger.info(f"Unsubscribing user {user_id} from {len(subscription_types)} types")
            
            user = await User.find_one({"telegram_user_id": user_id})
            if not user:
                return False
            
            # Update notification preferences
            current_prefs = user.notification_preferences or {}
            
            for sub_type in subscription_types:
                current_prefs[sub_type.value] = False
                
                # Remove from default subscribers
                self.default_subscribers[sub_type].discard(user_id)
            
            user.notification_preferences = current_prefs
            await user.save()
            
            # Clear cache for this user
            self._clear_user_cache(user_id)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error unsubscribing user {user_id}: {str(e)}")
            return False
    
    async def update_user_preferences(
        self,
        user_id: str,
        preferences: NotificationPreferences
    ) -> bool:
        """Update user notification preferences"""
        try:
            self.logger.info(f"Updating preferences for user {user_id}")
            
            user = await User.find_one({"telegram_user_id": user_id})
            if not user:
                return False
            
            # Convert preferences to dict and update
            prefs_dict = asdict(preferences)
            current_prefs = user.notification_preferences or {}
            current_prefs.update(prefs_dict)
            
            user.notification_preferences = current_prefs
            await user.save()
            
            # Clear cache for this user
            self._clear_user_cache(user_id)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating preferences for user {user_id}: {str(e)}")
            return False
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user notification preferences with caching"""
        try:
            # Check cache first
            cache_key = f"prefs_{user_id}"
            if cache_key in self.preferences_cache:
                cached_data = self.preferences_cache[cache_key]
                if (datetime.utcnow() - cached_data["timestamp"]).total_seconds() < (self.cache_ttl_minutes * 60):
                    return cached_data["preferences"]
            
            # Get from database
            user = await User.find_one({"telegram_user_id": user_id})
            if not user:
                # Return default preferences
                default_prefs = asdict(NotificationPreferences())
                self._cache_user_preferences(user_id, default_prefs)
                return default_prefs
            
            preferences = user.notification_preferences or {}
            
            # Merge with defaults for missing keys
            default_prefs = asdict(NotificationPreferences())
            merged_prefs = {**default_prefs, **preferences}
            
            # Cache the preferences
            self._cache_user_preferences(user_id, merged_prefs)
            
            return merged_prefs
            
        except Exception as e:
            self.logger.error(f"Error getting preferences for user {user_id}: {str(e)}")
            return asdict(NotificationPreferences())
    
    async def get_signal_subscribers(
        self,
        signal_strength: Optional[SignalStrength] = None,
        confidence_threshold: Optional[float] = None
    ) -> List[str]:
        """Get users subscribed to signal alerts with optional filtering"""
        try:
            subscribers = []
            
            # Get all users with signal alert subscriptions
            users = await User.find({
                "notification_preferences.signal_alerts": True
            }).to_list()
            
            for user in users:
                user_id = user.telegram_user_id
                if not user_id:
                    continue
                
                # Apply filters if specified
                if signal_strength or confidence_threshold:
                    prefs = await self.get_user_preferences(user_id)
                    
                    # Check signal strength filter
                    if signal_strength:
                        min_strength = prefs.get("min_signal_strength", "MODERATE")
                        if signal_strength.value < SignalStrength[min_strength].value:
                            continue
                    
                    # Check confidence filter
                    if confidence_threshold:
                        min_confidence = prefs.get("min_confidence", 0.6)
                        if confidence_threshold < min_confidence:
                            continue
                
                subscribers.append(user_id)
            
            # Add default subscribers
            subscribers.extend(list(self.default_subscribers[SubscriptionType.SIGNAL_ALERTS]))
            
            return list(set(subscribers))  # Remove duplicates
            
        except Exception as e:
            self.logger.error(f"Error getting signal subscribers: {str(e)}")
            return []
    
    async def get_portfolio_subscribers(self, portfolio_id: Optional[str] = None) -> List[str]:
        """Get users subscribed to portfolio updates"""
        try:
            subscribers = []
            
            if portfolio_id:
                # Get specific portfolio subscribers (portfolio owners)
                from ...database.models import Portfolio
                portfolio = await Portfolio.find_one({"_id": portfolio_id})
                if portfolio and portfolio.user_id:
                    # Check if user wants portfolio updates
                    prefs = await self.get_user_preferences(portfolio.user_id)
                    if prefs.get("portfolio_updates", True):
                        subscribers.append(portfolio.user_id)
            else:
                # Get all portfolio update subscribers
                users = await User.find({
                    "notification_preferences.portfolio_updates": True
                }).to_list()
                
                subscribers = [user.telegram_user_id for user in users if user.telegram_user_id]
            
            # Add default subscribers
            subscribers.extend(list(self.default_subscribers[SubscriptionType.PORTFOLIO_UPDATES]))
            
            return list(set(subscribers))
            
        except Exception as e:
            self.logger.error(f"Error getting portfolio subscribers: {str(e)}")
            return []
    
    async def get_trade_subscribers(self, portfolio_id: Optional[str] = None) -> List[str]:
        """Get users subscribed to trade notifications"""
        try:
            subscribers = []
            
            if portfolio_id:
                # Get specific portfolio subscribers
                from ...database.models import Portfolio
                portfolio = await Portfolio.find_one({"_id": portfolio_id})
                if portfolio and portfolio.user_id:
                    prefs = await self.get_user_preferences(portfolio.user_id)
                    if prefs.get("trade_notifications", True):
                        subscribers.append(portfolio.user_id)
            else:
                # Get all trade notification subscribers
                users = await User.find({
                    "notification_preferences.trade_notifications": True
                }).to_list()
                
                subscribers = [user.telegram_user_id for user in users if user.telegram_user_id]
            
            # Add default subscribers
            subscribers.extend(list(self.default_subscribers[SubscriptionType.TRADE_NOTIFICATIONS]))
            
            return list(set(subscribers))
            
        except Exception as e:
            self.logger.error(f"Error getting trade subscribers: {str(e)}")
            return []
    
    async def get_report_subscribers(self) -> List[str]:
        """Get users subscribed to performance reports"""
        try:
            users = await User.find({
                "notification_preferences.performance_reports": True
            }).to_list()
            
            subscribers = [user.telegram_user_id for user in users if user.telegram_user_id]
            
            # Add default subscribers
            subscribers.extend(list(self.default_subscribers[SubscriptionType.PERFORMANCE_REPORTS]))
            
            return list(set(subscribers))
            
        except Exception as e:
            self.logger.error(f"Error getting report subscribers: {str(e)}")
            return []
    
    async def get_user_subscriptions(self, user_id: str) -> Dict[str, bool]:
        """Get all subscriptions for a user"""
        try:
            user = await User.find_one({"telegram_user_id": user_id})
            if not user:
                return {}
            
            preferences = user.notification_preferences or {}
            
            # Extract subscription status
            subscriptions = {}
            for sub_type in SubscriptionType:
                subscriptions[sub_type.value] = preferences.get(sub_type.value, False)
            
            return subscriptions
            
        except Exception as e:
            self.logger.error(f"Error getting subscriptions for user {user_id}: {str(e)}")
            return {}
    
    async def is_user_in_quiet_hours(self, user_id: str) -> bool:
        """Check if user is currently in quiet hours"""
        try:
            prefs = await self.get_user_preferences(user_id)
            
            quiet_start = prefs.get("quiet_hours_start")
            quiet_end = prefs.get("quiet_hours_end")
            
            if not quiet_start or not quiet_end:
                return False
            
            # Get current time in user's timezone
            # For now, assume UTC - in production, implement proper timezone handling
            current_time = datetime.utcnow().time()
            
            # Parse quiet hours
            start_time = datetime.strptime(quiet_start, "%H:%M").time()
            end_time = datetime.strptime(quiet_end, "%H:%M").time()
            
            # Check if current time is in quiet hours
            if start_time <= end_time:
                # Same day range (e.g., 22:00 to 23:59)
                return start_time <= current_time <= end_time
            else:
                # Overnight range (e.g., 22:00 to 08:00)
                return current_time >= start_time or current_time <= end_time
            
        except Exception as e:
            self.logger.error(f"Error checking quiet hours for user {user_id}: {str(e)}")
            return False
    
    def _cache_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> None:
        """Cache user preferences"""
        cache_key = f"prefs_{user_id}"
        self.preferences_cache[cache_key] = {
            "preferences": preferences,
            "timestamp": datetime.utcnow()
        }
        
        # Clean up old cache entries
        cutoff_time = datetime.utcnow() - timedelta(minutes=self.cache_ttl_minutes * 2)
        self.preferences_cache = {
            k: v for k, v in self.preferences_cache.items()
            if v["timestamp"] > cutoff_time
        }
    
    def _clear_user_cache(self, user_id: str) -> None:
        """Clear cached preferences for a user"""
        cache_key = f"prefs_{user_id}"
        self.preferences_cache.pop(cache_key, None)
    
    async def get_subscription_analytics(self) -> Dict[str, Any]:
        """Get subscription analytics and insights"""
        try:
            analytics = {
                "total_users": 0,
                "subscription_counts": {},
                "active_subscribers": {},
                "preference_distribution": {}
            }
            
            # Get all users
            users = await User.find({}).to_list()
            analytics["total_users"] = len(users)
            
            # Count subscriptions
            for sub_type in SubscriptionType:
                count = len(await User.find({
                    f"notification_preferences.{sub_type.value}": True
                }).to_list())
                analytics["subscription_counts"][sub_type.value] = count
            
            # Add default subscriber counts
            for sub_type, subscribers in self.default_subscribers.items():
                analytics["active_subscribers"][sub_type.value] = len(subscribers)
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"Error getting subscription analytics: {str(e)}")
            return {}
