import os
import time
from typing import Dict, Any, Optional
import requests
from dotenv import load_dotenv
from logger import setup_logger
from datetime import datetime, timedelta, timezone
import dateutil.parser

load_dotenv()

EXECUTION_ID_FILE = ".last_execution_id"

class DuneClient:
    def __init__(self):
        self.logger = setup_logger('dune_client')
        self.api_key = os.getenv('DUNE_API_KEY')
        self.query_id = os.getenv('DUNE_QUERY_ID')
        self.base_url = "https://api.dune.com/api/v1"
        self.headers = {
            "x-dune-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        self.logger.info(f"Initialized DuneClient with query_id: {self.query_id}")

    def _read_last_execution_id(self) -> Optional[str]:
        """Reads the last known execution ID from the file."""
        try:
            if os.path.exists(EXECUTION_ID_FILE):
                with open(EXECUTION_ID_FILE, 'r') as f:
                    return f.read().strip()
        except IOError as e:
            self.logger.error(f"Error reading execution ID file: {e}")
        return None

    def _write_last_execution_id(self, execution_id: str) -> None:
        """Writes the given execution ID to the file."""
        try:
            with open(EXECUTION_ID_FILE, 'w') as f:
                f.write(execution_id)
        except IOError as e:
            self.logger.error(f"Error writing execution ID file: {e}")
            
    def _get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Gets the status and potentially end time of a specific execution ID."""
        status_url = f"{self.base_url}/execution/{execution_id}/status"
        response = None # Initialize response to None
        try:
            response = requests.get(status_url, headers=self.headers)
            response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
            status_data = response.json()
            state = status_data.get("state")
            ended_at_str = status_data.get("execution_ended_at") # Get potential end time string
            ended_at_dt = None
            if ended_at_str:
                try:
                    # Use dateutil.parser for robust datetime string parsing
                    ended_at_dt = dateutil.parser.isoparse(ended_at_str)
                    # Ensure it's timezone-aware (UTC)
                    if ended_at_dt.tzinfo is None:
                         ended_at_dt = ended_at_dt.replace(tzinfo=timezone.utc)
                except ValueError:
                     self.logger.warning(f"Could not parse 'execution_ended_at' timestamp: {ended_at_str}")
            return {"state": state, "ended_at": ended_at_dt}
        except requests.exceptions.HTTPError as e:
            # Handle non-2xx responses specifically
            if response is not None and response.status_code == 404:
                 self.logger.warning(f"Execution ID {execution_id} not found (404). Assuming invalid/old.")
                 return None # Treat as invalid/needs new execution
            else:
                 # Log the status code if available
                 status_code = response.status_code if response is not None else "N/A"
                 self.logger.error(f"HTTP error {status_code} getting status for {execution_id}: {e}")
                 return None # Treat other HTTP errors as needing a new execution for now
        except requests.exceptions.RequestException as e:
            # Handle connection errors, timeouts, etc. (where response might not exist)
            self.logger.error(f"Network/Request error getting status for {execution_id}: {e}")
            return None # Treat network errors as potentially temporary, but signal failure for this check
        except Exception as e:
            # Catch any other unexpected errors
            self.logger.error(f"Unexpected error getting status for {execution_id}: {e}", exc_info=True)
            return None

    def _get_execution_results(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Fetches the results for a completed execution ID."""
        self.logger.info(f"Attempting to fetch results for completed execution {execution_id}")
        results_url = f"{self.base_url}/execution/{execution_id}/results"
        try:
            results_response = requests.get(results_url, headers=self.headers)
            results_response.raise_for_status()
            results = results_response.json()
            if not results.get('result', {}).get('rows'): # Check nested structure
                self.logger.warning(f"Execution {execution_id} completed but returned no result rows.")
                return None # Treat as no new data, but not necessarily a failure to fetch
            self.logger.debug(f"Successfully retrieved results for {execution_id}")
            return results # Return the full response containing 'result' key
        except requests.exceptions.HTTPError as e:
             status_code = e.response.status_code if e.response is not None else "N/A"
             self.logger.error(f"HTTP error {status_code} getting results for completed execution {execution_id}: {e}")
             return None # Failed to get results due to API error (e.g., 404, 500)
        except requests.exceptions.RequestException as e:
             # Network errors, timeouts during result fetch
             self.logger.error(f"Network/Request error getting results for completed execution {execution_id}: {e}")
             return None # Failed to get results due to network issue
        except Exception as e:
            self.logger.error(f"Unexpected error getting results for completed execution {execution_id}: {e}", exc_info=True)
            return None

    def _poll_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Polls the status of an execution and returns results or None. Raises TimeoutError on timeout."""
        self.logger.info(f"Polling status for execution ID: {execution_id}")
        # Set timeout (60 minutes)
        timeout = 60 * 60  # 60 minutes in seconds
        start_time = time.time()
        poll_interval = 60 # Check every 60 seconds

        while True:
            if time.time() - start_time > timeout:
                self.logger.error(f"Polling for execution {execution_id} timed out after {timeout/60.0:.1f} minutes")
                raise TimeoutError(f"Polling timed out for execution {execution_id}") # Raise TimeoutError

            status_info = self._get_execution_status(execution_id) # Returns dict {"state": ..., "ended_at": ...} or None

            if status_info is None: # Indicates an error fetching status or invalid ID (e.g., 404, network error)
                 self.logger.error(f"Could not determine status for {execution_id} during polling. Assuming failure for this attempt.")
                 # We might retry polling later if this was a transient network issue,
                 # but for this specific poll cycle, we cannot proceed.
                 # The outer loop in execute_query handles retries based on TimeoutError.
                 # If the status check persistently fails (e.g. 404), _get_execution_status handles it.
                 # Here, we just wait before the next poll attempt.
                 time.sleep(poll_interval)
                 continue # Go to next iteration of the while loop to re-check status

            current_state = status_info.get("state")

            if current_state == "QUERY_STATE_COMPLETED":
                self.logger.info(f"Execution {execution_id} completed. Fetching results...")
                # Use the dedicated result fetching method
                return self._get_execution_results(execution_id)

            elif current_state == "QUERY_STATE_FAILED":
                self.logger.error(f"Execution {execution_id} failed according to Dune API.")
                return None # Execution definitively failed
            elif current_state in ["QUERY_STATE_CANCELLED", "QUERY_STATE_EXPIRED"]:
                 self.logger.warning(f"Execution {execution_id} ended with state: {current_state}.")
                 return None # Terminal state, no results
            elif current_state in ["QUERY_STATE_PENDING", "QUERY_STATE_EXECUTING"]: # Dune examples use EXECUTING, let's keep PENDING too
                elapsed_time = time.time() - start_time
                state_msg = current_state.split('_')[-1].lower() # Dynamically get state
                self.logger.info(f"Execution {execution_id} is {state_msg}. Elapsed time: {elapsed_time/60:.1f} minutes. Waiting {poll_interval}s...")
                time.sleep(poll_interval)
            else: # Should not happen with known states, but handle unexpected ones
                self.logger.warning(f"Unknown or unexpected execution state '{current_state}' for {execution_id}. Status info: {status_info}. Waiting {poll_interval}s...")
                time.sleep(poll_interval)


    def execute_query(self) -> Optional[Dict[str, Any]]:
        """Checks for ongoing executions or starts a new one (unless recently completed), then polls for results."""
        execution_to_poll = None
        results_to_return = None # Variable to hold results if fetched directly
        min_completion_age = timedelta(hours=2) # Minimum age for completed query before re-running

        # 1. Check last known execution ID
        last_execution_id = self._read_last_execution_id()
        if last_execution_id:
            self.logger.info(f"Found last execution ID: {last_execution_id}. Checking its status.")
            # Expects a dict {"state": ..., "ended_at": datetime | None} or None
            last_status_info = self._get_execution_status(last_execution_id)

            if last_status_info:
                last_state = last_status_info.get("state")
                last_ended_at = last_status_info.get("ended_at")

                # 2a. If active (still executing/pending), poll it
                # Note: Dune docs suggest only QUERY_STATE_EXECUTING and QUERY_STATE_PENDING are active non-terminal states before completion/failure.
                if last_state in ["QUERY_STATE_PENDING", "QUERY_STATE_EXECUTING"]:
                    self.logger.info(f"Execution {last_execution_id} is still active ({last_state}). Resuming polling.")
                    execution_to_poll = last_execution_id
                # 2b. Check if completed recently
                elif last_state == "QUERY_STATE_COMPLETED" and last_ended_at is not None:
                    now_utc = datetime.now(timezone.utc)
                    time_since_completion = now_utc - last_ended_at
                    if time_since_completion < min_completion_age:
                        self.logger.info(f"Last execution {last_execution_id} completed successfully at {last_ended_at.strftime('%Y-%m-%d %H:%M:%S %Z')} (less than {min_completion_age} ago). Attempting to fetch its results instead of starting new execution.")
                        # Attempt to get results directly for this completed execution
                        results_to_return = self._get_execution_results(last_execution_id)
                        # If results were fetched successfully OR failed (returns None), we don't start/poll a new one.
                        # The calling function (process_query_results) will handle None appropriately.
                        return results_to_return
                    else:
                         self.logger.info(f"Last execution {last_execution_id} completed successfully, but more than {min_completion_age} ago. Starting a new one.")
                # 2c. Handle other finished/failed/unexpected states (start new one)
                else:
                    # Includes QUERY_STATE_FAILED, QUERY_STATE_CANCELLED, QUERY_STATE_EXPIRED,
                    # or if ended_at was None for completed state, or any other unknown state.
                    self.logger.info(f"Last execution {last_execution_id} has terminal or unknown status: {last_state or 'Unknown'}. Starting a new one.")
            else:
                 # If _get_execution_status returned None (e.g., network error, 404)
                 self.logger.warning(f"Could not get status for last execution ID {last_execution_id} (Network error or invalid ID?). Starting a new one.")

        # 3. If no active execution to poll was found AND we didn't return results directly above, start a new one.
        if execution_to_poll is None and results_to_return is None:
            self.logger.info(f"Initiating new execution for query {self.query_id}")
            execute_url = f"{self.base_url}/query/{self.query_id}/execute"
            try:
                response = requests.post(execute_url, headers=self.headers, json={"query_parameters": {}}) 
                response.raise_for_status()
                new_execution_id = response.json()["execution_id"]
                self.logger.info(f"New execution started with ID: {new_execution_id}")
                self._write_last_execution_id(new_execution_id)
                execution_to_poll = new_execution_id
            except requests.exceptions.RequestException as e:
                self.logger.error(f"API error starting new execution: {e}")
                return None
            except Exception as e:
                 self.logger.error(f"Unexpected error starting new execution: {e}")
                 return None

        # 4. Poll the determined execution ID
        if execution_to_poll:
            max_attempts = 3 # Initial attempt + 2 retries
            retry_delay = 10 * 60 # 10 minutes in seconds
            
            for attempt in range(max_attempts):
                try:
                    self.logger.info(f"Attempt {attempt + 1}/{max_attempts} to poll execution {execution_to_poll}")
                    results = self._poll_execution_status(execution_to_poll)
                    # If polling succeeds (doesn't time out or fail), return results
                    return results 
                except TimeoutError:
                    if attempt < max_attempts - 1:
                        self.logger.warning(f"Polling attempt {attempt + 1} timed out for {execution_to_poll}. Waiting {retry_delay/60} minutes before retrying...")
                        time.sleep(retry_delay)
                    else:
                        self.logger.error(f"Polling for execution {execution_to_poll} failed after {max_attempts} attempts (including initial timeout).")
                        return None # Give up after final attempt
                except Exception as e:
                     # Catch other potential errors during polling
                     self.logger.error(f"Unexpected error during polling attempt {attempt + 1} for {execution_to_poll}: {e}")
                     return None # Stop retrying on unexpected errors
                     
            # Should only be reached if all attempts fail due to timeout
            return None 
        else:
             # Should not happen if logic above is correct, but as a safeguard
             self.logger.error("Failed to determine an execution ID to poll.")
             return None


    def get_latest_results(self) -> Optional[Dict[str, Any]]:
        """Get the latest results directly from the query endpoint (doesn't use execution state)."""
        # This method might be less useful now, consider if it's still needed.
        # It bypasses the execution logic and might return stale data if the query hasn't run recently.
        self.logger.info(f"Fetching latest results directly for query {self.query_id} (may be stale)")
        url = f"{self.base_url}/query/{self.query_id}/results"
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            results = response.json()
            # Check the new potential structure for rows based on polling method adjustment
            if not results.get('result', {}).get('rows'): 
                 self.logger.warning("Direct result fetch returned no rows.")
                 return None
            self.logger.debug(f"Retrieved {len(results.get('result', {}).get('rows', []))} results directly")
            return results
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to fetch latest results directly: {str(e)}")
            return None
        except Exception as e:
             self.logger.error(f"Unexpected error fetching latest results directly: {str(e)}")
             return None 