import pytest
from unittest.mock import Mock, patch
from src.telegram_client import TelegramClient

@pytest.mark.asyncio
async def test_send_token_update(telegram_client, mock_dune_response):
    # Mock the bot's send_message method
    telegram_client.bot.send_message = Mock()
    
    # Send token update
    await telegram_client.send_token_update(mock_dune_response["rows"][0])
    
    # Verify message was sent
    telegram_client.bot.send_message.assert_called_once()
    call_args = telegram_client.bot.send_message.call_args[1]
    assert call_args["chat_id"] == telegram_client.channel_id
    assert "Test Token" in call_args["text"]
    assert "TEST" in call_args["text"]
    assert "1.23" in call_args["text"]
    assert call_args["parse_mode"] == "HTML"

@pytest.mark.asyncio
async def test_send_error_message(telegram_client):
    # Mock the bot's send_message method
    telegram_client.bot.send_message = Mock()
    
    # Send error message
    error_message = "Test error message"
    await telegram_client.send_error_message(error_message)
    
    # Verify error message was sent
    telegram_client.bot.send_message.assert_called_once()
    call_args = telegram_client.bot.send_message.call_args[1]
    assert call_args["chat_id"] == telegram_client.channel_id
    assert "Error Alert" in call_args["text"]
    assert error_message in call_args["text"]
    assert call_args["parse_mode"] == "HTML"

def test_format_token_message(telegram_client):
    # Test message formatting
    token_data = {
        "token_name": "Test Token",
        "token_symbol": "TEST",
        "price": "1.23",
        "volume": "1000000",
        "profit": "50000",
        "wallet_address": "0x123...",
        "transaction_hash": "0xabc..."
    }
    
    message = telegram_client._format_token_message(token_data)
    
    # Verify message format
    assert "Test Token" in message
    assert "TEST" in message
    assert "1.23" in message
    assert "1000000" in message
    assert "50000" in message
    assert "0x123..." in message
    assert "0xabc..." in message 