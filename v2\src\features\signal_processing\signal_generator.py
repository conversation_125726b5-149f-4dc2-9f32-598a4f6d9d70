"""
🎯 Signal Generator Service

Multi-factor signal generation with confidence scoring, position sizing,
and signal expiration management following V2 architecture patterns.
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from uuid import uuid4

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import SIGNAL_CONSTANTS, TRADING_CONSTANTS
from ...shared.types import SignalType, SignalStrength, SignalData, MarketData
from ...database.models import Signal

from .technical_analyzer import TechnicalAnalyzer, TechnicalIndicators
from .risk_assessor import RiskAssessor

logger = get_logger(__name__)
settings = get_settings()


class SignalGenerator:
    """
    🎯 Signal Generator Service
    
    Generates trading signals based on:
    - Technical analysis indicators
    - Risk assessment scores
    - Market conditions
    - Volume analysis
    - Multi-factor confirmation
    """
    
    def __init__(self):
        self.logger = logger
        self.technical_analyzer = TechnicalAnalyzer()
        self.risk_assessor = RiskAssessor()
        
        # Signal generation parameters
        self.min_confidence = 0.6  # Minimum confidence for signal generation
        self.signal_expiry_hours = 24  # Default signal expiry
        self.max_position_size = Decimal(str(settings.max_position_size_usd))
        self.default_stop_loss = Decimal(str(settings.default_stop_loss_percent))
        self.default_take_profit = Decimal(str(settings.default_take_profit_percent))
    
    async def generate_signal(
        self,
        token_address: str,
        price_data: List[MarketData],
        current_market_data: MarketData
    ) -> Optional[SignalData]:
        """
        Generate trading signal for a token
        
        Args:
            token_address: Token contract address
            price_data: Historical price data
            current_market_data: Current market data
            
        Returns:
            SignalData object or None if no signal generated
        """
        try:
            self.logger.info(f"Generating signal for token {token_address}")
            
            # Perform technical analysis
            indicators = await self.technical_analyzer.analyze_token(price_data)
            
            # Perform risk assessment
            risk_assessment = await self.risk_assessor.assess_token_risk(
                token_address, current_market_data, price_data
            )
            
            # Generate signal based on analysis
            signal_result = self._analyze_indicators_for_signal(
                indicators, risk_assessment, current_market_data
            )
            
            if not signal_result or signal_result['confidence'] < self.min_confidence:
                self.logger.info(f"No signal generated for {token_address}. Confidence too low.")
                return None
            
            # Create signal data
            signal_data = await self._create_signal_data(
                token_address,
                signal_result,
                indicators,
                risk_assessment,
                current_market_data
            )
            
            # Save signal to database
            signal_doc = Signal(**signal_data.dict())
            await signal_doc.save()
            
            self.logger.info(
                f"Signal generated for {token_address}: "
                f"{signal_data.signal_type} with {signal_data.confidence:.2f} confidence"
            )
            
            return signal_data
            
        except Exception as e:
            self.logger.error(f"Error generating signal for {token_address}: {str(e)}")
            return None
    
    def _analyze_indicators_for_signal(
        self,
        indicators: TechnicalIndicators,
        risk_assessment: Dict[str, Any],
        market_data: MarketData
    ) -> Optional[Dict[str, Any]]:
        """
        Analyze technical indicators to determine signal
        
        Args:
            indicators: Technical analysis results
            risk_assessment: Risk assessment results
            market_data: Current market data
            
        Returns:
            Signal analysis result or None
        """
        try:
            signal_factors = []
            confidence_factors = []
            
            # RSI Analysis
            if indicators.rsi is not None:
                if indicators.rsi < SIGNAL_CONSTANTS.RSI_OVERSOLD:
                    signal_factors.append(('BUY', 'RSI oversold', 0.3))
                    confidence_factors.append(0.2)
                elif indicators.rsi > SIGNAL_CONSTANTS.RSI_OVERBOUGHT:
                    signal_factors.append(('SELL', 'RSI overbought', 0.3))
                    confidence_factors.append(0.2)
            
            # MACD Analysis
            if all([indicators.macd, indicators.macd_signal, indicators.macd_histogram]):
                if indicators.macd > indicators.macd_signal and indicators.macd_histogram > 0:
                    signal_factors.append(('BUY', 'MACD bullish crossover', 0.25))
                    confidence_factors.append(0.15)
                elif indicators.macd < indicators.macd_signal and indicators.macd_histogram < 0:
                    signal_factors.append(('SELL', 'MACD bearish crossover', 0.25))
                    confidence_factors.append(0.15)
            
            # Bollinger Bands Analysis
            if all([indicators.bb_upper, indicators.bb_lower, indicators.bb_middle]):
                current_price = float(market_data.price)
                if current_price < indicators.bb_lower:
                    signal_factors.append(('BUY', 'Price below lower Bollinger Band', 0.2))
                    confidence_factors.append(0.1)
                elif current_price > indicators.bb_upper:
                    signal_factors.append(('SELL', 'Price above upper Bollinger Band', 0.2))
                    confidence_factors.append(0.1)
            
            # Volume Analysis
            if indicators.volume_ratio and indicators.volume_ratio > SIGNAL_CONSTANTS.VOLUME_SPIKE_THRESHOLD:
                signal_factors.append(('CONFIRM', 'High volume confirmation', 0.15))
                confidence_factors.append(0.1)
            
            # Trend Analysis
            if indicators.trend_direction:
                if indicators.trend_direction in ['uptrend', 'strong_uptrend']:
                    signal_factors.append(('BUY', f'Trend: {indicators.trend_direction}', 0.1))
                    confidence_factors.append(0.05)
                elif indicators.trend_direction in ['downtrend', 'strong_downtrend']:
                    signal_factors.append(('SELL', f'Trend: {indicators.trend_direction}', 0.1))
                    confidence_factors.append(0.05)
            
            # Risk Assessment Integration
            risk_score = risk_assessment.get('overall_risk_score', 0.5)
            if risk_score > 0.7:  # High risk
                confidence_factors = [cf * 0.7 for cf in confidence_factors]  # Reduce confidence
            elif risk_score < 0.3:  # Low risk
                confidence_factors = [cf * 1.2 for cf in confidence_factors]  # Increase confidence
            
            # Determine dominant signal
            buy_signals = [sf for sf in signal_factors if sf[0] == 'BUY']
            sell_signals = [sf for sf in signal_factors if sf[0] == 'SELL']
            
            buy_weight = sum(sf[2] for sf in buy_signals)
            sell_weight = sum(sf[2] for sf in sell_signals)
            
            # Determine signal type and strength
            if buy_weight > sell_weight and buy_weight > 0.4:
                signal_type = SignalType.BUY
                strength = self._calculate_signal_strength(buy_weight)
                reasoning = '; '.join([sf[1] for sf in buy_signals])
            elif sell_weight > buy_weight and sell_weight > 0.4:
                signal_type = SignalType.SELL
                strength = self._calculate_signal_strength(sell_weight)
                reasoning = '; '.join([sf[1] for sf in sell_signals])
            else:
                return None  # No clear signal
            
            # Calculate confidence
            confidence = min(0.95, sum(confidence_factors) + max(buy_weight, sell_weight))
            
            return {
                'signal_type': signal_type,
                'strength': strength,
                'confidence': confidence,
                'reasoning': reasoning,
                'supporting_factors': [sf[1] for sf in signal_factors if sf[0] in [signal_type.value, 'CONFIRM']],
                'risk_score': risk_score
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing indicators: {str(e)}")
            return None
    
    def _calculate_signal_strength(self, weight: float) -> SignalStrength:
        """Calculate signal strength based on weight"""
        if weight >= 0.8:
            return SignalStrength.VERY_STRONG
        elif weight >= 0.6:
            return SignalStrength.STRONG
        elif weight >= 0.4:
            return SignalStrength.MODERATE
        else:
            return SignalStrength.WEAK
    
    async def _create_signal_data(
        self,
        token_address: str,
        signal_result: Dict[str, Any],
        indicators: TechnicalIndicators,
        risk_assessment: Dict[str, Any],
        market_data: MarketData
    ) -> SignalData:
        """Create SignalData object from analysis results"""
        
        current_price = market_data.price
        
        # Calculate position size based on risk
        position_size = self._calculate_position_size(
            current_price,
            signal_result['risk_score'],
            signal_result['confidence']
        )
        
        # Calculate stop loss and take profit
        stop_loss, take_profit = self._calculate_stop_loss_take_profit(
            current_price,
            signal_result['signal_type']
        )
        
        # Prepare technical indicators dict
        tech_indicators = {
            'rsi': indicators.rsi,
            'macd': indicators.macd,
            'macd_signal': indicators.macd_signal,
            'macd_histogram': indicators.macd_histogram,
            'bb_upper': indicators.bb_upper,
            'bb_middle': indicators.bb_middle,
            'bb_lower': indicators.bb_lower,
            'volume_ratio': indicators.volume_ratio,
            'momentum_score': indicators.momentum_score,
            'trend_direction': indicators.trend_direction
        }
        
        # Create signal data
        signal_data = SignalData(
            id=str(uuid4()),
            token_address=token_address,
            signal_type=signal_result['signal_type'],
            strength=signal_result['strength'],
            confidence=signal_result['confidence'],
            entry_price=current_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            position_size_usd=position_size,
            reasoning=signal_result['reasoning'],
            indicators=tech_indicators,
            risk_score=signal_result['risk_score'],
            expires_at=datetime.utcnow() + timedelta(hours=self.signal_expiry_hours)
        )
        
        return signal_data
    
    def _calculate_position_size(
        self,
        entry_price: Decimal,
        risk_score: float,
        confidence: float
    ) -> Decimal:
        """Calculate appropriate position size based on risk and confidence"""
        
        # Base position size
        base_size = self.max_position_size
        
        # Adjust for risk (higher risk = smaller position)
        risk_multiplier = Decimal(str(1.0 - (risk_score * 0.5)))
        
        # Adjust for confidence (higher confidence = larger position)
        confidence_multiplier = Decimal(str(confidence))
        
        # Calculate final position size
        position_size = base_size * risk_multiplier * confidence_multiplier
        
        # Ensure minimum and maximum bounds
        min_size = Decimal("100")  # Minimum $100 position
        max_size = self.max_position_size
        
        return max(min_size, min(max_size, position_size))
    
    def _calculate_stop_loss_take_profit(
        self,
        entry_price: Decimal,
        signal_type: SignalType
    ) -> tuple[Optional[Decimal], Optional[Decimal]]:
        """Calculate stop loss and take profit levels"""
        
        if signal_type == SignalType.BUY:
            stop_loss = entry_price * (Decimal("1") - self.default_stop_loss)
            take_profit = entry_price * (Decimal("1") + self.default_take_profit)
        elif signal_type == SignalType.SELL:
            stop_loss = entry_price * (Decimal("1") + self.default_stop_loss)
            take_profit = entry_price * (Decimal("1") - self.default_take_profit)
        else:
            return None, None
        
        return stop_loss, take_profit
    
    async def get_active_signals(self, token_address: Optional[str] = None) -> List[Signal]:
        """Get active signals, optionally filtered by token"""
        try:
            query = {"is_active": True, "expires_at": {"$gt": datetime.utcnow()}}
            if token_address:
                query["token_address"] = token_address
            
            signals = await Signal.find(query).sort("-created_at").to_list()
            return signals
            
        except Exception as e:
            self.logger.error(f"Error fetching active signals: {str(e)}")
            return []
    
    async def expire_old_signals(self) -> int:
        """Expire old signals and return count of expired signals"""
        try:
            result = await Signal.find({
                "is_active": True,
                "expires_at": {"$lt": datetime.utcnow()}
            }).update({"$set": {"is_active": False}})
            
            expired_count = result.modified_count if result else 0
            if expired_count > 0:
                self.logger.info(f"Expired {expired_count} old signals")
            
            return expired_count
            
        except Exception as e:
            self.logger.error(f"Error expiring old signals: {str(e)}")
            return 0
