"""
🏷️ Type Definitions

Comprehensive type definitions following CLEAN_CODE_RULES.md
for type safety and clear data structures.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Union, Any
from pydantic import BaseModel, Field, validator
from dataclasses import dataclass


# 🔄 Enums for Type Safety
class SignalType(str, Enum):
    """Trading signal types"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


class SignalStrength(int, Enum):
    """Signal strength levels"""
    WEAK = 1
    MODERATE = 2
    STRONG = 3
    VERY_STRONG = 4


class TradeStatus(str, Enum):
    """Trade execution status"""
    PENDING = "PENDING"
    EXECUTED = "EXECUTED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class OrderType(str, Enum):
    """Order types"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"


class PortfolioStatus(str, Enum):
    """Portfolio status"""
    ACTIVE = "ACTIVE"
    PAUSED = "PAUSED"
    CLOSED = "CLOSED"


# 🪙 Token Data Models
class TokenData(BaseModel):
    """Token information model"""
    
    address: str = Field(..., description="Token mint address")
    symbol: str = Field(..., description="Token symbol")
    name: str = Field(..., description="Token name")
    decimals: int = Field(..., description="Token decimals")
    price_usd: Optional[Decimal] = Field(None, description="Current price in USD")
    market_cap: Optional[Decimal] = Field(None, description="Market capitalization")
    volume_24h: Optional[Decimal] = Field(None, description="24h trading volume")
    liquidity_usd: Optional[Decimal] = Field(None, description="Available liquidity")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    @validator("address")
    def validate_address(cls, v):
        """Validate Solana address format"""
        if not v or len(v) < 32:
            raise ValueError("Invalid token address")
        return v
    
    class Config:
        json_encoders = {
            Decimal: str,
            datetime: lambda v: v.isoformat()
        }


class TokenMetrics(BaseModel):
    """Token performance metrics"""
    
    token_address: str
    price_change_1h: Optional[Decimal] = None
    price_change_24h: Optional[Decimal] = None
    price_change_7d: Optional[Decimal] = None
    volume_change_24h: Optional[Decimal] = None
    liquidity_change_24h: Optional[Decimal] = None
    holder_count: Optional[int] = None
    transaction_count_24h: Optional[int] = None
    unique_traders_24h: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# 📊 Signal Data Models
class SignalData(BaseModel):
    """Trading signal model"""
    
    id: Optional[str] = Field(None, description="Signal ID")
    token_address: str = Field(..., description="Target token address")
    signal_type: SignalType = Field(..., description="Signal type")
    strength: SignalStrength = Field(..., description="Signal strength")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    entry_price: Decimal = Field(..., description="Recommended entry price")
    stop_loss: Optional[Decimal] = Field(None, description="Stop loss price")
    take_profit: Optional[Decimal] = Field(None, description="Take profit price")
    position_size_usd: Decimal = Field(..., description="Recommended position size")
    reasoning: str = Field(..., description="Signal reasoning")
    indicators: Dict[str, Any] = Field(default_factory=dict, description="Technical indicators")
    risk_score: float = Field(..., ge=0.0, le=1.0, description="Risk assessment")
    expires_at: datetime = Field(..., description="Signal expiration time")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    @validator("confidence", "risk_score")
    def validate_percentage(cls, v):
        """Validate percentage values"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Value must be between 0.0 and 1.0")
        return v


class SignalAnalysis(BaseModel):
    """Signal analysis details"""
    
    signal_id: str
    technical_indicators: Dict[str, float]
    volume_analysis: Dict[str, float]
    liquidity_analysis: Dict[str, float]
    market_sentiment: Dict[str, float]
    risk_factors: List[str]
    supporting_factors: List[str]
    analysis_timestamp: datetime = Field(default_factory=datetime.utcnow)


# 💼 Trading Data Models
class TradeData(BaseModel):
    """Trade execution model"""
    
    id: Optional[str] = Field(None, description="Trade ID")
    signal_id: str = Field(..., description="Source signal ID")
    token_address: str = Field(..., description="Token address")
    order_type: OrderType = Field(..., description="Order type")
    side: SignalType = Field(..., description="Buy or sell")
    quantity: Decimal = Field(..., description="Trade quantity")
    price: Decimal = Field(..., description="Execution price")
    value_usd: Decimal = Field(..., description="Trade value in USD")
    fees: Decimal = Field(default=Decimal("0"), description="Trading fees")
    slippage: Decimal = Field(default=Decimal("0"), description="Price slippage")
    status: TradeStatus = Field(..., description="Trade status")
    transaction_hash: Optional[str] = Field(None, description="Blockchain transaction hash")
    executed_at: Optional[datetime] = Field(None, description="Execution timestamp")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    @validator("quantity", "price", "value_usd")
    def validate_positive_values(cls, v):
        """Validate positive decimal values"""
        if v <= 0:
            raise ValueError("Value must be positive")
        return v


class Position(BaseModel):
    """Trading position model"""
    
    id: Optional[str] = Field(None, description="Position ID")
    token_address: str = Field(..., description="Token address")
    quantity: Decimal = Field(..., description="Position quantity")
    average_price: Decimal = Field(..., description="Average entry price")
    current_price: Optional[Decimal] = Field(None, description="Current market price")
    unrealized_pnl: Optional[Decimal] = Field(None, description="Unrealized P&L")
    realized_pnl: Decimal = Field(default=Decimal("0"), description="Realized P&L")
    stop_loss: Optional[Decimal] = Field(None, description="Stop loss price")
    take_profit: Optional[Decimal] = Field(None, description="Take profit price")
    opened_at: datetime = Field(default_factory=datetime.utcnow)
    closed_at: Optional[datetime] = Field(None, description="Position close time")
    is_open: bool = Field(default=True, description="Position status")


# 📈 Portfolio Data Models
class PortfolioData(BaseModel):
    """Portfolio model"""
    
    id: Optional[str] = Field(None, description="Portfolio ID")
    name: str = Field(..., description="Portfolio name")
    description: Optional[str] = Field(None, description="Portfolio description")
    initial_balance: Decimal = Field(..., description="Starting balance")
    current_balance: Decimal = Field(..., description="Current balance")
    total_pnl: Decimal = Field(default=Decimal("0"), description="Total P&L")
    total_fees: Decimal = Field(default=Decimal("0"), description="Total fees paid")
    positions: List[Position] = Field(default_factory=list, description="Open positions")
    status: PortfolioStatus = Field(default=PortfolioStatus.ACTIVE)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    @property
    def total_value(self) -> Decimal:
        """Calculate total portfolio value"""
        position_value = sum(
            pos.quantity * (pos.current_price or pos.average_price)
            for pos in self.positions if pos.is_open
        )
        return self.current_balance + position_value
    
    @property
    def return_percentage(self) -> Decimal:
        """Calculate portfolio return percentage"""
        if self.initial_balance == 0:
            return Decimal("0")
        return ((self.total_value - self.initial_balance) / self.initial_balance) * 100


class PerformanceMetrics(BaseModel):
    """Portfolio performance metrics"""
    
    portfolio_id: str
    total_return: Decimal
    annualized_return: Decimal
    sharpe_ratio: Optional[Decimal] = None
    max_drawdown: Decimal
    win_rate: Decimal
    profit_factor: Decimal
    total_trades: int
    winning_trades: int
    losing_trades: int
    average_win: Decimal
    average_loss: Decimal
    largest_win: Decimal
    largest_loss: Decimal
    calculated_at: datetime = Field(default_factory=datetime.utcnow)


# 🔍 Market Data Models
class MarketData(BaseModel):
    """Market data snapshot"""
    
    token_address: str
    price: Decimal
    volume_24h: Decimal
    market_cap: Optional[Decimal] = None
    liquidity: Optional[Decimal] = None
    price_change_24h: Optional[Decimal] = None
    volume_change_24h: Optional[Decimal] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class OrderBookData(BaseModel):
    """Order book data"""
    
    token_address: str
    bids: List[Dict[str, Decimal]]  # [{"price": price, "quantity": quantity}]
    asks: List[Dict[str, Decimal]]
    spread: Decimal
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# 🔔 Notification Models
class NotificationData(BaseModel):
    """Notification model"""
    
    id: Optional[str] = Field(None, description="Notification ID")
    type: str = Field(..., description="Notification type")
    title: str = Field(..., description="Notification title")
    message: str = Field(..., description="Notification message")
    priority: int = Field(default=2, description="Priority level")
    data: Dict[str, Any] = Field(default_factory=dict, description="Additional data")
    sent_at: Optional[datetime] = Field(None, description="Send timestamp")
    created_at: datetime = Field(default_factory=datetime.utcnow)


# 🔧 System Models
class HealthCheck(BaseModel):
    """System health check model"""
    
    status: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    services: Dict[str, bool] = Field(default_factory=dict)
    metrics: Dict[str, Any] = Field(default_factory=dict)
    uptime: float
    version: str


class APIResponse(BaseModel):
    """Standard API response model"""
    
    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = None
