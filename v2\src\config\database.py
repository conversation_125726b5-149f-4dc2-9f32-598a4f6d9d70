"""
🗄️ Database Configuration

MongoDB Atlas configuration following DATABASE_PATTERNS.md guidelines
with connection management, optimization, and monitoring.
"""

import asyncio
from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
from beanie import init_beanie

from .settings import get_settings
from .logging_config import get_logger
from ..database.models import *

logger = get_logger(__name__)


class DatabaseConfig:
    """
    🗄️ Database configuration and connection management
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.is_connected = False
        
    async def connect(self) -> None:
        """
        🔌 Establish database connection with optimized settings
        """
        try:
            # Connection options optimized for production
            connection_options = {
                "maxPoolSize": 20 if self.settings.is_production else 10,
                "minPoolSize": 5 if self.settings.is_production else 2,
                "maxIdleTimeMS": 30000,
                "serverSelectionTimeoutMS": 5000,
                "socketTimeoutMS": 45000,
                "connectTimeoutMS": 10000,
                "retryWrites": True,
                "w": "majority",
                "readPreference": "primaryPreferred",
                "heartbeatFrequencyMS": 10000,
                "maxStalenessSeconds": 120,
            }
            
            # Add authentication if credentials are in URI
            self.client = AsyncIOMotorClient(
                self.settings.mongodb_uri,
                **connection_options
            )
            
            # Test connection
            await self.client.admin.command('ping')
            
            # Get database
            self.database = self.client[self.settings.mongodb_db_name]
            
            # Initialize Beanie ODM
            await self._initialize_beanie()
            
            # Create indexes
            await self._create_indexes()
            
            self.is_connected = True
            logger.info(
                "Database connected successfully",
                database=self.settings.mongodb_db_name,
                connection_options=connection_options
            )
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(
                "Database connection failed",
                error=str(e),
                uri_host=self.settings.mongodb_uri.split('@')[-1].split('/')[0] if '@' in self.settings.mongodb_uri else "unknown"
            )
            raise
        except Exception as e:
            logger.error(
                "Unexpected database connection error",
                error=str(e),
                error_type=type(e).__name__
            )
            raise
    
    async def _initialize_beanie(self) -> None:
        """
        🔧 Initialize Beanie ODM with all models
        """
        try:
            # Import all model classes
            from ..database.models.token import Token
            from ..database.models.signal import Signal
            from ..database.models.trade import Trade
            from ..database.models.portfolio import Portfolio
            from ..database.models.user import User
            from ..database.models.query_result import QueryResult
            from ..database.models.performance_metric import PerformanceMetric
            
            # Initialize Beanie
            await init_beanie(
                database=self.database,
                document_models=[
                    Token,
                    Signal,
                    Trade,
                    Portfolio,
                    User,
                    QueryResult,
                    PerformanceMetric
                ]
            )
            
            logger.info("Beanie ODM initialized successfully")
            
        except Exception as e:
            logger.error(
                "Failed to initialize Beanie ODM",
                error=str(e)
            )
            raise
    
    async def _create_indexes(self) -> None:
        """
        📊 Create database indexes for optimal performance
        """
        try:
            # Token indexes
            await self.database.tokens.create_index("address", unique=True)
            await self.database.tokens.create_index([("symbol", 1), ("is_active", 1)])
            await self.database.tokens.create_index([("created_at", -1)])
            
            # Signal indexes
            await self.database.signals.create_index([("token_address", 1), ("created_at", -1)])
            await self.database.signals.create_index([("signal_type", 1), ("strength", 1)])
            await self.database.signals.create_index([("expires_at", 1)])
            
            # Trade indexes
            await self.database.trades.create_index([("signal_id", 1)])
            await self.database.trades.create_index([("token_address", 1), ("executed_at", -1)])
            await self.database.trades.create_index([("status", 1), ("created_at", -1)])
            
            # Portfolio indexes
            await self.database.portfolios.create_index([("user_id", 1), ("status", 1)])
            await self.database.portfolios.create_index([("created_at", -1)])
            
            # Query result indexes
            await self.database.query_results.create_index([("timestamp", -1)])
            await self.database.query_results.create_index([("processed", 1)])
            
            # Performance metric indexes
            await self.database.performance_metrics.create_index([("portfolio_id", 1), ("calculated_at", -1)])
            
            # TTL indexes for cleanup
            await self.database.query_results.create_index(
                [("timestamp", 1)],
                expireAfterSeconds=30 * 24 * 3600  # 30 days
            )
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(
                "Failed to create database indexes",
                error=str(e)
            )
            # Don't raise here as indexes are not critical for startup
    
    async def disconnect(self) -> None:
        """
        🔌 Close database connection
        """
        if self.client:
            self.client.close()
            self.is_connected = False
            logger.info("Database disconnected")
    
    async def health_check(self) -> dict:
        """
        🏥 Perform database health check
        """
        try:
            if not self.is_connected or not self.client:
                return {
                    "status": "unhealthy",
                    "error": "Not connected to database"
                }
            
            # Test basic connectivity
            await self.client.admin.command('ping')
            
            # Get server status
            server_status = await self.client.admin.command('serverStatus')
            
            # Get database stats
            db_stats = await self.database.command('dbStats')
            
            return {
                "status": "healthy",
                "server_version": server_status.get("version"),
                "uptime_seconds": server_status.get("uptime"),
                "connections": server_status.get("connections", {}),
                "database_size_mb": round(db_stats.get("dataSize", 0) / 1024 / 1024, 2),
                "collection_count": db_stats.get("collections", 0),
                "index_count": db_stats.get("indexes", 0)
            }
            
        except Exception as e:
            logger.error(
                "Database health check failed",
                error=str(e)
            )
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    def get_database(self) -> AsyncIOMotorDatabase:
        """
        🗄️ Get database instance
        """
        if not self.database:
            raise RuntimeError("Database not connected")
        return self.database
    
    def get_client(self) -> AsyncIOMotorClient:
        """
        🔌 Get client instance
        """
        if not self.client:
            raise RuntimeError("Database client not initialized")
        return self.client


# Global database instance
database_config = DatabaseConfig()


async def get_database() -> AsyncIOMotorDatabase:
    """
    🗄️ Get database instance (dependency injection)
    """
    return database_config.get_database()


async def connect_database() -> None:
    """
    🚀 Initialize database connection
    """
    await database_config.connect()


async def disconnect_database() -> None:
    """
    🔌 Close database connection
    """
    await database_config.disconnect()


async def database_health_check() -> dict:
    """
    🏥 Database health check endpoint
    """
    return await database_config.health_check()
