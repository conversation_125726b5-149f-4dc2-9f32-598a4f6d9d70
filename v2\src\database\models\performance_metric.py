"""
📊 Performance Metric Database Model

Beanie ODM model for storing portfolio and trading performance metrics
following DATABASE_PATTERNS.md with proper indexing and calculations.
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from beanie import Document, Indexed
from pydantic import Field, validator
from pymongo import IndexModel, ASCENDING, DESCENDING

from .base import BaseDocument


class PerformanceMetric(BaseDocument):
    """
    📊 Performance Metric Model
    
    Stores calculated performance metrics for portfolios and strategies
    following V2 architecture patterns.
    """
    
    # 🎯 Core Metric Data
    portfolio_id: Indexed(str) = Field(..., description="Portfolio ID")
    metric_type: str = Field(..., description="Type of metric (daily, weekly, monthly)")
    calculation_date: Indexed(datetime) = Field(..., description="Metric calculation date")
    
    # 💰 Return Metrics
    total_return: Decimal = Field(..., description="Total return amount")
    total_return_percent: Decimal = Field(..., description="Total return percentage")
    annualized_return: Optional[Decimal] = Field(None, description="Annualized return")
    daily_return: Optional[Decimal] = Field(None, description="Daily return")
    weekly_return: Optional[Decimal] = Field(None, description="Weekly return")
    monthly_return: Optional[Decimal] = Field(None, description="Monthly return")
    
    # 📊 Risk Metrics
    volatility: Optional[Decimal] = Field(None, description="Return volatility")
    sharpe_ratio: Optional[Decimal] = Field(None, description="Sharpe ratio")
    sortino_ratio: Optional[Decimal] = Field(None, description="Sortino ratio")
    calmar_ratio: Optional[Decimal] = Field(None, description="Calmar ratio")
    max_drawdown: Decimal = Field(default=Decimal("0"), description="Maximum drawdown")
    current_drawdown: Decimal = Field(default=Decimal("0"), description="Current drawdown")
    
    # 📈 Trading Metrics
    win_rate: Decimal = Field(default=Decimal("0"), description="Win rate percentage")
    profit_factor: Optional[Decimal] = Field(None, description="Profit factor")
    average_win: Decimal = Field(default=Decimal("0"), description="Average winning trade")
    average_loss: Decimal = Field(default=Decimal("0"), description="Average losing trade")
    largest_win: Decimal = Field(default=Decimal("0"), description="Largest winning trade")
    largest_loss: Decimal = Field(default=Decimal("0"), description="Largest losing trade")
    
    # 📊 Trade Statistics
    total_trades: int = Field(default=0, description="Total number of trades")
    winning_trades: int = Field(default=0, description="Number of winning trades")
    losing_trades: int = Field(default=0, description="Number of losing trades")
    average_trade_duration: Optional[float] = Field(None, description="Average trade duration in hours")
    trades_per_day: Optional[float] = Field(None, description="Average trades per day")
    
    # 💼 Portfolio Metrics
    portfolio_value: Decimal = Field(..., description="Total portfolio value")
    cash_balance: Decimal = Field(..., description="Cash balance")
    invested_amount: Decimal = Field(..., description="Amount invested in positions")
    unrealized_pnl: Decimal = Field(default=Decimal("0"), description="Unrealized P&L")
    realized_pnl: Decimal = Field(default=Decimal("0"), description="Realized P&L")
    
    # 🔍 Risk Management Metrics
    var_95: Optional[Decimal] = Field(None, description="Value at Risk (95%)")
    var_99: Optional[Decimal] = Field(None, description="Value at Risk (99%)")
    expected_shortfall: Optional[Decimal] = Field(None, description="Expected shortfall")
    beta: Optional[Decimal] = Field(None, description="Portfolio beta")
    alpha: Optional[Decimal] = Field(None, description="Portfolio alpha")
    
    # 📊 Benchmark Comparison
    benchmark_return: Optional[Decimal] = Field(None, description="Benchmark return")
    excess_return: Optional[Decimal] = Field(None, description="Excess return over benchmark")
    tracking_error: Optional[Decimal] = Field(None, description="Tracking error")
    information_ratio: Optional[Decimal] = Field(None, description="Information ratio")
    
    # 🔄 Calculation Metadata
    calculation_method: str = Field(default="standard", description="Calculation method used")
    data_points: int = Field(default=0, description="Number of data points used")
    confidence_level: float = Field(default=0.95, description="Confidence level for calculations")
    
    # 📋 Additional Data
    notes: Optional[str] = Field(None, description="Additional notes")
    tags: List[str] = Field(default_factory=list, description="Metric tags")
    raw_data: Dict[str, Any] = Field(default_factory=dict, description="Raw calculation data")
    
    @validator("total_return_percent", "win_rate")
    def validate_percentages(cls, v):
        """Validate percentage values"""
        if v < -100:
            raise ValueError("Percentage cannot be less than -100%")
        return v
    
    @validator("confidence_level")
    def validate_confidence_level(cls, v):
        """Validate confidence level"""
        if not 0.0 < v < 1.0:
            raise ValueError("Confidence level must be between 0 and 1")
        return v
    
    @property
    def is_profitable(self) -> bool:
        """Check if portfolio is profitable"""
        return self.total_return > 0
    
    @property
    def risk_adjusted_return(self) -> Optional[Decimal]:
        """Calculate risk-adjusted return"""
        if self.max_drawdown > 0:
            return self.total_return_percent / self.max_drawdown
        return None
    
    @property
    def metric_age_days(self) -> int:
        """Calculate metric age in days"""
        return (datetime.utcnow() - self.calculation_date).days
    
    def calculate_sharpe_ratio(self, risk_free_rate: Decimal = Decimal("0.02")) -> Optional[Decimal]:
        """
        Calculate Sharpe ratio
        
        Args:
            risk_free_rate: Risk-free rate (default 2%)
            
        Returns:
            Sharpe ratio or None if volatility is zero
        """
        if not self.volatility or self.volatility == 0:
            return None
        
        excess_return = self.annualized_return - risk_free_rate if self.annualized_return else Decimal("0")
        return excess_return / self.volatility
    
    def calculate_sortino_ratio(self, risk_free_rate: Decimal = Decimal("0.02")) -> Optional[Decimal]:
        """
        Calculate Sortino ratio (using downside deviation)
        
        Args:
            risk_free_rate: Risk-free rate (default 2%)
            
        Returns:
            Sortino ratio or None if downside deviation not available
        """
        # This would require daily returns data to calculate downside deviation
        # For now, return None - implement when daily returns are tracked
        return None
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive performance summary
        
        Returns:
            Dictionary with key performance metrics
        """
        return {
            "total_return_percent": float(self.total_return_percent),
            "annualized_return": float(self.annualized_return) if self.annualized_return else None,
            "sharpe_ratio": float(self.sharpe_ratio) if self.sharpe_ratio else None,
            "max_drawdown": float(self.max_drawdown),
            "win_rate": float(self.win_rate),
            "profit_factor": float(self.profit_factor) if self.profit_factor else None,
            "total_trades": self.total_trades,
            "portfolio_value": float(self.portfolio_value),
            "is_profitable": self.is_profitable,
            "risk_adjusted_return": float(self.risk_adjusted_return) if self.risk_adjusted_return else None,
            "calculation_date": self.calculation_date.isoformat(),
            "metric_age_days": self.metric_age_days
        }
    
    class Settings:
        name = "performance_metrics"
        indexes = [
            # Core query indexes
            IndexModel([("portfolio_id", ASCENDING), ("calculation_date", DESCENDING)]),
            IndexModel([("metric_type", ASCENDING), ("calculation_date", DESCENDING)]),
            
            # Performance indexes
            IndexModel([("total_return_percent", DESCENDING)]),
            IndexModel([("sharpe_ratio", DESCENDING)]),
            IndexModel([("win_rate", DESCENDING)]),
            IndexModel([("max_drawdown", ASCENDING)]),
            
            # Risk indexes
            IndexModel([("volatility", ASCENDING)]),
            IndexModel([("var_95", ASCENDING)]),
            
            # Time-based indexes
            IndexModel([("calculation_date", DESCENDING)]),
            IndexModel([("created_at", DESCENDING)]),
            
            # Compound indexes
            IndexModel([
                ("portfolio_id", ASCENDING),
                ("metric_type", ASCENDING),
                ("calculation_date", DESCENDING)
            ]),
            IndexModel([
                ("total_return_percent", DESCENDING),
                ("sharpe_ratio", DESCENDING),
                ("calculation_date", DESCENDING)
            ])
        ]
