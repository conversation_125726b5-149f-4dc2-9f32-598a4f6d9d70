"""
🧪 Signal Processing Tests

Comprehensive test suite for signal processing module including technical analysis,
signal generation, risk assessment, and validation.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, AsyncMock, patch
import numpy as np
import pandas as pd

# Import the modules we want to test
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.features.signal_processing import (
    TechnicalAnalyzer,
    SignalGenerator,
    RiskAssessor,
    SignalValidator
)
from src.shared.types import SignalType, SignalStrength, MarketData, SignalData
from src.database.models import Signal


class TestTechnicalAnalyzer:
    """Test suite for TechnicalAnalyzer"""
    
    @pytest.fixture
    def analyzer(self):
        return TechnicalAnalyzer()
    
    @pytest.fixture
    def sample_price_data(self):
        """Generate sample price data for testing"""
        base_price = 100.0
        data = []
        
        for i in range(50):
            # Create some price movement
            price_change = np.random.normal(0, 0.02)  # 2% volatility
            price = base_price * (1 + price_change)
            base_price = price
            
            market_data = MarketData(
                token_address="test_token",
                price=Decimal(str(price)),
                volume_24h=Decimal(str(1000000 + np.random.normal(0, 100000))),
                market_cap=Decimal(str(price * 1000000)),
                liquidity=Decimal(str(500000)),
                timestamp=datetime.utcnow() - timedelta(hours=50-i)
            )
            data.append(market_data)
        
        return data
    
    @pytest.mark.asyncio
    async def test_analyze_token_success(self, analyzer, sample_price_data):
        """Test successful technical analysis"""
        indicators = await analyzer.analyze_token(sample_price_data)
        
        assert indicators is not None
        assert indicators.rsi is not None
        assert 0 <= indicators.rsi <= 100
        assert indicators.macd is not None
        assert indicators.macd_signal is not None
        assert indicators.bb_upper is not None
        assert indicators.bb_lower is not None
        assert indicators.bb_middle is not None
        assert indicators.volume_sma is not None
        assert indicators.trend_direction is not None
        assert indicators.momentum_score is not None
        assert 0 <= indicators.momentum_score <= 100
    
    @pytest.mark.asyncio
    async def test_analyze_token_insufficient_data(self, analyzer):
        """Test analysis with insufficient data"""
        # Only 5 data points (need at least 26 for MACD)
        short_data = []
        for i in range(5):
            market_data = MarketData(
                token_address="test_token",
                price=Decimal("100"),
                volume_24h=Decimal("1000000"),
                timestamp=datetime.utcnow() - timedelta(hours=5-i)
            )
            short_data.append(market_data)
        
        indicators = await analyzer.analyze_token(short_data)
        
        # Should return empty indicators due to insufficient data
        assert indicators.rsi is None
        assert indicators.macd is None
    
    def test_calculate_rsi(self, analyzer):
        """Test RSI calculation"""
        # Create price series with known pattern
        prices = pd.Series([100, 102, 101, 103, 105, 104, 106, 108, 107, 109, 111, 110, 112, 114, 113])
        
        rsi = analyzer._calculate_rsi(prices)
        
        assert rsi is not None
        assert 0 <= rsi <= 100
    
    def test_calculate_macd(self, analyzer):
        """Test MACD calculation"""
        # Create price series
        prices = pd.Series(np.random.randn(50).cumsum() + 100)
        
        macd_data = analyzer._calculate_macd(prices)
        
        assert macd_data['macd'] is not None
        assert macd_data['signal'] is not None
        assert macd_data['histogram'] is not None
    
    def test_calculate_bollinger_bands(self, analyzer):
        """Test Bollinger Bands calculation"""
        prices = pd.Series(np.random.randn(30).cumsum() + 100)
        
        bb_data = analyzer._calculate_bollinger_bands(prices)
        
        assert bb_data['upper'] is not None
        assert bb_data['middle'] is not None
        assert bb_data['lower'] is not None
        assert bb_data['width'] is not None
        assert bb_data['upper'] > bb_data['middle'] > bb_data['lower']


class TestSignalGenerator:
    """Test suite for SignalGenerator"""
    
    @pytest.fixture
    def generator(self):
        return SignalGenerator()
    
    @pytest.fixture
    def mock_market_data(self):
        return MarketData(
            token_address="test_token",
            price=Decimal("100"),
            volume_24h=Decimal("1000000"),
            market_cap=Decimal("100000000"),
            liquidity=Decimal("500000"),
            timestamp=datetime.utcnow()
        )
    
    @pytest.fixture
    def sample_price_history(self):
        """Generate sample price history"""
        data = []
        base_price = 100.0
        
        for i in range(50):
            price_change = np.random.normal(0, 0.01)
            price = base_price * (1 + price_change)
            base_price = price
            
            market_data = MarketData(
                token_address="test_token",
                price=Decimal(str(price)),
                volume_24h=Decimal("1000000"),
                timestamp=datetime.utcnow() - timedelta(hours=50-i)
            )
            data.append(market_data)
        
        return data
    
    @pytest.mark.asyncio
    async def test_generate_signal_success(self, generator, sample_price_history, mock_market_data):
        """Test successful signal generation"""
        with patch.object(generator.technical_analyzer, 'analyze_token') as mock_analyze, \
             patch.object(generator.risk_assessor, 'assess_token_risk') as mock_risk, \
             patch('src.database.models.Signal.save') as mock_save:
            
            # Mock technical analysis with strong buy signal
            mock_analyze.return_value = Mock(
                rsi=25,  # Oversold
                macd=0.5,
                macd_signal=0.3,
                macd_histogram=0.2,
                bb_upper=105,
                bb_middle=100,
                bb_lower=95,
                volume_ratio=2.0,  # High volume
                trend_direction="uptrend",
                momentum_score=75
            )
            
            # Mock risk assessment
            mock_risk.return_value = {
                'overall_risk_score': 0.3,  # Low risk
                'liquidity_risk': 0.2,
                'volatility_risk': 0.3,
                'market_risk': 0.4,
                'risk_factors': []
            }
            
            mock_save.return_value = None
            
            signal = await generator.generate_signal(
                "test_token",
                sample_price_history,
                mock_market_data
            )
            
            assert signal is not None
            assert signal.signal_type == SignalType.BUY
            assert signal.confidence >= 0.6  # Should meet minimum confidence
            assert signal.entry_price == mock_market_data.price
            assert signal.position_size_usd > 0
            assert signal.stop_loss is not None
            assert signal.take_profit is not None
    
    @pytest.mark.asyncio
    async def test_generate_signal_low_confidence(self, generator, sample_price_history, mock_market_data):
        """Test signal generation with low confidence"""
        with patch.object(generator.technical_analyzer, 'analyze_token') as mock_analyze, \
             patch.object(generator.risk_assessor, 'assess_token_risk') as mock_risk:
            
            # Mock weak signals
            mock_analyze.return_value = Mock(
                rsi=50,  # Neutral
                macd=0.1,
                macd_signal=0.1,
                macd_histogram=0.0,
                bb_upper=105,
                bb_middle=100,
                bb_lower=95,
                volume_ratio=1.0,  # Normal volume
                trend_direction="sideways",
                momentum_score=50
            )
            
            mock_risk.return_value = {
                'overall_risk_score': 0.8,  # High risk
                'risk_factors': ['High volatility']
            }
            
            signal = await generator.generate_signal(
                "test_token",
                sample_price_history,
                mock_market_data
            )
            
            # Should return None due to low confidence
            assert signal is None
    
    def test_calculate_signal_strength(self, generator):
        """Test signal strength calculation"""
        assert generator._calculate_signal_strength(0.9) == SignalStrength.VERY_STRONG
        assert generator._calculate_signal_strength(0.7) == SignalStrength.STRONG
        assert generator._calculate_signal_strength(0.5) == SignalStrength.MODERATE
        assert generator._calculate_signal_strength(0.3) == SignalStrength.WEAK
    
    def test_calculate_position_size(self, generator):
        """Test position size calculation"""
        entry_price = Decimal("100")
        risk_score = 0.3  # Low risk
        confidence = 0.8  # High confidence
        
        position_size = generator._calculate_position_size(entry_price, risk_score, confidence)
        
        assert position_size > 0
        assert position_size <= generator.max_position_size
    
    def test_calculate_stop_loss_take_profit_buy(self, generator):
        """Test stop loss and take profit calculation for buy signal"""
        entry_price = Decimal("100")
        
        stop_loss, take_profit = generator._calculate_stop_loss_take_profit(
            entry_price, SignalType.BUY
        )
        
        assert stop_loss < entry_price  # Stop loss below entry for buy
        assert take_profit > entry_price  # Take profit above entry for buy
    
    def test_calculate_stop_loss_take_profit_sell(self, generator):
        """Test stop loss and take profit calculation for sell signal"""
        entry_price = Decimal("100")
        
        stop_loss, take_profit = generator._calculate_stop_loss_take_profit(
            entry_price, SignalType.SELL
        )
        
        assert stop_loss > entry_price  # Stop loss above entry for sell
        assert take_profit < entry_price  # Take profit below entry for sell


class TestRiskAssessor:
    """Test suite for RiskAssessor"""
    
    @pytest.fixture
    def assessor(self):
        return RiskAssessor()
    
    @pytest.fixture
    def high_liquidity_market_data(self):
        return MarketData(
            token_address="test_token",
            price=Decimal("100"),
            volume_24h=Decimal("1000000"),
            market_cap=Decimal("100000000"),
            liquidity=Decimal("1000000"),  # High liquidity
            timestamp=datetime.utcnow()
        )
    
    @pytest.fixture
    def low_liquidity_market_data(self):
        return MarketData(
            token_address="test_token",
            price=Decimal("100"),
            volume_24h=Decimal("10000"),
            market_cap=Decimal("1000000"),
            liquidity=Decimal("30000"),  # Low liquidity
            timestamp=datetime.utcnow()
        )
    
    @pytest.mark.asyncio
    async def test_assess_token_risk_high_liquidity(self, assessor, high_liquidity_market_data):
        """Test risk assessment for high liquidity token"""
        price_history = []
        for i in range(30):
            data = MarketData(
                token_address="test_token",
                price=Decimal(str(100 + np.random.normal(0, 1))),  # Low volatility
                volume_24h=Decimal("1000000"),
                timestamp=datetime.utcnow() - timedelta(hours=30-i)
            )
            price_history.append(data)
        
        risk_assessment = await assessor.assess_token_risk(
            "test_token",
            high_liquidity_market_data,
            price_history
        )
        
        assert risk_assessment['overall_risk_score'] < 0.5  # Should be low risk
        assert risk_assessment['liquidity_risk'] < 0.5
        assert risk_assessment['risk_level'] in ['low', 'very_low', 'moderate']
        assert 'liquidity_usd' in risk_assessment
        assert 'volume_24h_usd' in risk_assessment
    
    @pytest.mark.asyncio
    async def test_assess_token_risk_low_liquidity(self, assessor, low_liquidity_market_data):
        """Test risk assessment for low liquidity token"""
        price_history = []
        for i in range(30):
            data = MarketData(
                token_address="test_token",
                price=Decimal(str(100 + np.random.normal(0, 10))),  # High volatility
                volume_24h=Decimal("10000"),
                timestamp=datetime.utcnow() - timedelta(hours=30-i)
            )
            price_history.append(data)
        
        risk_assessment = await assessor.assess_token_risk(
            "test_token",
            low_liquidity_market_data,
            price_history
        )
        
        assert risk_assessment['overall_risk_score'] > 0.5  # Should be high risk
        assert risk_assessment['liquidity_risk'] > 0.5
        assert risk_assessment['risk_level'] in ['high', 'very_high']
        assert len(risk_assessment['risk_factors']) > 0
    
    def test_assess_liquidity_risk_high_liquidity(self, assessor):
        """Test liquidity risk assessment for high liquidity"""
        market_data = MarketData(
            token_address="test_token",
            price=Decimal("100"),
            volume_24h=Decimal("1000000"),
            liquidity=Decimal("2000000")  # Very high liquidity
        )
        
        liquidity_risk = assessor._assess_liquidity_risk(market_data)
        
        assert liquidity_risk < 0.3  # Should be low risk
    
    def test_assess_liquidity_risk_low_liquidity(self, assessor):
        """Test liquidity risk assessment for low liquidity"""
        market_data = MarketData(
            token_address="test_token",
            price=Decimal("100"),
            volume_24h=Decimal("10000"),
            liquidity=Decimal("20000")  # Very low liquidity
        )
        
        liquidity_risk = assessor._assess_liquidity_risk(market_data)
        
        assert liquidity_risk > 0.7  # Should be high risk
    
    def test_assess_volatility_risk_low_volatility(self, assessor):
        """Test volatility risk assessment for low volatility"""
        # Create low volatility price history
        price_history = []
        base_price = 100.0
        
        for i in range(30):
            price_change = np.random.normal(0, 0.005)  # 0.5% volatility
            price = base_price * (1 + price_change)
            base_price = price
            
            data = MarketData(
                token_address="test_token",
                price=Decimal(str(price)),
                timestamp=datetime.utcnow() - timedelta(hours=30-i)
            )
            price_history.append(data)
        
        volatility_risk = assessor._assess_volatility_risk(price_history)
        
        assert volatility_risk < 0.5  # Should be low risk
    
    def test_assess_volatility_risk_high_volatility(self, assessor):
        """Test volatility risk assessment for high volatility"""
        # Create high volatility price history
        price_history = []
        base_price = 100.0
        
        for i in range(30):
            price_change = np.random.normal(0, 0.15)  # 15% volatility
            price = base_price * (1 + price_change)
            base_price = price
            
            data = MarketData(
                token_address="test_token",
                price=Decimal(str(price)),
                timestamp=datetime.utcnow() - timedelta(hours=30-i)
            )
            price_history.append(data)
        
        volatility_risk = assessor._assess_volatility_risk(price_history)
        
        assert volatility_risk > 0.7  # Should be high risk


class TestSignalValidator:
    """Test suite for SignalValidator"""
    
    @pytest.fixture
    def validator(self):
        return SignalValidator()
    
    @pytest.fixture
    def valid_signal_data(self):
        return SignalData(
            id="test_signal_123",
            token_address="test_token",
            signal_type=SignalType.BUY,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=Decimal("100"),
            stop_loss=Decimal("95"),
            take_profit=Decimal("115"),
            position_size_usd=Decimal("1000"),
            reasoning="Strong technical indicators",
            risk_score=0.3,
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
    
    @pytest.fixture
    def market_data(self):
        return MarketData(
            token_address="test_token",
            price=Decimal("100"),
            volume_24h=Decimal("1000000"),
            liquidity=Decimal("500000"),
            timestamp=datetime.utcnow()
        )
    
    @pytest.mark.asyncio
    async def test_validate_signal_success(self, validator, valid_signal_data, market_data):
        """Test successful signal validation"""
        price_history = []
        for i in range(50):
            data = MarketData(
                token_address="test_token",
                price=Decimal(str(100 + np.random.normal(0, 2))),
                volume_24h=Decimal("1000000"),
                timestamp=datetime.utcnow() - timedelta(hours=50-i)
            )
            price_history.append(data)
        
        with patch.object(validator, '_check_signal_frequency') as mock_frequency, \
             patch.object(validator, '_check_historical_performance') as mock_historical, \
             patch.object(validator, '_check_conflicting_signals') as mock_conflicts:
            
            mock_frequency.return_value = {'valid': True}
            mock_historical.return_value = {
                'valid': True,
                'accuracy': 0.7,
                'confidence_boost': 0.05
            }
            mock_conflicts.return_value = {'has_conflicts': False}
            
            result = await validator.validate_signal(valid_signal_data, market_data, price_history)
            
            assert result.is_valid
            assert result.validation_score >= validator.min_validation_score
            assert len(result.validation_factors) > 0
    
    @pytest.mark.asyncio
    async def test_validate_signal_frequency_violation(self, validator, valid_signal_data, market_data):
        """Test signal validation with frequency violation"""
        with patch.object(validator, '_check_signal_frequency') as mock_frequency:
            mock_frequency.return_value = {
                'valid': False,
                'reason': 'Too many signals today'
            }
            
            result = await validator.validate_signal(valid_signal_data, market_data, [])
            
            assert not result.is_valid
            assert 'Too many signals today' in result.rejection_reasons
    
    def test_validate_market_conditions_insufficient_liquidity(self, validator, market_data):
        """Test market conditions validation with insufficient liquidity"""
        low_liquidity_data = MarketData(
            token_address="test_token",
            price=Decimal("100"),
            volume_24h=Decimal("1000"),
            liquidity=Decimal("10000"),  # Very low liquidity
            timestamp=datetime.utcnow()
        )
        
        result = validator._validate_market_conditions(low_liquidity_data, [])
        
        assert not result['valid']
        assert 'liquidity' in result['warning'].lower()
    
    def test_check_signal_consistency_buy_signal(self, validator, valid_signal_data, market_data):
        """Test signal consistency check for buy signal"""
        result = validator._check_signal_consistency(valid_signal_data, market_data)
        
        assert result['valid']  # Should be valid with correct stop loss/take profit
    
    def test_check_signal_consistency_invalid_stop_loss(self, validator, market_data):
        """Test signal consistency check with invalid stop loss"""
        invalid_signal = SignalData(
            id="test_signal_123",
            token_address="test_token",
            signal_type=SignalType.BUY,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=Decimal("100"),
            stop_loss=Decimal("105"),  # Invalid: stop loss above entry for buy
            take_profit=Decimal("115"),
            position_size_usd=Decimal("1000"),
            reasoning="Test signal",
            risk_score=0.3,
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        
        result = validator._check_signal_consistency(invalid_signal, market_data)
        
        assert not result['valid'] or result['confidence_penalty'] < -0.1
    
    def test_validate_signal_timing_expired(self, validator, market_data):
        """Test signal timing validation for expired signal"""
        expired_signal = SignalData(
            id="test_signal_123",
            token_address="test_token",
            signal_type=SignalType.BUY,
            strength=SignalStrength.STRONG,
            confidence=0.8,
            entry_price=Decimal("100"),
            stop_loss=Decimal("95"),
            take_profit=Decimal("115"),
            position_size_usd=Decimal("1000"),
            reasoning="Test signal",
            risk_score=0.3,
            expires_at=datetime.utcnow() - timedelta(hours=1)  # Expired
        )
        
        result = validator._validate_signal_timing(expired_signal, market_data)
        
        assert not result['valid']


# Integration Tests
class TestSignalProcessingIntegration:
    """Integration tests for signal processing workflow"""
    
    @pytest.mark.asyncio
    async def test_full_signal_generation_workflow(self):
        """Test complete signal generation and validation workflow"""
        # This would test the full workflow from data input to signal output
        # Including technical analysis, risk assessment, signal generation, and validation
        pass
    
    @pytest.mark.asyncio
    async def test_signal_database_operations(self):
        """Test signal database save/retrieve operations"""
        # This would test database operations for signals
        pass


if __name__ == "__main__":
    pytest.main([__file__])
