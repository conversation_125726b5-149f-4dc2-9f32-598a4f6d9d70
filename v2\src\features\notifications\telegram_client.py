"""
📱 Enhanced Telegram Client

Advanced Telegram integration with interactive buttons, message formatting,
user management, and signal alerts following V2 architecture patterns.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
import httpx
from dataclasses import dataclass

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.types import SignalData, SignalType, SignalStrength

logger = get_logger(__name__)
settings = get_settings()


@dataclass
class TelegramMessage:
    """Telegram message data structure"""
    chat_id: Union[str, int]
    text: str
    parse_mode: str = "HTML"
    reply_markup: Optional[Dict[str, Any]] = None
    disable_web_page_preview: bool = True
    disable_notification: bool = False


@dataclass
class InlineButton:
    """Inline keyboard button"""
    text: str
    callback_data: Optional[str] = None
    url: Optional[str] = None


class TelegramClient:
    """
    📱 Enhanced Telegram Client
    
    Provides advanced Telegram integration with:
    - Interactive inline keyboards
    - Message formatting and templates
    - User subscription management
    - Signal alerts and notifications
    - Command handling and responses
    """
    
    def __init__(self):
        self.logger = logger
        self.bot_token = settings.telegram_bot_token
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        self.session = None
        
        # Rate limiting
        self.rate_limit_delay = 1.0  # Seconds between messages
        self.last_message_time = {}
        
        # Message templates
        self.templates = {
            "signal_alert": self._get_signal_alert_template(),
            "portfolio_update": self._get_portfolio_update_template(),
            "trade_execution": self._get_trade_execution_template(),
            "welcome": self._get_welcome_template(),
            "help": self._get_help_template()
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.aclose()
    
    async def send_message(
        self,
        chat_id: Union[str, int],
        text: str,
        parse_mode: str = "HTML",
        reply_markup: Optional[Dict[str, Any]] = None,
        disable_web_page_preview: bool = True,
        disable_notification: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        Send a message to Telegram chat
        
        Args:
            chat_id: Chat ID or username
            text: Message text
            parse_mode: Message parsing mode (HTML, Markdown)
            reply_markup: Inline keyboard markup
            disable_web_page_preview: Disable link previews
            disable_notification: Send silently
            
        Returns:
            Telegram API response or None if failed
        """
        try:
            # Rate limiting
            await self._apply_rate_limit(chat_id)
            
            # Prepare message data
            message_data = {
                "chat_id": chat_id,
                "text": text[:4096],  # Telegram message limit
                "parse_mode": parse_mode,
                "disable_web_page_preview": disable_web_page_preview,
                "disable_notification": disable_notification
            }
            
            if reply_markup:
                message_data["reply_markup"] = json.dumps(reply_markup)
            
            # Send message
            if not self.session:
                await self.__aenter__()
            
            response = await self.session.post(
                f"{self.base_url}/sendMessage",
                data=message_data
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("ok"):
                    self.logger.info(f"Message sent successfully to {chat_id}")
                    return result.get("result")
                else:
                    self.logger.error(f"Telegram API error: {result.get('description')}")
                    return None
            else:
                self.logger.error(f"HTTP error {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error sending Telegram message: {str(e)}")
            return None
    
    async def send_signal_alert(
        self,
        chat_id: Union[str, int],
        signal: SignalData,
        include_actions: bool = True
    ) -> Optional[Dict[str, Any]]:
        """
        Send a signal alert with interactive buttons
        
        Args:
            chat_id: Chat ID to send to
            signal: Signal data
            include_actions: Include action buttons
            
        Returns:
            Telegram API response
        """
        try:
            # Format signal message
            message_text = self._format_signal_message(signal)
            
            # Create inline keyboard
            reply_markup = None
            if include_actions:
                reply_markup = self._create_signal_keyboard(signal)
            
            return await self.send_message(
                chat_id=chat_id,
                text=message_text,
                reply_markup=reply_markup
            )
            
        except Exception as e:
            self.logger.error(f"Error sending signal alert: {str(e)}")
            return None
    
    async def send_portfolio_update(
        self,
        chat_id: Union[str, int],
        portfolio_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Send portfolio performance update"""
        try:
            message_text = self._format_portfolio_message(portfolio_data)
            
            # Create portfolio action buttons
            reply_markup = self._create_portfolio_keyboard(portfolio_data["portfolio_id"])
            
            return await self.send_message(
                chat_id=chat_id,
                text=message_text,
                reply_markup=reply_markup
            )
            
        except Exception as e:
            self.logger.error(f"Error sending portfolio update: {str(e)}")
            return None
    
    async def send_trade_notification(
        self,
        chat_id: Union[str, int],
        trade_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Send trade execution notification"""
        try:
            message_text = self._format_trade_message(trade_data)
            
            return await self.send_message(
                chat_id=chat_id,
                text=message_text
            )
            
        except Exception as e:
            self.logger.error(f"Error sending trade notification: {str(e)}")
            return None
    
    async def handle_callback_query(
        self,
        callback_query: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Handle inline keyboard button callbacks
        
        Args:
            callback_query: Telegram callback query data
            
        Returns:
            Response data
        """
        try:
            callback_data = callback_query.get("data", "")
            chat_id = callback_query["message"]["chat"]["id"]
            message_id = callback_query["message"]["message_id"]
            
            # Parse callback data
            action_parts = callback_data.split(":")
            if len(action_parts) < 2:
                return None
            
            action_type = action_parts[0]
            action_id = action_parts[1]
            
            # Handle different action types
            if action_type == "signal":
                return await self._handle_signal_action(chat_id, message_id, action_id, action_parts[2:])
            elif action_type == "portfolio":
                return await self._handle_portfolio_action(chat_id, message_id, action_id, action_parts[2:])
            elif action_type == "subscribe":
                return await self._handle_subscription_action(chat_id, action_id)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error handling callback query: {str(e)}")
            return None
    
    def _format_signal_message(self, signal: SignalData) -> str:
        """Format signal data into Telegram message"""
        # Signal strength emoji
        strength_emoji = {
            SignalStrength.WEAK: "🟡",
            SignalStrength.MODERATE: "🟠", 
            SignalStrength.STRONG: "🔴",
            SignalStrength.VERY_STRONG: "🚨"
        }
        
        # Signal type emoji
        type_emoji = {
            SignalType.BUY: "🟢",
            SignalType.SELL: "🔴",
            SignalType.HOLD: "🟡"
        }
        
        message = f"""
{type_emoji.get(signal.signal_type, '⚪')} <b>{signal.signal_type.value} SIGNAL</b> {strength_emoji.get(signal.strength, '⚪')}

🪙 <b>Token:</b> <code>{signal.token_address[:8]}...{signal.token_address[-8:]}</code>
💪 <b>Strength:</b> {signal.strength.value}
🎯 <b>Confidence:</b> {signal.confidence:.1%}
💰 <b>Entry Price:</b> ${signal.entry_price:.6f}
📊 <b>Position Size:</b> ${signal.position_size_usd:,.2f}

🛡️ <b>Risk Management:</b>
• Stop Loss: ${signal.stop_loss:.6f} ({((signal.stop_loss - signal.entry_price) / signal.entry_price * 100):+.1f}%)
• Take Profit: ${signal.take_profit:.6f} ({((signal.take_profit - signal.entry_price) / signal.entry_price * 100):+.1f}%)
• Risk Score: {signal.risk_score:.1%}

📝 <b>Analysis:</b>
{signal.reasoning}

⏰ <b>Expires:</b> {signal.expires_at.strftime('%Y-%m-%d %H:%M UTC')}
🕐 <b>Generated:</b> {signal.created_at.strftime('%Y-%m-%d %H:%M UTC')}
"""
        return message.strip()
    
    def _format_portfolio_message(self, portfolio_data: Dict[str, Any]) -> str:
        """Format portfolio data into Telegram message"""
        portfolio = portfolio_data["portfolio"]
        value_breakdown = portfolio_data.get("value_breakdown", {})
        
        # Performance emoji
        pnl_emoji = "🟢" if portfolio["total_pnl"] > 0 else "🔴" if portfolio["total_pnl"] < 0 else "⚪"
        
        message = f"""
💼 <b>Portfolio Update: {portfolio['name']}</b>

💰 <b>Balance Overview:</b>
• Total Value: ${portfolio['total_value']:,.2f}
• Cash Balance: ${value_breakdown.get('cash_balance', 0):,.2f}
• Position Value: ${value_breakdown.get('position_value', 0):,.2f}

📈 <b>Performance:</b>
{pnl_emoji} Total P&L: ${portfolio['total_pnl']:,.2f} ({portfolio['total_return_percent']:+.2f}%)
• Realized P&L: ${value_breakdown.get('realized_pnl', 0):,.2f}
• Unrealized P&L: ${value_breakdown.get('unrealized_pnl', 0):,.2f}

📊 <b>Statistics:</b>
• Total Trades: {portfolio['total_trades']}
• Win Rate: {portfolio['win_rate']:.1f}%
• Active Positions: {portfolio['position_count']}

🕐 <b>Last Updated:</b> {portfolio['last_updated_at'][:19]} UTC
"""
        return message.strip()
    
    def _format_trade_message(self, trade_data: Dict[str, Any]) -> str:
        """Format trade data into Telegram message"""
        side_emoji = "🟢" if trade_data["side"] == "BUY" else "🔴"
        
        message = f"""
{side_emoji} <b>Trade Executed</b>

🪙 <b>Token:</b> <code>{trade_data['token_address'][:8]}...{trade_data['token_address'][-8:]}</code>
📊 <b>Side:</b> {trade_data['side']}
💰 <b>Quantity:</b> {trade_data['quantity']:,.6f}
💵 <b>Price:</b> ${trade_data['price']:,.6f}
💸 <b>Value:</b> ${trade_data['value_usd']:,.2f}
🏦 <b>Fees:</b> ${trade_data['fees']:,.2f}

🕐 <b>Executed:</b> {trade_data['execution_time'][:19]} UTC
"""
        return message.strip()
    
    def _create_signal_keyboard(self, signal: SignalData) -> Dict[str, Any]:
        """Create inline keyboard for signal actions"""
        keyboard = [
            [
                {"text": "📊 View Details", "callback_data": f"signal:{signal.id}:details"},
                {"text": "💼 Execute Trade", "callback_data": f"signal:{signal.id}:execute"}
            ],
            [
                {"text": "📈 View Chart", "callback_data": f"signal:{signal.id}:chart"},
                {"text": "🔍 Risk Analysis", "callback_data": f"signal:{signal.id}:risk"}
            ],
            [
                {"text": "⭐ Save Signal", "callback_data": f"signal:{signal.id}:save"},
                {"text": "🔕 Mute Token", "callback_data": f"signal:{signal.id}:mute"}
            ]
        ]
        
        return {"inline_keyboard": keyboard}
    
    def _create_portfolio_keyboard(self, portfolio_id: str) -> Dict[str, Any]:
        """Create inline keyboard for portfolio actions"""
        keyboard = [
            [
                {"text": "📊 Full Report", "callback_data": f"portfolio:{portfolio_id}:report"},
                {"text": "📈 Performance", "callback_data": f"portfolio:{portfolio_id}:performance"}
            ],
            [
                {"text": "💼 Positions", "callback_data": f"portfolio:{portfolio_id}:positions"},
                {"text": "📋 Trade History", "callback_data": f"portfolio:{portfolio_id}:trades"}
            ],
            [
                {"text": "⚙️ Settings", "callback_data": f"portfolio:{portfolio_id}:settings"}
            ]
        ]
        
        return {"inline_keyboard": keyboard}
    
    async def _handle_signal_action(
        self,
        chat_id: Union[str, int],
        message_id: int,
        signal_id: str,
        action_params: List[str]
    ) -> Optional[Dict[str, Any]]:
        """Handle signal-related button actions"""
        try:
            if not action_params:
                return None
            
            action = action_params[0]
            
            if action == "details":
                # Send detailed signal information
                response_text = f"📊 Detailed analysis for signal {signal_id[:8]}...\n\n"
                response_text += "This would show comprehensive technical analysis, indicators, and market conditions."
                
            elif action == "execute":
                # Initiate trade execution
                response_text = f"💼 Executing trade for signal {signal_id[:8]}...\n\n"
                response_text += "Trade execution initiated. You will receive a confirmation shortly."
                
            elif action == "chart":
                # Show price chart
                response_text = f"📈 Price chart for signal {signal_id[:8]}...\n\n"
                response_text += "Chart analysis would be displayed here with technical indicators."
                
            elif action == "risk":
                # Show risk analysis
                response_text = f"🔍 Risk analysis for signal {signal_id[:8]}...\n\n"
                response_text += "Detailed risk assessment including liquidity, volatility, and market conditions."
                
            elif action == "save":
                # Save signal to watchlist
                response_text = f"⭐ Signal {signal_id[:8]}... saved to your watchlist!"
                
            elif action == "mute":
                # Mute notifications for this token
                response_text = f"🔕 Notifications muted for this token."
                
            else:
                response_text = "❓ Unknown action."
            
            # Send response
            return await self.send_message(chat_id, response_text)
            
        except Exception as e:
            self.logger.error(f"Error handling signal action: {str(e)}")
            return None
    
    async def _handle_portfolio_action(
        self,
        chat_id: Union[str, int],
        message_id: int,
        portfolio_id: str,
        action_params: List[str]
    ) -> Optional[Dict[str, Any]]:
        """Handle portfolio-related button actions"""
        try:
            if not action_params:
                return None
            
            action = action_params[0]
            
            if action == "report":
                response_text = f"📊 Full portfolio report for {portfolio_id[:8]}...\n\n"
                response_text += "Comprehensive portfolio analysis would be displayed here."
                
            elif action == "performance":
                response_text = f"📈 Performance metrics for {portfolio_id[:8]}...\n\n"
                response_text += "Detailed performance analytics including Sharpe ratio, drawdown, etc."
                
            elif action == "positions":
                response_text = f"💼 Current positions in {portfolio_id[:8]}...\n\n"
                response_text += "List of all active positions with P&L and risk metrics."
                
            elif action == "trades":
                response_text = f"📋 Trade history for {portfolio_id[:8]}...\n\n"
                response_text += "Recent trade executions and performance summary."
                
            elif action == "settings":
                response_text = f"⚙️ Portfolio settings for {portfolio_id[:8]}...\n\n"
                response_text += "Risk management settings and notification preferences."
                
            else:
                response_text = "❓ Unknown action."
            
            return await self.send_message(chat_id, response_text)
            
        except Exception as e:
            self.logger.error(f"Error handling portfolio action: {str(e)}")
            return None
    
    async def _handle_subscription_action(
        self,
        chat_id: Union[str, int],
        action: str
    ) -> Optional[Dict[str, Any]]:
        """Handle subscription-related actions"""
        try:
            if action == "signals":
                response_text = "🔔 You are now subscribed to signal alerts!"
            elif action == "portfolio":
                response_text = "📊 You are now subscribed to portfolio updates!"
            elif action == "trades":
                response_text = "💼 You are now subscribed to trade notifications!"
            else:
                response_text = "❓ Unknown subscription type."
            
            return await self.send_message(chat_id, response_text)
            
        except Exception as e:
            self.logger.error(f"Error handling subscription action: {str(e)}")
            return None
    
    async def _apply_rate_limit(self, chat_id: Union[str, int]) -> None:
        """Apply rate limiting to prevent spam"""
        current_time = datetime.utcnow()
        last_time = self.last_message_time.get(str(chat_id))
        
        if last_time:
            time_diff = (current_time - last_time).total_seconds()
            if time_diff < self.rate_limit_delay:
                await asyncio.sleep(self.rate_limit_delay - time_diff)
        
        self.last_message_time[str(chat_id)] = current_time
    
    def _get_signal_alert_template(self) -> str:
        """Get signal alert message template"""
        return """
🚨 <b>SIGNAL ALERT</b> 🚨

{signal_details}

{action_buttons}
"""
    
    def _get_portfolio_update_template(self) -> str:
        """Get portfolio update message template"""
        return """
💼 <b>Portfolio Update</b>

{portfolio_details}

{action_buttons}
"""
    
    def _get_trade_execution_template(self) -> str:
        """Get trade execution message template"""
        return """
✅ <b>Trade Executed</b>

{trade_details}
"""
    
    def _get_welcome_template(self) -> str:
        """Get welcome message template"""
        return """
🎉 <b>Welcome to TokenTracker V2!</b>

Your advanced Solana token tracking and trading assistant is ready.

<b>Features:</b>
• 📊 Real-time signal alerts
• 💼 Portfolio tracking
• 🤖 Automated trading
• 📈 Performance analytics

Use /help to see available commands.
"""
    
    def _get_help_template(self) -> str:
        """Get help message template"""
        return """
🆘 <b>TokenTracker V2 Help</b>

<b>Available Commands:</b>
/start - Get started with TokenTracker
/help - Show this help message
/portfolio - View your portfolios
/signals - View active signals
/subscribe - Manage subscriptions
/settings - Update preferences

<b>Features:</b>
• Real-time signal alerts with interactive buttons
• Portfolio performance tracking
• Trade execution notifications
• Risk management tools

For support, contact @your_support_username
"""
