"""
✅ Signal Validation System

Multi-source confirmation, historical performance validation,
false signal filtering, and signal quality metrics.
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from ...config.logging_config import get_logger
from ...shared.types import SignalData, SignalType, MarketData
from ...database.models import Signal, Trade

logger = get_logger(__name__)


@dataclass
class ValidationResult:
    """Signal validation result"""
    is_valid: bool
    confidence_adjustment: float
    validation_score: float
    validation_factors: List[str]
    warnings: List[str]
    rejection_reasons: List[str]


class SignalValidator:
    """
    ✅ Signal Validation System
    
    Validates trading signals through:
    - Multi-source confirmation
    - Historical performance analysis
    - False signal filtering
    - Signal quality metrics
    - Market condition validation
    """
    
    def __init__(self):
        self.logger = logger
        
        # Validation thresholds
        self.min_validation_score = 0.6
        self.max_signals_per_token_per_day = 3
        self.min_time_between_signals = timedelta(hours=4)
        self.historical_lookback_days = 30
        self.min_historical_accuracy = 0.6
    
    async def validate_signal(
        self,
        signal_data: SignalData,
        market_data: MarketData,
        price_history: List[MarketData]
    ) -> ValidationResult:
        """
        Validate a trading signal comprehensively
        
        Args:
            signal_data: Signal to validate
            market_data: Current market data
            price_history: Historical price data
            
        Returns:
            ValidationResult with validation outcome
        """
        try:
            self.logger.info(f"Validating signal for {signal_data.token_address}")
            
            validation_factors = []
            warnings = []
            rejection_reasons = []
            confidence_adjustment = 0.0
            
            # Check signal frequency
            frequency_check = await self._check_signal_frequency(signal_data.token_address)
            if not frequency_check['valid']:
                rejection_reasons.append(frequency_check['reason'])
            else:
                validation_factors.append("Signal frequency acceptable")
            
            # Check market conditions
            market_check = self._validate_market_conditions(market_data, price_history)
            if market_check['valid']:
                validation_factors.append("Market conditions favorable")
                confidence_adjustment += market_check['confidence_boost']
            else:
                warnings.append(market_check['warning'])
                confidence_adjustment += market_check['confidence_penalty']
            
            # Check signal consistency
            consistency_check = self._check_signal_consistency(signal_data, market_data)
            if consistency_check['valid']:
                validation_factors.append("Signal internally consistent")
            else:
                warnings.append(consistency_check['warning'])
                confidence_adjustment += consistency_check['confidence_penalty']
            
            # Check historical performance
            historical_check = await self._check_historical_performance(
                signal_data.token_address, signal_data.signal_type
            )
            if historical_check['valid']:
                validation_factors.append(f"Historical accuracy: {historical_check['accuracy']:.1%}")
                confidence_adjustment += historical_check['confidence_boost']
            else:
                warnings.append(historical_check['warning'])
                confidence_adjustment += historical_check['confidence_penalty']
            
            # Check for conflicting signals
            conflict_check = await self._check_conflicting_signals(signal_data.token_address)
            if conflict_check['has_conflicts']:
                warnings.append(conflict_check['warning'])
                confidence_adjustment += conflict_check['confidence_penalty']
            else:
                validation_factors.append("No conflicting signals")
            
            # Check signal timing
            timing_check = self._validate_signal_timing(signal_data, market_data)
            if timing_check['valid']:
                validation_factors.append("Signal timing appropriate")
            else:
                warnings.append(timing_check['warning'])
                confidence_adjustment += timing_check['confidence_penalty']
            
            # Calculate validation score
            validation_score = self._calculate_validation_score(
                validation_factors, warnings, rejection_reasons, confidence_adjustment
            )
            
            # Determine if signal is valid
            is_valid = (
                len(rejection_reasons) == 0 and
                validation_score >= self.min_validation_score
            )
            
            result = ValidationResult(
                is_valid=is_valid,
                confidence_adjustment=confidence_adjustment,
                validation_score=validation_score,
                validation_factors=validation_factors,
                warnings=warnings,
                rejection_reasons=rejection_reasons
            )
            
            self.logger.info(
                f"Signal validation completed. Valid: {is_valid}, "
                f"Score: {validation_score:.2f}, Confidence adjustment: {confidence_adjustment:.2f}"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error validating signal: {str(e)}")
            return ValidationResult(
                is_valid=False,
                confidence_adjustment=-0.3,
                validation_score=0.0,
                validation_factors=[],
                warnings=[],
                rejection_reasons=["Validation error occurred"]
            )
    
    async def _check_signal_frequency(self, token_address: str) -> Dict[str, Any]:
        """Check if signal frequency is within acceptable limits"""
        try:
            # Check signals in last 24 hours
            since = datetime.utcnow() - timedelta(days=1)
            recent_signals = await Signal.find({
                "token_address": token_address,
                "created_at": {"$gte": since}
            }).count()
            
            if recent_signals >= self.max_signals_per_token_per_day:
                return {
                    'valid': False,
                    'reason': f"Too many signals today ({recent_signals}/{self.max_signals_per_token_per_day})"
                }
            
            # Check time since last signal
            last_signal = await Signal.find({
                "token_address": token_address
            }).sort("-created_at").first_or_none()
            
            if last_signal:
                time_since_last = datetime.utcnow() - last_signal.created_at
                if time_since_last < self.min_time_between_signals:
                    return {
                        'valid': False,
                        'reason': f"Too soon since last signal ({time_since_last})"
                    }
            
            return {'valid': True}
            
        except Exception as e:
            self.logger.error(f"Error checking signal frequency: {str(e)}")
            return {'valid': False, 'reason': "Frequency check error"}
    
    def _validate_market_conditions(
        self, 
        market_data: MarketData, 
        price_history: List[MarketData]
    ) -> Dict[str, Any]:
        """Validate current market conditions for signal generation"""
        try:
            confidence_boost = 0.0
            confidence_penalty = 0.0
            
            # Check liquidity
            if not market_data.liquidity or market_data.liquidity < Decimal("50000"):
                return {
                    'valid': False,
                    'warning': "Insufficient liquidity for reliable trading",
                    'confidence_penalty': -0.2
                }
            
            # Check volume
            if not market_data.volume_24h or market_data.volume_24h == 0:
                return {
                    'valid': False,
                    'warning': "No trading volume in last 24 hours",
                    'confidence_penalty': -0.3
                }
            
            # Check for extreme volatility
            if len(price_history) >= 24:  # Last 24 data points
                recent_prices = [float(data.price) for data in price_history[-24:]]
                price_range = (max(recent_prices) - min(recent_prices)) / min(recent_prices)
                
                if price_range > 0.5:  # > 50% range
                    confidence_penalty = -0.1
                    warning = "High volatility detected"
                elif price_range < 0.05:  # < 5% range
                    confidence_boost = 0.05
                    warning = None
                else:
                    warning = None
            else:
                warning = "Insufficient price history"
                confidence_penalty = -0.05
            
            return {
                'valid': True,
                'warning': warning,
                'confidence_boost': confidence_boost,
                'confidence_penalty': confidence_penalty
            }
            
        except Exception as e:
            self.logger.error(f"Error validating market conditions: {str(e)}")
            return {
                'valid': False,
                'warning': "Market condition validation error",
                'confidence_penalty': -0.1
            }
    
    def _check_signal_consistency(self, signal_data: SignalData, market_data: MarketData) -> Dict[str, Any]:
        """Check internal consistency of signal data"""
        try:
            warnings = []
            confidence_penalty = 0.0
            
            # Check price consistency
            price_diff = abs(float(signal_data.entry_price - market_data.price)) / float(market_data.price)
            if price_diff > 0.05:  # > 5% difference
                warnings.append(f"Entry price differs from market price by {price_diff:.1%}")
                confidence_penalty -= 0.1
            
            # Check stop loss and take profit logic
            if signal_data.signal_type == SignalType.BUY:
                if signal_data.stop_loss and signal_data.stop_loss >= signal_data.entry_price:
                    warnings.append("Stop loss should be below entry price for BUY signal")
                    confidence_penalty -= 0.2
                
                if signal_data.take_profit and signal_data.take_profit <= signal_data.entry_price:
                    warnings.append("Take profit should be above entry price for BUY signal")
                    confidence_penalty -= 0.2
            
            elif signal_data.signal_type == SignalType.SELL:
                if signal_data.stop_loss and signal_data.stop_loss <= signal_data.entry_price:
                    warnings.append("Stop loss should be above entry price for SELL signal")
                    confidence_penalty -= 0.2
                
                if signal_data.take_profit and signal_data.take_profit >= signal_data.entry_price:
                    warnings.append("Take profit should be below entry price for SELL signal")
                    confidence_penalty -= 0.2
            
            # Check position size reasonableness
            if signal_data.position_size_usd <= 0:
                warnings.append("Invalid position size")
                confidence_penalty -= 0.3
            elif signal_data.position_size_usd > Decimal("10000"):
                warnings.append("Position size seems very large")
                confidence_penalty -= 0.1
            
            is_valid = len(warnings) == 0 or confidence_penalty > -0.3
            
            return {
                'valid': is_valid,
                'warning': '; '.join(warnings) if warnings else None,
                'confidence_penalty': confidence_penalty
            }
            
        except Exception as e:
            self.logger.error(f"Error checking signal consistency: {str(e)}")
            return {
                'valid': False,
                'warning': "Signal consistency check error",
                'confidence_penalty': -0.2
            }
    
    async def _check_historical_performance(self, token_address: str, signal_type: SignalType) -> Dict[str, Any]:
        """Check historical performance of similar signals"""
        try:
            # Look for historical signals and their performance
            since = datetime.utcnow() - timedelta(days=self.historical_lookback_days)
            
            historical_signals = await Signal.find({
                "token_address": token_address,
                "signal_type": signal_type,
                "is_executed": True,
                "created_at": {"$gte": since}
            }).to_list()
            
            if len(historical_signals) < 3:
                return {
                    'valid': True,
                    'warning': "Limited historical data for validation",
                    'confidence_penalty': -0.05,
                    'accuracy': 0.5
                }
            
            # Calculate accuracy based on performance
            profitable_signals = 0
            for signal in historical_signals:
                if signal.performance_pnl and signal.performance_pnl > 0:
                    profitable_signals += 1
            
            accuracy = profitable_signals / len(historical_signals)
            
            if accuracy >= self.min_historical_accuracy:
                return {
                    'valid': True,
                    'accuracy': accuracy,
                    'confidence_boost': min(0.1, (accuracy - 0.5) * 0.2)
                }
            else:
                return {
                    'valid': True,
                    'warning': f"Low historical accuracy: {accuracy:.1%}",
                    'accuracy': accuracy,
                    'confidence_penalty': -0.15
                }
            
        except Exception as e:
            self.logger.error(f"Error checking historical performance: {str(e)}")
            return {
                'valid': True,
                'warning': "Historical performance check error",
                'confidence_penalty': -0.05,
                'accuracy': 0.5
            }
    
    async def _check_conflicting_signals(self, token_address: str) -> Dict[str, Any]:
        """Check for conflicting active signals"""
        try:
            active_signals = await Signal.find({
                "token_address": token_address,
                "is_active": True,
                "expires_at": {"$gt": datetime.utcnow()}
            }).to_list()
            
            if len(active_signals) <= 1:
                return {'has_conflicts': False}
            
            # Check for opposing signals
            signal_types = [signal.signal_type for signal in active_signals]
            has_buy = SignalType.BUY in signal_types
            has_sell = SignalType.SELL in signal_types
            
            if has_buy and has_sell:
                return {
                    'has_conflicts': True,
                    'warning': "Conflicting BUY and SELL signals active",
                    'confidence_penalty': -0.2
                }
            
            return {
                'has_conflicts': True,
                'warning': f"Multiple active signals ({len(active_signals)})",
                'confidence_penalty': -0.05
            }
            
        except Exception as e:
            self.logger.error(f"Error checking conflicting signals: {str(e)}")
            return {'has_conflicts': False}
    
    def _validate_signal_timing(self, signal_data: SignalData, market_data: MarketData) -> Dict[str, Any]:
        """Validate signal timing and expiration"""
        try:
            now = datetime.utcnow()
            
            # Check expiration time
            if signal_data.expires_at <= now:
                return {
                    'valid': False,
                    'warning': "Signal already expired"
                }
            
            # Check if expiration is too far in future
            time_to_expiry = signal_data.expires_at - now
            if time_to_expiry > timedelta(days=7):
                return {
                    'valid': True,
                    'warning': "Signal expires very far in future",
                    'confidence_penalty': -0.05
                }
            
            # Check if expiration is too soon
            if time_to_expiry < timedelta(hours=1):
                return {
                    'valid': True,
                    'warning': "Signal expires very soon",
                    'confidence_penalty': -0.1
                }
            
            return {'valid': True}
            
        except Exception as e:
            self.logger.error(f"Error validating signal timing: {str(e)}")
            return {
                'valid': False,
                'warning': "Signal timing validation error"
            }
    
    def _calculate_validation_score(
        self,
        validation_factors: List[str],
        warnings: List[str],
        rejection_reasons: List[str],
        confidence_adjustment: float
    ) -> float:
        """Calculate overall validation score"""
        try:
            # Base score
            base_score = 0.7
            
            # Add points for validation factors
            factor_bonus = len(validation_factors) * 0.05
            
            # Subtract points for warnings
            warning_penalty = len(warnings) * 0.1
            
            # Major penalty for rejection reasons
            rejection_penalty = len(rejection_reasons) * 0.5
            
            # Apply confidence adjustment
            score = base_score + factor_bonus - warning_penalty - rejection_penalty + confidence_adjustment
            
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            self.logger.error(f"Error calculating validation score: {str(e)}")
            return 0.0
