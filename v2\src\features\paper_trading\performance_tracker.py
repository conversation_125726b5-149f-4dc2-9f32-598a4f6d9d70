"""
📊 Performance Tracker

Portfolio performance analytics with Sharpe ratio, max drawdown,
win rate, and comprehensive performance metrics calculation.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from ...config.logging_config import get_logger
from ...database.models import Portfolio, Trade, PerformanceMetric
from .portfolio_manager import PortfolioManager

logger = get_logger(__name__)


@dataclass
class PerformanceSnapshot:
    """Performance metrics snapshot"""
    total_return: Decimal
    total_return_percent: Decimal
    annualized_return: Optional[Decimal]
    sharpe_ratio: Optional[Decimal]
    sortino_ratio: Optional[Decimal]
    max_drawdown: Decimal
    current_drawdown: Decimal
    volatility: Optional[Decimal]
    win_rate: Decimal
    profit_factor: Optional[Decimal]
    calmar_ratio: Optional[Decimal]
    var_95: Optional[Decimal]
    beta: Optional[Decimal]
    alpha: Optional[Decimal]


class PerformanceTracker:
    """
    📊 Performance Tracker
    
    Calculates and tracks portfolio performance metrics:
    - Return metrics (total, annualized, risk-adjusted)
    - Risk metrics (Sharpe ratio, max drawdown, volatility)
    - Trading metrics (win rate, profit factor)
    - Advanced metrics (Sortino, Calmar, VaR)
    """
    
    def __init__(self):
        self.logger = logger
        self.portfolio_manager = PortfolioManager()
        self.risk_free_rate = Decimal("0.02")  # 2% annual risk-free rate
    
    async def calculate_performance_metrics(
        self,
        portfolio_id: str,
        benchmark_returns: Optional[List[float]] = None
    ) -> PerformanceSnapshot:
        """
        Calculate comprehensive performance metrics for a portfolio
        
        Args:
            portfolio_id: Portfolio ID
            benchmark_returns: Optional benchmark returns for beta/alpha calculation
            
        Returns:
            PerformanceSnapshot with calculated metrics
        """
        try:
            self.logger.info(f"Calculating performance metrics for portfolio {portfolio_id}")
            
            # Get portfolio and value data
            portfolio = await self.portfolio_manager.get_portfolio(portfolio_id)
            if not portfolio:
                raise ValueError(f"Portfolio {portfolio_id} not found")
            
            value_data = await self.portfolio_manager.calculate_portfolio_value(portfolio_id)
            
            # Get daily returns
            daily_returns = await self._get_daily_returns(portfolio_id)
            
            # Calculate basic metrics
            total_value = Decimal(str(value_data["total_value"]))
            total_return = total_value - portfolio.initial_balance
            total_return_percent = (total_return / portfolio.initial_balance) * 100 if portfolio.initial_balance > 0 else Decimal("0")
            
            # Calculate time-based metrics
            portfolio_age_days = (datetime.utcnow() - portfolio.started_at).days
            annualized_return = self._calculate_annualized_return(total_return_percent, portfolio_age_days)
            
            # Calculate risk metrics
            volatility = self._calculate_volatility(daily_returns)
            sharpe_ratio = self._calculate_sharpe_ratio(annualized_return, volatility)
            sortino_ratio = self._calculate_sortino_ratio(daily_returns, annualized_return)
            
            # Calculate drawdown metrics
            max_drawdown, current_drawdown = await self._calculate_drawdown_metrics(portfolio_id)
            
            # Calculate Calmar ratio
            calmar_ratio = self._calculate_calmar_ratio(annualized_return, max_drawdown)
            
            # Calculate trading metrics
            win_rate = await self._calculate_win_rate(portfolio_id)
            profit_factor = await self._calculate_profit_factor(portfolio_id)
            
            # Calculate VaR
            var_95 = self._calculate_var(daily_returns, 0.95)
            
            # Calculate beta and alpha (if benchmark provided)
            beta, alpha = self._calculate_beta_alpha(daily_returns, benchmark_returns, annualized_return)
            
            snapshot = PerformanceSnapshot(
                total_return=total_return,
                total_return_percent=total_return_percent,
                annualized_return=annualized_return,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                current_drawdown=current_drawdown,
                volatility=volatility,
                win_rate=win_rate,
                profit_factor=profit_factor,
                calmar_ratio=calmar_ratio,
                var_95=var_95,
                beta=beta,
                alpha=alpha
            )
            
            # Save performance metric to database
            await self._save_performance_metric(portfolio_id, snapshot)
            
            self.logger.info(f"Performance metrics calculated. Return: {total_return_percent:.2f}%, Sharpe: {sharpe_ratio}")
            
            return snapshot
            
        except Exception as e:
            self.logger.error(f"Error calculating performance metrics: {str(e)}")
            raise
    
    async def _get_daily_returns(self, portfolio_id: str) -> List[float]:
        """Get daily returns for the portfolio"""
        try:
            portfolio = await self.portfolio_manager.get_portfolio(portfolio_id)
            if not portfolio or not portfolio.daily_balances:
                return []
            
            # Extract daily values
            daily_values = []
            for snapshot in portfolio.daily_balances:
                daily_values.append(snapshot.get("total_value", 0))
            
            # Calculate daily returns
            returns = []
            for i in range(1, len(daily_values)):
                if daily_values[i-1] > 0:
                    daily_return = (daily_values[i] - daily_values[i-1]) / daily_values[i-1]
                    returns.append(daily_return)
            
            return returns
            
        except Exception as e:
            self.logger.error(f"Error getting daily returns: {str(e)}")
            return []
    
    def _calculate_annualized_return(self, total_return_percent: Decimal, days: int) -> Optional[Decimal]:
        """Calculate annualized return"""
        try:
            if days <= 0:
                return None
            
            # Convert to annual return
            years = Decimal(str(days)) / Decimal("365")
            if years <= 0:
                return None
            
            # Compound annual growth rate
            if total_return_percent > -100:  # Avoid negative values in power calculation
                growth_factor = (total_return_percent / 100) + 1
                annualized = (growth_factor ** (1 / float(years)) - 1) * 100
                return Decimal(str(annualized))
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error calculating annualized return: {str(e)}")
            return None
    
    def _calculate_volatility(self, returns: List[float]) -> Optional[Decimal]:
        """Calculate return volatility (standard deviation)"""
        try:
            if len(returns) < 2:
                return None
            
            volatility = np.std(returns) * np.sqrt(252)  # Annualized volatility
            return Decimal(str(volatility))
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility: {str(e)}")
            return None
    
    def _calculate_sharpe_ratio(
        self,
        annualized_return: Optional[Decimal],
        volatility: Optional[Decimal]
    ) -> Optional[Decimal]:
        """Calculate Sharpe ratio"""
        try:
            if not annualized_return or not volatility or volatility == 0:
                return None
            
            excess_return = (annualized_return / 100) - (self.risk_free_rate)
            sharpe = excess_return / (volatility)
            return sharpe
            
        except Exception as e:
            self.logger.error(f"Error calculating Sharpe ratio: {str(e)}")
            return None
    
    def _calculate_sortino_ratio(
        self,
        returns: List[float],
        annualized_return: Optional[Decimal]
    ) -> Optional[Decimal]:
        """Calculate Sortino ratio (using downside deviation)"""
        try:
            if len(returns) < 2 or not annualized_return:
                return None
            
            # Calculate downside deviation
            negative_returns = [r for r in returns if r < 0]
            if not negative_returns:
                return None
            
            downside_deviation = np.std(negative_returns) * np.sqrt(252)
            if downside_deviation == 0:
                return None
            
            excess_return = (annualized_return / 100) - self.risk_free_rate
            sortino = excess_return / Decimal(str(downside_deviation))
            return sortino
            
        except Exception as e:
            self.logger.error(f"Error calculating Sortino ratio: {str(e)}")
            return None
    
    async def _calculate_drawdown_metrics(self, portfolio_id: str) -> Tuple[Decimal, Decimal]:
        """Calculate maximum and current drawdown"""
        try:
            portfolio = await self.portfolio_manager.get_portfolio(portfolio_id)
            if not portfolio or not portfolio.daily_balances:
                return Decimal("0"), Decimal("0")
            
            # Get daily values
            daily_values = [snapshot.get("total_value", 0) for snapshot in portfolio.daily_balances]
            
            if len(daily_values) < 2:
                return Decimal("0"), Decimal("0")
            
            # Calculate running maximum and drawdowns
            running_max = daily_values[0]
            max_drawdown = 0
            current_value = daily_values[-1]
            
            for value in daily_values:
                if value > running_max:
                    running_max = value
                
                if running_max > 0:
                    drawdown = (running_max - value) / running_max
                    max_drawdown = max(max_drawdown, drawdown)
            
            # Current drawdown
            current_running_max = max(daily_values)
            current_drawdown = (current_running_max - current_value) / current_running_max if current_running_max > 0 else 0
            
            return Decimal(str(max_drawdown)), Decimal(str(current_drawdown))
            
        except Exception as e:
            self.logger.error(f"Error calculating drawdown metrics: {str(e)}")
            return Decimal("0"), Decimal("0")
    
    def _calculate_calmar_ratio(
        self,
        annualized_return: Optional[Decimal],
        max_drawdown: Decimal
    ) -> Optional[Decimal]:
        """Calculate Calmar ratio (annualized return / max drawdown)"""
        try:
            if not annualized_return or max_drawdown == 0:
                return None
            
            calmar = (annualized_return / 100) / max_drawdown
            return calmar
            
        except Exception as e:
            self.logger.error(f"Error calculating Calmar ratio: {str(e)}")
            return None
    
    async def _calculate_win_rate(self, portfolio_id: str) -> Decimal:
        """Calculate win rate percentage"""
        try:
            trades = await Trade.find({
                "portfolio_id": portfolio_id,
                "status": "EXECUTED"
            }).to_list()
            
            if not trades:
                return Decimal("0")
            
            profitable_trades = len([t for t in trades if t.pnl > 0])
            win_rate = (profitable_trades / len(trades)) * 100
            
            return Decimal(str(win_rate))
            
        except Exception as e:
            self.logger.error(f"Error calculating win rate: {str(e)}")
            return Decimal("0")
    
    async def _calculate_profit_factor(self, portfolio_id: str) -> Optional[Decimal]:
        """Calculate profit factor (gross profit / gross loss)"""
        try:
            trades = await Trade.find({
                "portfolio_id": portfolio_id,
                "status": "EXECUTED"
            }).to_list()
            
            if not trades:
                return None
            
            gross_profit = sum(t.pnl for t in trades if t.pnl > 0)
            gross_loss = abs(sum(t.pnl for t in trades if t.pnl < 0))
            
            if gross_loss == 0:
                return None
            
            profit_factor = gross_profit / gross_loss
            return Decimal(str(profit_factor))
            
        except Exception as e:
            self.logger.error(f"Error calculating profit factor: {str(e)}")
            return None
    
    def _calculate_var(self, returns: List[float], confidence_level: float) -> Optional[Decimal]:
        """Calculate Value at Risk"""
        try:
            if len(returns) < 10:
                return None
            
            # Calculate VaR at given confidence level
            var_percentile = (1 - confidence_level) * 100
            var = np.percentile(returns, var_percentile)
            
            return Decimal(str(abs(var)))
            
        except Exception as e:
            self.logger.error(f"Error calculating VaR: {str(e)}")
            return None
    
    def _calculate_beta_alpha(
        self,
        portfolio_returns: List[float],
        benchmark_returns: Optional[List[float]],
        annualized_return: Optional[Decimal]
    ) -> Tuple[Optional[Decimal], Optional[Decimal]]:
        """Calculate beta and alpha relative to benchmark"""
        try:
            if not benchmark_returns or len(portfolio_returns) != len(benchmark_returns) or len(portfolio_returns) < 10:
                return None, None
            
            # Calculate beta using linear regression
            portfolio_array = np.array(portfolio_returns)
            benchmark_array = np.array(benchmark_returns)
            
            covariance = np.cov(portfolio_array, benchmark_array)[0][1]
            benchmark_variance = np.var(benchmark_array)
            
            if benchmark_variance == 0:
                return None, None
            
            beta = covariance / benchmark_variance
            
            # Calculate alpha
            if annualized_return:
                benchmark_return = np.mean(benchmark_returns) * 252  # Annualized
                alpha = (annualized_return / 100) - (self.risk_free_rate + Decimal(str(beta)) * (Decimal(str(benchmark_return)) - self.risk_free_rate))
                return Decimal(str(beta)), alpha
            
            return Decimal(str(beta)), None
            
        except Exception as e:
            self.logger.error(f"Error calculating beta/alpha: {str(e)}")
            return None, None
    
    async def _save_performance_metric(self, portfolio_id: str, snapshot: PerformanceSnapshot) -> None:
        """Save performance metric to database"""
        try:
            metric = PerformanceMetric(
                portfolio_id=portfolio_id,
                metric_type="daily",
                calculation_date=datetime.utcnow(),
                total_return=snapshot.total_return,
                total_return_percent=snapshot.total_return_percent,
                annualized_return=snapshot.annualized_return,
                sharpe_ratio=snapshot.sharpe_ratio,
                sortino_ratio=snapshot.sortino_ratio,
                calmar_ratio=snapshot.calmar_ratio,
                max_drawdown=snapshot.max_drawdown,
                current_drawdown=snapshot.current_drawdown,
                volatility=snapshot.volatility,
                win_rate=snapshot.win_rate,
                profit_factor=snapshot.profit_factor,
                var_95=snapshot.var_95,
                beta=snapshot.beta,
                alpha=snapshot.alpha
            )
            
            await metric.save()
            
        except Exception as e:
            self.logger.error(f"Error saving performance metric: {str(e)}")
    
    async def get_performance_history(
        self,
        portfolio_id: str,
        days: int = 30
    ) -> List[PerformanceMetric]:
        """Get performance history for a portfolio"""
        try:
            since = datetime.utcnow() - timedelta(days=days)
            
            metrics = await PerformanceMetric.find({
                "portfolio_id": portfolio_id,
                "calculation_date": {"$gte": since}
            }).sort("calculation_date").to_list()
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error getting performance history: {str(e)}")
            return []
    
    async def compare_portfolios(self, portfolio_ids: List[str]) -> Dict[str, Any]:
        """Compare performance across multiple portfolios"""
        try:
            comparison_data = {}
            
            for portfolio_id in portfolio_ids:
                snapshot = await self.calculate_performance_metrics(portfolio_id)
                comparison_data[portfolio_id] = {
                    "total_return_percent": float(snapshot.total_return_percent),
                    "sharpe_ratio": float(snapshot.sharpe_ratio) if snapshot.sharpe_ratio else None,
                    "max_drawdown": float(snapshot.max_drawdown),
                    "win_rate": float(snapshot.win_rate),
                    "volatility": float(snapshot.volatility) if snapshot.volatility else None
                }
            
            return comparison_data
            
        except Exception as e:
            self.logger.error(f"Error comparing portfolios: {str(e)}")
            return {}
