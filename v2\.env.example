# ===========================================
# 🔐 TOKENTRACKER V2 ENVIRONMENT CONFIGURATION
# ===========================================

# 🌍 Application Environment
NODE_ENV=development
APP_NAME=TokenTracker_V2
APP_VERSION=2.0.0
APP_PORT=3000

# 🗄️ Database Configuration
MONGODB_URI=your_mongodb_atlas_uri_here
MONGODB_DB_NAME=tokentracker_v2
REDIS_URL=redis://localhost:6379

# 🔍 Dune Analytics Configuration
DUNE_API_KEY=your_dune_api_key_here
DUNE_QUERY_ID=4625658
DUNE_EXECUTION_ID=7705504
DUNE_BASE_URL=https://api.dune.com/api/v1

# 📱 Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHANNEL_ID=your_telegram_channel_id_here
TELEGRAM_ADMIN_CHAT_ID=your_admin_chat_id_here

# 🌐 Solana & DEX Configuration
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_WS_URL=wss://api.mainnet-beta.solana.com
JUPITER_API_URL=https://quote-api.jup.ag/v6
RAYDIUM_API_URL=https://api.raydium.io/v2
BIRDEYE_API_KEY=your_birdeye_api_key_here

# 🔐 Security Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h
API_RATE_LIMIT=100
ENCRYPTION_KEY=your-32-character-encryption-key

# 📊 Trading Configuration
QUERY_INTERVAL_MINUTES=30
CACHE_DURATION_MINUTES=60
MAX_POSITION_SIZE_USD=1000
DEFAULT_STOP_LOSS_PERCENT=5
DEFAULT_TAKE_PROFIT_PERCENT=15
MIN_LIQUIDITY_USD=50000

# 🤖 AI Integration (Optional)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 📈 Monitoring & Logging
LOG_LEVEL=info
LOG_FORMAT=json
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=60
PROMETHEUS_PORT=9090

# 🔄 Performance Configuration
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT_MS=30000
RETRY_ATTEMPTS=3
RETRY_DELAY_MS=1000

# 🧪 Testing Configuration
TEST_MONGODB_URI=mongodb://localhost:27017/tokentracker_v2_test
TEST_REDIS_URL=redis://localhost:6379/1
TEST_TIMEOUT_MS=10000

# 🚀 Deployment Configuration
DOCKER_REGISTRY=ghcr.io
IMAGE_NAME=tokentracker-v2
DEPLOYMENT_ENVIRONMENT=development
HEALTH_CHECK_PATH=/health
READINESS_CHECK_PATH=/ready

# 📧 Notification Configuration
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=TokenTracker V2 <<EMAIL>>

# 🔔 Alert Configuration
ALERT_WEBHOOK_URL=your_webhook_url_here
SLACK_WEBHOOK_URL=your_slack_webhook_url_here
DISCORD_WEBHOOK_URL=your_discord_webhook_url_here

# 💾 Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
