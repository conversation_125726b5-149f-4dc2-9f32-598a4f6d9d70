"""
⚡ Trade Executor

Paper trading execution simulator with market orders, limit orders,
stop-loss, take-profit, and slippage simulation.
"""

import asyncio
import random
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from uuid import uuid4

from ...config.logging_config import get_logger
from ...shared.types import SignalType, OrderType, TradeStatus, SignalData
from ...database.models import Trade, Portfolio, Signal
from ...features.data_pipeline import DataAggregator

from .portfolio_manager import PortfolioManager

logger = get_logger(__name__)


class TradeExecutor:
    """
    ⚡ Trade Executor
    
    Simulates trade execution with:
    - Market order simulation
    - Limit order handling
    - Stop-loss and take-profit execution
    - Slippage simulation
    - Fee calculation
    - Execution quality metrics
    """
    
    def __init__(self):
        self.logger = logger
        self.portfolio_manager = PortfolioManager()
        self.data_aggregator = DataAggregator()
        
        # Execution parameters
        self.base_fee_percent = Decimal("0.003")  # 0.3% base trading fee
        self.slippage_factor = Decimal("0.001")   # 0.1% base slippage
        self.execution_delay_seconds = (1, 5)     # Random execution delay
    
    async def execute_signal_trade(
        self,
        portfolio_id: str,
        signal: SignalData,
        custom_quantity: Optional[Decimal] = None
    ) -> Optional[Trade]:
        """
        Execute a trade based on a signal
        
        Args:
            portfolio_id: Portfolio ID
            signal: Trading signal
            custom_quantity: Custom trade quantity (overrides signal position size)
            
        Returns:
            Executed Trade object or None
        """
        try:
            self.logger.info(f"Executing signal trade for portfolio {portfolio_id}")
            
            # Get current market data
            market_data = await self.data_aggregator.get_token_data(signal.token_address)
            if not market_data:
                self.logger.error(f"No market data available for {signal.token_address}")
                return None
            
            # Calculate trade quantity
            if custom_quantity:
                trade_value = custom_quantity * market_data.price
            else:
                trade_value = signal.position_size_usd
                custom_quantity = trade_value / market_data.price
            
            # Check risk limits
            risk_check = await self.portfolio_manager.check_risk_limits(portfolio_id, trade_value)
            if not risk_check["valid"]:
                self.logger.warning(f"Risk check failed: {risk_check['reason']}")
                return None
            
            # Execute the trade
            trade = await self.execute_market_order(
                portfolio_id=portfolio_id,
                token_address=signal.token_address,
                side=signal.signal_type,
                quantity=custom_quantity,
                signal_id=signal.id,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit
            )
            
            if trade:
                # Update signal as executed
                signal_doc = await Signal.find_one({"id": signal.id})
                if signal_doc:
                    signal_doc.is_executed = True
                    signal_doc.execution_price = trade.price
                    signal_doc.execution_time = trade.execution_time
                    await signal_doc.save()
                
                # Update portfolio metrics
                await self.portfolio_manager.update_portfolio_metrics(portfolio_id)
            
            return trade
            
        except Exception as e:
            self.logger.error(f"Error executing signal trade: {str(e)}")
            return None
    
    async def execute_market_order(
        self,
        portfolio_id: str,
        token_address: str,
        side: SignalType,
        quantity: Decimal,
        signal_id: Optional[str] = None,
        stop_loss: Optional[Decimal] = None,
        take_profit: Optional[Decimal] = None
    ) -> Optional[Trade]:
        """
        Execute a market order
        
        Args:
            portfolio_id: Portfolio ID
            token_address: Token address
            side: BUY or SELL
            quantity: Trade quantity
            signal_id: Source signal ID
            stop_loss: Stop loss price
            take_profit: Take profit price
            
        Returns:
            Executed Trade object or None
        """
        try:
            # Get current market data
            market_data = await self.data_aggregator.get_token_data(token_address)
            if not market_data:
                return None
            
            # Simulate execution delay
            delay = random.uniform(*self.execution_delay_seconds)
            await asyncio.sleep(delay)
            
            # Calculate execution price with slippage
            execution_price = self._calculate_execution_price(
                market_data.price, side, quantity, market_data.liquidity
            )
            
            # Calculate trade value
            trade_value = quantity * execution_price
            
            # Calculate fees
            fees = self._calculate_fees(trade_value, market_data.liquidity)
            
            # Calculate slippage
            slippage = abs(execution_price - market_data.price)
            
            # Create trade record
            trade = Trade(
                portfolio_id=portfolio_id,
                signal_id=signal_id,
                token_address=token_address,
                side=side,
                order_type=OrderType.MARKET,
                quantity=quantity,
                price=execution_price,
                value_usd=trade_value,
                fees=fees,
                slippage=slippage,
                total_cost=fees,
                stop_loss=stop_loss,
                take_profit=take_profit,
                status=TradeStatus.EXECUTED,
                execution_time=datetime.utcnow(),
                is_automated=True,
                automation_source="signal_processor"
            )
            
            # Update execution quality metrics
            trade.update_execution_quality(
                expected_price=market_data.price,
                market_impact=self._calculate_market_impact(quantity, market_data.liquidity)
            )
            
            # Save trade
            await trade.save()
            
            # Update portfolio balance
            if side == SignalType.BUY:
                # Deduct cash for buy order
                total_cost = trade_value + fees
                success = await self.portfolio_manager.update_portfolio_balance(
                    portfolio_id, total_cost, "subtract"
                )
                if not success:
                    self.logger.error("Failed to update portfolio balance for buy order")
                    return None
            
            elif side == SignalType.SELL:
                # Add cash for sell order
                net_proceeds = trade_value - fees
                await self.portfolio_manager.update_portfolio_balance(
                    portfolio_id, net_proceeds, "add"
                )
            
            self.logger.info(
                f"Market order executed: {side} {quantity} {token_address} "
                f"at ${execution_price} (slippage: ${slippage})"
            )
            
            return trade
            
        except Exception as e:
            self.logger.error(f"Error executing market order: {str(e)}")
            return None
    
    async def execute_limit_order(
        self,
        portfolio_id: str,
        token_address: str,
        side: SignalType,
        quantity: Decimal,
        limit_price: Decimal,
        signal_id: Optional[str] = None,
        expires_at: Optional[datetime] = None
    ) -> Optional[Trade]:
        """
        Execute a limit order (simplified simulation)
        
        Args:
            portfolio_id: Portfolio ID
            token_address: Token address
            side: BUY or SELL
            quantity: Trade quantity
            limit_price: Limit price
            signal_id: Source signal ID
            expires_at: Order expiration time
            
        Returns:
            Trade object (pending or executed)
        """
        try:
            # Get current market data
            market_data = await self.data_aggregator.get_token_data(token_address)
            if not market_data:
                return None
            
            current_price = market_data.price
            
            # Check if limit order can be executed immediately
            can_execute = (
                (side == SignalType.BUY and current_price <= limit_price) or
                (side == SignalType.SELL and current_price >= limit_price)
            )
            
            if can_execute:
                # Execute as market order at limit price
                return await self.execute_market_order(
                    portfolio_id, token_address, side, quantity, signal_id
                )
            else:
                # Create pending limit order
                trade = Trade(
                    portfolio_id=portfolio_id,
                    signal_id=signal_id,
                    token_address=token_address,
                    side=side,
                    order_type=OrderType.LIMIT,
                    quantity=quantity,
                    price=limit_price,
                    value_usd=quantity * limit_price,
                    status=TradeStatus.PENDING,
                    is_automated=True
                )
                
                await trade.save()
                
                self.logger.info(
                    f"Limit order created: {side} {quantity} {token_address} "
                    f"at ${limit_price} (current: ${current_price})"
                )
                
                return trade
            
        except Exception as e:
            self.logger.error(f"Error executing limit order: {str(e)}")
            return None
    
    def _calculate_execution_price(
        self,
        market_price: Decimal,
        side: SignalType,
        quantity: Decimal,
        liquidity: Optional[Decimal]
    ) -> Decimal:
        """Calculate execution price with slippage"""
        try:
            # Base slippage
            base_slippage = self.slippage_factor
            
            # Adjust slippage based on trade size vs liquidity
            if liquidity and liquidity > 0:
                trade_value = quantity * market_price
                liquidity_ratio = trade_value / liquidity
                
                # Increase slippage for larger trades relative to liquidity
                size_impact = min(Decimal("0.01"), liquidity_ratio * Decimal("0.1"))
                total_slippage = base_slippage + size_impact
            else:
                total_slippage = base_slippage * Decimal("2")  # Higher slippage if no liquidity data
            
            # Add random component (±50% of calculated slippage)
            random_factor = Decimal(str(random.uniform(0.5, 1.5)))
            final_slippage = total_slippage * random_factor
            
            # Apply slippage based on side
            if side == SignalType.BUY:
                # Buy orders get worse (higher) prices
                execution_price = market_price * (Decimal("1") + final_slippage)
            else:
                # Sell orders get worse (lower) prices
                execution_price = market_price * (Decimal("1") - final_slippage)
            
            return execution_price
            
        except Exception as e:
            self.logger.error(f"Error calculating execution price: {str(e)}")
            return market_price
    
    def _calculate_fees(self, trade_value: Decimal, liquidity: Optional[Decimal]) -> Decimal:
        """Calculate trading fees"""
        try:
            # Base fee
            base_fee = trade_value * self.base_fee_percent
            
            # Adjust fee based on liquidity (lower liquidity = higher fees)
            if liquidity and liquidity > 0:
                if liquidity < Decimal("100000"):  # Low liquidity
                    fee_multiplier = Decimal("1.5")
                elif liquidity < Decimal("500000"):  # Medium liquidity
                    fee_multiplier = Decimal("1.2")
                else:  # High liquidity
                    fee_multiplier = Decimal("1.0")
            else:
                fee_multiplier = Decimal("2.0")  # High fee if no liquidity data
            
            return base_fee * fee_multiplier
            
        except Exception as e:
            self.logger.error(f"Error calculating fees: {str(e)}")
            return trade_value * self.base_fee_percent
    
    def _calculate_market_impact(self, quantity: Decimal, liquidity: Optional[Decimal]) -> Decimal:
        """Calculate market impact of the trade"""
        try:
            if not liquidity or liquidity <= 0:
                return Decimal("0.01")  # 1% impact if no liquidity data
            
            # Simple market impact model
            # Impact increases with trade size relative to liquidity
            impact_ratio = quantity / liquidity
            market_impact = min(Decimal("0.05"), impact_ratio * Decimal("0.1"))  # Max 5% impact
            
            return market_impact
            
        except Exception as e:
            self.logger.error(f"Error calculating market impact: {str(e)}")
            return Decimal("0.005")  # Default 0.5% impact
    
    async def check_stop_loss_take_profit(self, portfolio_id: str) -> List[Trade]:
        """
        Check and execute stop-loss and take-profit orders
        
        Args:
            portfolio_id: Portfolio ID
            
        Returns:
            List of executed trades
        """
        try:
            executed_trades = []
            
            # Get current positions
            positions = await self.portfolio_manager.get_portfolio_positions(portfolio_id)
            
            for position in positions:
                token_address = position["token_address"]
                
                # Get current market price
                market_data = await self.data_aggregator.get_token_data(token_address)
                if not market_data:
                    continue
                
                current_price = market_data.price
                
                # Get open trades with stop-loss or take-profit
                open_trades = await Trade.find({
                    "portfolio_id": portfolio_id,
                    "token_address": token_address,
                    "status": TradeStatus.EXECUTED,
                    "side": SignalType.BUY,  # Only check long positions for now
                    "$or": [
                        {"stop_loss": {"$ne": None}},
                        {"take_profit": {"$ne": None}}
                    ]
                }).to_list()
                
                for trade in open_trades:
                    should_close = False
                    close_reason = ""
                    
                    # Check stop-loss
                    if trade.stop_loss and current_price <= trade.stop_loss:
                        should_close = True
                        close_reason = "stop_loss"
                    
                    # Check take-profit
                    elif trade.take_profit and current_price >= trade.take_profit:
                        should_close = True
                        close_reason = "take_profit"
                    
                    if should_close:
                        # Execute closing trade
                        close_trade = await self.execute_market_order(
                            portfolio_id=portfolio_id,
                            token_address=token_address,
                            side=SignalType.SELL,
                            quantity=trade.quantity,
                            signal_id=trade.signal_id
                        )
                        
                        if close_trade:
                            close_trade.notes = f"Closed by {close_reason}"
                            close_trade.parent_trade_id = str(trade.id)
                            await close_trade.save()
                            
                            executed_trades.append(close_trade)
                            
                            self.logger.info(
                                f"Position closed by {close_reason}: "
                                f"{trade.quantity} {token_address} at ${current_price}"
                            )
            
            return executed_trades
            
        except Exception as e:
            self.logger.error(f"Error checking stop-loss/take-profit: {str(e)}")
            return []
    
    async def get_trade_history(
        self,
        portfolio_id: str,
        limit: int = 50,
        token_address: Optional[str] = None
    ) -> List[Trade]:
        """Get trade history for a portfolio"""
        try:
            query = {"portfolio_id": portfolio_id}
            if token_address:
                query["token_address"] = token_address
            
            trades = await Trade.find(query).sort("-created_at").limit(limit).to_list()
            return trades
            
        except Exception as e:
            self.logger.error(f"Error getting trade history: {str(e)}")
            return []
