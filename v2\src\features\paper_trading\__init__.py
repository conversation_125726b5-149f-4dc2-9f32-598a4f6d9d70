"""
💼 Paper Trading Module

Virtual trading system with portfolio management, trade execution simulation,
performance analytics, and backtesting following V2 architecture patterns.
"""

from .portfolio_manager import PortfolioManager
from .trade_executor import TradeExecutor
from .performance_tracker import PerformanceTracker
from .backtest_engine import BacktestEngine
from .routes import router

__all__ = [
    "PortfolioManager",
    "TradeExecutor",
    "PerformanceTracker", 
    "BacktestEngine",
    "router"
]
