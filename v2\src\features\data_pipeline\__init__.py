"""
🔍 Data Pipeline Module

Multi-source data collection and processing following
V2 instructions for enhanced reliability and performance.
"""

from .dune_client import DuneClient
from .jupiter_client import JupiterClient
from .raydium_client import RaydiumClient
from .solana_client import SolanaClient
from .data_aggregator import DataAggregator, AggregatedTokenData
from .data_validator import DataValidator, ValidationResult
from .cache_manager import CacheManager

__all__ = [
    "DuneClient",
    "JupiterClient",
    "RaydiumClient",
    "SolanaClient",
    "DataAggregator",
    "AggregatedTokenData",
    "DataValidator",
    "ValidationResult",
    "CacheManager"
]
