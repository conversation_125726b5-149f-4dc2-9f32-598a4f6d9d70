# 🔐 SECURITY RULES FOR AI-GENERATED CODE

## 🛡️ AUTHENTICATION & AUTHORIZATION
- **JWT Tokens**: Use secure JWT implementation with proper expiration and refresh mechanisms
- **Password Security**: Hash passwords with bcrypt (min 12 rounds), never store plaintext
- **Session Management**: Implement secure session handling with proper timeout and invalidation
- **Role-Based Access**: Implement RBAC with principle of least privilege
- **API Keys**: Rotate API keys regularly, store in environment variables only

## 🔒 DATA PROTECTION
- **Input Validation**: Validate and sanitize ALL user inputs (SQL injection, XSS prevention)
- **Output Encoding**: Encode outputs appropriately for context (HTML, JSON, URL)
- **Sensitive Data**: Never log passwords, tokens, credit cards, or PII
- **Data Encryption**: Encrypt sensitive data at rest and in transit (TLS 1.3+)
- **Database Security**: Use parameterized queries, avoid dynamic SQL construction

## 🌐 API SECURITY
- **Rate Limiting**: Implement rate limiting on all endpoints (per IP, per user)
- **CORS**: Configure CORS properly - avoid wildcard (*) in production
- **Headers**: Set security headers (HSTS, CSP, X-Frame-Options, etc.)
- **Request Size**: Limit request payload sizes to prevent DoS attacks
- **API Versioning**: Version APIs properly to maintain security across updates

## 🔍 MONITORING & LOGGING
- **Security Events**: Log authentication failures, privilege escalations, data access
- **Anomaly Detection**: Monitor for unusual patterns (multiple failed logins, etc.)
- **Audit Trails**: Maintain audit logs for sensitive operations
- **Error Handling**: Don't expose internal system details in error messages
- **Incident Response**: Have procedures for security incident handling

## 🏗️ INFRASTRUCTURE SECURITY
- **Environment Variables**: Store all secrets in environment variables, never in code
- **Dependencies**: Regularly audit and update dependencies for vulnerabilities
- **Container Security**: Use minimal base images, scan for vulnerabilities
- **Network Security**: Implement proper firewall rules and network segmentation
- **Backup Security**: Encrypt backups and test restoration procedures

## 📋 COMPLIANCE & STANDARDS
- **OWASP Top 10**: Address all OWASP Top 10 vulnerabilities
- **Data Privacy**: Comply with GDPR, CCPA, and relevant data protection laws
- **Security Testing**: Include security testing in CI/CD pipeline
- **Penetration Testing**: Regular security assessments and penetration testing
- **Documentation**: Maintain security documentation and incident response plans

## 🚨 CRITICAL SECURITY CHECKLIST
- [ ] No hardcoded secrets or credentials
- [ ] All inputs validated and sanitized
- [ ] Proper authentication and authorization
- [ ] Secure communication (HTTPS/TLS)
- [ ] Error handling doesn't leak information
- [ ] Security headers configured
- [ ] Rate limiting implemented
- [ ] Logging configured for security events
- [ ] Dependencies regularly updated
- [ ] Security testing included in CI/CD