import pytest
from unittest.mock import Mock, patch
from src.dune_client import Dune<PERSON>lient

@pytest.mark.asyncio
async def test_execute_query(dune_client, mock_dune_response):
    # Mock the API responses
    dune_client.requests.post.return_value.json.return_value = {"execution_id": "test_id"}
    dune_client.requests.get.side_effect = [
        Mock(json=lambda: {"state": "QUERY_STATE_COMPLETED"}),
        Mock(json=lambda: mock_dune_response)
    ]
    
    # Execute the query
    results = dune_client.execute_query()
    
    # Verify the results
    assert results == mock_dune_response
    assert dune_client.requests.post.called
    assert dune_client.requests.get.call_count == 2

@pytest.mark.asyncio
async def test_get_latest_results(dune_client, mock_dune_response):
    # Mock the API response
    dune_client.requests.get.return_value.json.return_value = mock_dune_response
    
    # Get latest results
    results = dune_client.get_latest_results()
    
    # Verify the results
    assert results == mock_dune_response
    assert dune_client.requests.get.call_count == 1
    assert dune_client.requests.get.call_args[0][0] == f"{dune_client.base_url}/query/{dune_client.query_id}/results"

@pytest.mark.asyncio
async def test_get_latest_results_error(dune_client):
    # Mock the API error
    dune_client.requests.get.side_effect = Exception("API Error")
    
    # Get latest results
    results = dune_client.get_latest_results()
    
    # Verify no results
    assert results is None
    assert dune_client.requests.get.call_count == 1 