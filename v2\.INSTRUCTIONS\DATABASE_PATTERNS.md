# 🗄️ DATABASE PATTERNS & MONGODB ATLAS OPTIMIZATION

## 🚀 MONGODB ATLAS CONFIGURATION

### 🔧 CONNECTION MANAGEMENT
```javascript
// config/database.js
const mongoose = require('mongoose');

class DatabaseConnection {
  constructor() {
    this.connection = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      const options = {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10,          // Maintain up to 10 socket connections
        serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
        socketTimeoutMS: 45000,   // Close sockets after 45 seconds of inactivity
        bufferMaxEntries: 0,      // Disable mongoose buffering
        bufferCommands: false,    // Disable mongoose buffering
      };

      this.connection = await mongoose.connect(process.env.MONGODB_URI, options);
      this.isConnected = true;
      
      console.log('Connected to MongoDB Atlas');
      
      // Handle connection events
      mongoose.connection.on('error', this.handleError.bind(this));
      mongoose.connection.on('disconnected', this.handleDisconnection.bind(this));
      
    } catch (error) {
      console.error('MongoDB connection error:', error);
      throw error;
    }
  }

  async disconnect() {
    if (this.connection) {
      await mongoose.disconnect();
      this.isConnected = false;
    }
  }

  handleError(error) {
    console.error('MongoDB error:', error);
  }

  handleDisconnection() {
    console.log('MongoDB disconnected');
    this.isConnected = false;
  }
}

module.exports = new DatabaseConnection();
```

### 🌍 ENVIRONMENT-SPECIFIC CONFIGURATION
```javascript
// config/environments/production.js
module.exports = {
  database: {
    uri: process.env.MONGODB_URI,
    options: {
      maxPoolSize: 20,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      retryWrites: true,
      w: 'majority',
      readPreference: 'primaryPreferred'
    }
  }
};

// config/environments/development.js
module.exports = {
  database: {
    uri: process.env.MONGODB_DEV_URI,
    options: {
      maxPoolSize: 5,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      debug: true
    }
  }
};
```

## 📊 SCHEMA DESIGN PATTERNS

### 🏗️ BASE MODEL PATTERN
```javascript
// models/BaseModel.js
const mongoose = require('mongoose');

const baseSchema = new mongoose.Schema({
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  version: {
    type: Number,
    default: 1
  }
}, {
  timestamps: true,
  versionKey: false
});

// Pre-save middleware to update updatedAt
baseSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Pre-update middleware
baseSchema.pre(['updateOne', 'findOneAndUpdate'], function(next) {
  this.set({ updatedAt: new Date() });
  next();
});

module.exports = baseSchema;
```

### 👤 USER MODEL EXAMPLE
```javascript
// models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const baseSchema = require('./BaseModel');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    index: true
  },
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  profile: {
    firstName: { type: String, required: true, trim: true },
    lastName: { type: String, required: true, trim: true },
    avatar: { type: String },
    bio: { type: String, maxlength: 500 }
  },
  role: {
    type: String,
    enum: ['user', 'admin', 'moderator'],
    default: 'user',
    index: true
  },
  preferences: {
    notifications: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true }
    },
    theme: { type: String, enum: ['light', 'dark'], default: 'light' }
  },
  lastLoginAt: { type: Date },
  loginCount: { type: Number, default: 0 }
});

// Add base schema fields
userSchema.add(baseSchema);

// Indexes for performance
userSchema.index({ email: 1, isActive: 1 });
userSchema.index({ role: 1, isActive: 1 });
userSchema.index({ createdAt: -1 });

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return `${this.profile.firstName} ${this.profile.lastName}`;
});

// Password hashing middleware
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Password comparison method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Update login statistics
userSchema.methods.updateLoginStats = function() {
  this.lastLoginAt = new Date();
  this.loginCount += 1;
  return this.save();
};

// Transform output (remove sensitive data)
userSchema.methods.toJSON = function() {
  const user = this.toObject();
  delete user.password;
  return user;
};

module.exports = mongoose.model('User', userSchema);
```

## 🔍 QUERY OPTIMIZATION PATTERNS

### 📈 REPOSITORY PATTERN
```javascript
// repositories/UserRepository.js
class UserRepository {
  constructor(model) {
    this.model = model;
  }

  async findById(id, options = {}) {
    const { populate = [], select = '' } = options;
    
    let query = this.model.findById(id);
    
    if (select) query = query.select(select);
    if (populate.length) query = query.populate(populate);
    
    return query.exec();
  }

  async findByEmail(email) {
    return this.model.findOne({ 
      email: email.toLowerCase(),
      isActive: true 
    }).exec();
  }

  async findActiveUsers(filters = {}, options = {}) {
    const { 
      page = 1, 
      limit = 20, 
      sort = { createdAt: -1 },
      populate = [],
      select = ''
    } = options;

    const query = {
      isActive: true,
      ...filters
    };

    const skip = (page - 1) * limit;

    let mongoQuery = this.model.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit);

    if (select) mongoQuery = mongoQuery.select(select);
    if (populate.length) mongoQuery = mongoQuery.populate(populate);

    const [users, total] = await Promise.all([
      mongoQuery.exec(),
      this.model.countDocuments(query)
    ]);

    return {
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async createUser(userData) {
    const user = new this.model(userData);
    return user.save();
  }

  async updateUser(id, updateData) {
    return this.model.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).exec();
  }

  async softDelete(id) {
    return this.model.findByIdAndUpdate(
      id,
      { $set: { isActive: false } },
      { new: true }
    ).exec();
  }

  // Aggregation example
  async getUserStats() {
    return this.model.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 },
          avgLoginCount: { $avg: '$loginCount' }
        }
      },
      { $sort: { count: -1 } }
    ]);
  }
}

module.exports = UserRepository;
```

### 🚀 PERFORMANCE OPTIMIZATION
```javascript
// utils/queryOptimizer.js
class QueryOptimizer {
  static addPagination(query, page = 1, limit = 20) {
    const skip = (page - 1) * limit;
    return query.skip(skip).limit(limit);
  }

  static addSorting(query, sort = { createdAt: -1 }) {
    return query.sort(sort);
  }

  static addProjection(query, fields) {
    if (fields && fields.length > 0) {
      return query.select(fields.join(' '));
    }
    return query;
  }

  static addPopulation(query, populate = []) {
    if (populate.length > 0) {
      populate.forEach(pop => {
        if (typeof pop === 'string') {
          query = query.populate(pop);
        } else {
          query = query.populate(pop);
        }
      });
    }
    return query;
  }

  // Lean queries for read-only operations
  static makeLean(query) {
    return query.lean();
  }

  // Index hints for complex queries
  static addHint(query, index) {
    return query.hint(index);
  }
}

module.exports = QueryOptimizer;
```

## 💾 CACHING STRATEGIES

### 🔄 REDIS INTEGRATION
```javascript
// services/CacheService.js
const Redis = require('redis');

class CacheService {
  constructor() {
    this.client = Redis.createClient({
      url: process.env.REDIS_URL,
      retry_strategy: (options) => {
        if (options.error && options.error.code === 'ECONNREFUSED') {
          return new Error('Redis server connection refused');
        }
        if (options.total_retry_time > 1000 * 60 * 60) {
          return new Error('Retry time exhausted');
        }
        if (options.attempt > 10) {
          return undefined;
        }
        return Math.min(options.attempt * 100, 3000);
      }
    });
  }

  async get(key) {
    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set(key, value, ttl = 3600) {
    try {
      await this.client.setex(key, ttl, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  async del(key) {
    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  async invalidatePattern(pattern) {
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(keys);
      }
      return true;
    } catch (error) {
      console.error('Cache invalidation error:', error);
      return false;
    }
  }

  // Cache wrapper for database queries
  async cacheQuery(key, queryFn, ttl = 3600) {
    const cached = await this.get(key);
    if (cached) {
      return cached;
    }

    const result = await queryFn();
    await this.set(key, result, ttl);
    return result;
  }
}

module.exports = new CacheService();
```

## 🔄 MIGRATION PATTERNS

### 📋 MIGRATION FRAMEWORK
```javascript
// migrations/Migration.js
class Migration {
  constructor(name, version) {
    this.name = name;
    this.version = version;
    this.executedAt = null;
  }

  async up() {
    throw new Error('up() method must be implemented');
  }

  async down() {
    throw new Error('down() method must be implemented');
  }

  async execute() {
    console.log(`Executing migration: ${this.name}`);
    await this.up();
    this.executedAt = new Date();
    console.log(`Migration completed: ${this.name}`);
  }

  async rollback() {
    console.log(`Rolling back migration: ${this.name}`);
    await this.down();
    console.log(`Rollback completed: ${this.name}`);
  }
}

// Example migration
class AddUserPreferences extends Migration {
  constructor() {
    super('AddUserPreferences', '001');
  }

  async up() {
    const User = require('../models/User');
    
    await User.updateMany(
      { preferences: { $exists: false } },
      {
        $set: {
          preferences: {
            notifications: { email: true, push: true },
            theme: 'light'
          }
        }
      }
    );
  }

  async down() {
    const User = require('../models/User');
    
    await User.updateMany(
      {},
      { $unset: { preferences: 1 } }
    );
  }
}

module.exports = { Migration, AddUserPreferences };
```

## 📊 MONITORING & ANALYTICS

### 📈 DATABASE PERFORMANCE MONITORING
```javascript
// utils/dbMonitor.js
class DatabaseMonitor {
  constructor() {
    this.metrics = {
      queryCount: 0,
      slowQueries: [],
      connectionCount: 0,
      errors: []
    };
  }

  logQuery(query, duration) {
    this.metrics.queryCount++;
    
    if (duration > 1000) { // Slow query threshold: 1 second
      this.metrics.slowQueries.push({
        query,
        duration,
        timestamp: new Date()
      });
    }
  }

  logError(error) {
    this.metrics.errors.push({
      error: error.message,
      stack: error.stack,
      timestamp: new Date()
    });
  }

  getMetrics() {
    return {
      ...this.metrics,
      averageQueryTime: this.calculateAverageQueryTime(),
      errorRate: this.calculateErrorRate()
    };
  }

  calculateAverageQueryTime() {
    if (this.metrics.slowQueries.length === 0) return 0;
    
    const total = this.metrics.slowQueries.reduce((sum, query) => sum + query.duration, 0);
    return total / this.metrics.slowQueries.length;
  }

  calculateErrorRate() {
    if (this.metrics.queryCount === 0) return 0;
    return (this.metrics.errors.length / this.metrics.queryCount) * 100;
  }
}

module.exports = new DatabaseMonitor();
```

## 📋 DATABASE CHECKLIST

### ✅ MONGODB ATLAS OPTIMIZATION CHECKLIST
- [ ] Connection pooling configured appropriately
- [ ] Indexes created for all query patterns
- [ ] Schema validation rules implemented
- [ ] Aggregation pipelines optimized
- [ ] Caching strategy implemented
- [ ] Migration system in place
- [ ] Monitoring and alerting configured
- [ ] Backup and disaster recovery plan
- [ ] Security rules and access controls
- [ ] Performance testing completed
