document.addEventListener('DOMContentLoaded', function () {
    // Fetch data from the API endpoint
    fetch('/api/chart-data')
        .then(response => response.json())
        .then(data => {
            // --- Trades Over Time Chart ---
            const tradesCtx = document.getElementById('tradesOverTimeChart').getContext('2d');
            if (window.tradesOverTimeChart instanceof Chart) {
                 window.tradesOverTimeChart.destroy();
            }
            window.tradesOverTimeChart = new Chart(tradesCtx, {
                type: 'line',
                data: {
                    labels: data.trades_over_time.labels,
                    datasets: [{
                        label: '# of Trades',
                        data: data.trades_over_time.data,
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // --- Top Tokens by USD Chart ---
            const tokensCtx = document.getElementById('topTokensByUsdChart').getContext('2d');
             if (window.topTokensByUsdChart instanceof Chart) {
                 window.topTokensByUsdChart.destroy();
            }
            window.topTokensByUsdChart = new Chart(tokensCtx, {
                type: 'bar',
                data: {
                    labels: data.top_tokens_by_usd.labels,
                    datasets: [{
                        label: 'Total USD Spent',
                        data: data.top_tokens_by_usd.data,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(255, 206, 86, 0.6)',
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)',
                            'rgba(199, 199, 199, 0.6)',
                            'rgba(83, 102, 255, 0.6)',
                            'rgba(40, 159, 64, 0.6)',
                            'rgba(210, 99, 132, 0.6)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)',
                            'rgba(40, 159, 64, 1)',
                            'rgba(210, 99, 132, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y', // Horizontal bar chart
                    scales: {
                        x: {
                            beginAtZero: true
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                     plugins: {
                        legend: {
                            display: false // Hide legend for bar chart
                        }
                    }
                }
            });
        })
        .catch(error => console.error('Error fetching chart data:', error));
}); 