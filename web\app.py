from flask import Flask, render_template, jsonify
import os
from pymongo import MongoClient
from dotenv import load_dotenv
from datetime import datetime, timedelta
import logging
import pathlib # Import pathlib

# --- Environment Variable Loading ---
# Construct the absolute path to the .env file in the parent directory
current_dir = pathlib.Path(__file__).parent
project_root = current_dir.parent
dotenv_path = project_root / '.env'

# Load environment variables
if dotenv_path.exists():
    load_dotenv(dotenv_path=dotenv_path)
    print(f"Loaded .env file from: {dotenv_path}") # Add print statement for confirmation
else:
    print(f"Warning: .env file not found at {dotenv_path}")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# --- MongoDB Connection ---
def get_mongo_client():
    """Creates and returns a MongoDB client connection."""
    try:
        uri = os.getenv('MONGODB_URI')
        if not uri:
            raise ValueError("MONGODB_URI not found in environment variables")
        client = MongoClient(uri)
        # Test connection
        client.admin.command('ping') 
        logger.info("Successfully connected to MongoDB.")
        return client
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}", exc_info=True)
        return None

mongo_client = get_mongo_client()

def get_db():
    """Returns the MongoDB database object."""
    if not mongo_client:
        return None
    try:
        db = mongo_client.dune_analytics # Use the same DB name as the main app
        return db
    except Exception as e:
        logger.error(f"Failed to get database: {e}", exc_info=True)
        return None

# --- Data Fetching Logic ---
def fetch_recent_trades(limit=50):
    """Fetches the most recent trade rows from the latest Dune results."""
    db = get_db()
    if db is None:
        return []
    try:
        # Find the latest document based on timestamp
        latest_doc = db.query_results.find_one(sort=[("timestamp", -1)])
        if latest_doc and 'results' in latest_doc and 'result' in latest_doc['results']:
            rows = latest_doc['results']['result'].get('rows', [])
            # Convert relevant fields (example: parse dates if needed)
            for row in rows:
                 # Basic type conversion example
                 row['total_usd_spent'] = float(row.get('total_usd_spent', 0))
                 row['total_token_bought'] = float(row.get('total_token_bought', 0))
                 row['unique_traders_per_token'] = int(row.get('unique_traders_per_token', 0))
                 # Assuming first_trade/last_trade are strings
                 try:
                     row['first_trade_dt'] = datetime.strptime(row.get('first_trade', ''), '%Y-%m-%d %H:%M:%S.%f UTC') # Adjust format as needed
                 except ValueError:
                     row['first_trade_dt'] = None
                 try:
                     row['last_trade_dt'] = datetime.strptime(row.get('last_trade', ''), '%Y-%m-%d %H:%M:%S.%f UTC') # Adjust format as needed
                 except ValueError:
                     row['last_trade_dt'] = None

            return rows[:limit]
        else:
             logger.warning("No recent trade data found in MongoDB or structure mismatch.")
             return []
    except Exception as e:
        logger.error(f"Error fetching recent trades from MongoDB: {e}", exc_info=True)
        return []

def aggregate_data_for_charts():
    """Aggregates data suitable for charting."""
    trades = fetch_recent_trades(limit=1000) # Fetch more for aggregation
    if not trades:
        return {
            'trades_over_time': {'labels': [], 'data': []},
            'top_tokens_by_usd': {'labels': [], 'data': []}
        }

    # Example 1: Trades over time (by last_trade hour)
    trades_by_hour = {}
    for trade in trades:
        if trade.get('last_trade_dt'):
            hour_str = trade['last_trade_dt'].strftime('%Y-%m-%d %H:00')
            trades_by_hour[hour_str] = trades_by_hour.get(hour_str, 0) + 1
    
    sorted_hours = sorted(trades_by_hour.keys())
    time_labels = sorted_hours
    time_data = [trades_by_hour[h] for h in sorted_hours]

    # Example 2: Top tokens by total USD spent
    usd_by_token = {}
    for trade in trades:
        symbol = trade.get('token_bought_symbol', 'Unknown')
        usd = trade.get('total_usd_spent', 0)
        usd_by_token[symbol] = usd_by_token.get(symbol, 0) + usd

    # Sort by USD spent and take top 10
    sorted_tokens = sorted(usd_by_token.items(), key=lambda item: item[1], reverse=True)[:10]
    token_labels = [item[0] for item in sorted_tokens]
    token_data = [item[1] for item in sorted_tokens]

    return {
        'trades_over_time': {'labels': time_labels, 'data': time_data},
        'top_tokens_by_usd': {'labels': token_labels, 'data': token_data}
    }


# --- Flask Routes ---
@app.route('/')
def index():
    """Main dashboard page."""
    recent_trades = fetch_recent_trades(limit=20)
    # Pass datetime module to the template context
    return render_template('index.html', trades=recent_trades, datetime=datetime)

@app.route('/api/chart-data')
def chart_data_api():
    """API endpoint to provide data for charts."""
    data = aggregate_data_for_charts()
    return jsonify(data)

@app.route('/health')
def health_check():
    """Basic health check endpoint."""
    if mongo_client and get_db():
        try:
             # Check MongoDB connection again
             mongo_client.admin.command('ping')
             return jsonify({"status": "ok", "mongodb": "connected"}), 200
        except Exception as e:
             logger.error(f"MongoDB health check failed: {e}")
             return jsonify({"status": "error", "mongodb": "disconnected"}), 500
    else:
        return jsonify({"status": "error", "mongodb": "disconnected"}), 500

if __name__ == '__main__':
    # Ensure MongoDB connection is attempted before running
    if not mongo_client:
         logger.critical("Failed to establish MongoDB connection. Aborting Flask app startup.")
    else:
         # Use waitress or gunicorn in production instead of Flask development server
         logger.info("Starting Flask development server on port 5005")
         app.run(host='0.0.0.0', port=5005, debug=False) # debug=False for production focus 