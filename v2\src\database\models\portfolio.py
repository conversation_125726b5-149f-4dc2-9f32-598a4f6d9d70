"""
💼 Portfolio Database Model

Beanie ODM model for paper trading portfolios following DATABASE_PATTERNS.md
with proper indexing, validation, and performance tracking.
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any
from beanie import Document, Indexed
from pydantic import Field, validator
from pymongo import IndexModel, ASCENDING, DESCENDING

from .base import BaseDocument
from ...shared.types import PortfolioStatus


class Portfolio(BaseDocument):
    """
    💼 Paper Trading Portfolio Model
    
    Stores virtual trading portfolios with positions, performance metrics,
    and risk management following V2 architecture patterns.
    """
    
    # 🎯 Core Portfolio Data
    name: str = Field(..., description="Portfolio name")
    description: Optional[str] = Field(None, description="Portfolio description")
    user_id: Optional[str] = Field(None, description="Owner user ID")
    status: PortfolioStatus = Field(default=PortfolioStatus.ACTIVE, description="Portfolio status")
    
    # 💰 Balance and Performance
    initial_balance: Decimal = Field(..., description="Starting balance in USD")
    current_balance: Decimal = Field(..., description="Current cash balance")
    total_invested: Decimal = Field(default=Decimal("0"), description="Total amount invested")
    total_pnl: Decimal = Field(default=Decimal("0"), description="Total realized P&L")
    unrealized_pnl: Decimal = Field(default=Decimal("0"), description="Unrealized P&L")
    total_fees: Decimal = Field(default=Decimal("0"), description="Total fees paid")
    
    # 📊 Performance Metrics
    total_return_percent: Decimal = Field(default=Decimal("0"), description="Total return percentage")
    annualized_return: Optional[Decimal] = Field(None, description="Annualized return")
    sharpe_ratio: Optional[Decimal] = Field(None, description="Sharpe ratio")
    max_drawdown: Decimal = Field(default=Decimal("0"), description="Maximum drawdown")
    win_rate: Decimal = Field(default=Decimal("0"), description="Win rate percentage")
    profit_factor: Optional[Decimal] = Field(None, description="Profit factor")
    
    # 📈 Trade Statistics
    total_trades: int = Field(default=0, description="Total number of trades")
    winning_trades: int = Field(default=0, description="Number of winning trades")
    losing_trades: int = Field(default=0, description="Number of losing trades")
    average_win: Decimal = Field(default=Decimal("0"), description="Average winning trade")
    average_loss: Decimal = Field(default=Decimal("0"), description="Average losing trade")
    largest_win: Decimal = Field(default=Decimal("0"), description="Largest winning trade")
    largest_loss: Decimal = Field(default=Decimal("0"), description="Largest losing trade")
    
    # 🔍 Risk Management
    max_position_size: Decimal = Field(default=Decimal("1000"), description="Maximum position size")
    max_portfolio_risk: Decimal = Field(default=Decimal("0.02"), description="Maximum portfolio risk")
    stop_loss_percent: Decimal = Field(default=Decimal("0.05"), description="Default stop loss")
    take_profit_percent: Decimal = Field(default=Decimal("0.15"), description="Default take profit")
    
    # 📋 Configuration
    trading_strategy: Optional[str] = Field(None, description="Trading strategy name")
    risk_tolerance: str = Field(default="moderate", description="Risk tolerance level")
    auto_trading_enabled: bool = Field(default=False, description="Auto trading status")
    
    # 📊 Historical Data
    daily_balances: List[Dict[str, Any]] = Field(default_factory=list, description="Daily balance history")
    performance_history: List[Dict[str, Any]] = Field(default_factory=list, description="Performance snapshots")
    
    # ⏰ Timing Data
    started_at: datetime = Field(default_factory=datetime.utcnow, description="Portfolio start time")
    last_trade_at: Optional[datetime] = Field(None, description="Last trade timestamp")
    last_updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update time")
    
    @validator("initial_balance", "current_balance", "max_position_size")
    def validate_positive_values(cls, v):
        """Validate positive decimal values"""
        if v <= 0:
            raise ValueError("Value must be positive")
        return v
    
    @validator("max_portfolio_risk", "stop_loss_percent", "take_profit_percent")
    def validate_percentage_values(cls, v):
        """Validate percentage values"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Percentage must be between 0.0 and 1.0")
        return v
    
    @property
    def total_value(self) -> Decimal:
        """Calculate total portfolio value (cash + positions)"""
        return self.current_balance + self.total_invested + self.unrealized_pnl
    
    @property
    def portfolio_age_days(self) -> int:
        """Calculate portfolio age in days"""
        return (datetime.utcnow() - self.started_at).days
    
    @property
    def is_profitable(self) -> bool:
        """Check if portfolio is profitable"""
        return self.total_pnl > 0
    
    def calculate_metrics(self) -> Dict[str, Any]:
        """
        Calculate comprehensive portfolio metrics
        
        Returns:
            Dictionary with calculated metrics
        """
        total_value = self.total_value
        
        # Return calculations
        if self.initial_balance > 0:
            total_return = ((total_value - self.initial_balance) / self.initial_balance) * 100
        else:
            total_return = Decimal("0")
        
        # Win rate calculation
        if self.total_trades > 0:
            win_rate = (self.winning_trades / self.total_trades) * 100
        else:
            win_rate = Decimal("0")
        
        # Profit factor calculation
        total_wins = self.winning_trades * self.average_win if self.average_win > 0 else Decimal("0")
        total_losses = abs(self.losing_trades * self.average_loss) if self.average_loss < 0 else Decimal("0")
        profit_factor = total_wins / total_losses if total_losses > 0 else None
        
        return {
            "total_value": float(total_value),
            "total_return_percent": float(total_return),
            "win_rate": float(win_rate),
            "profit_factor": float(profit_factor) if profit_factor else None,
            "portfolio_age_days": self.portfolio_age_days,
            "is_profitable": self.is_profitable,
            "risk_adjusted_return": float(total_return / max(self.max_drawdown, Decimal("1"))),
            "trades_per_day": self.total_trades / max(self.portfolio_age_days, 1)
        }
    
    def add_daily_snapshot(self) -> None:
        """Add daily balance and performance snapshot"""
        snapshot = {
            "date": datetime.utcnow().date().isoformat(),
            "balance": float(self.current_balance),
            "total_value": float(self.total_value),
            "pnl": float(self.total_pnl),
            "unrealized_pnl": float(self.unrealized_pnl),
            "total_trades": self.total_trades
        }
        
        # Keep only last 365 days
        self.daily_balances.append(snapshot)
        if len(self.daily_balances) > 365:
            self.daily_balances = self.daily_balances[-365:]
    
    class Settings:
        name = "portfolios"
        indexes = [
            # Core query indexes
            IndexModel([("user_id", ASCENDING), ("created_at", DESCENDING)]),
            IndexModel([("status", ASCENDING), ("created_at", DESCENDING)]),
            IndexModel([("name", ASCENDING)]),
            
            # Performance indexes
            IndexModel([("total_return_percent", DESCENDING)]),
            IndexModel([("sharpe_ratio", DESCENDING)]),
            IndexModel([("win_rate", DESCENDING)]),
            
            # Risk indexes
            IndexModel([("max_drawdown", ASCENDING)]),
            IndexModel([("risk_tolerance", ASCENDING)]),
            
            # Activity indexes
            IndexModel([("last_trade_at", DESCENDING)]),
            IndexModel([("auto_trading_enabled", ASCENDING)]),
            
            # Compound indexes
            IndexModel([
                ("user_id", ASCENDING),
                ("status", ASCENDING),
                ("total_return_percent", DESCENDING)
            ]),
            IndexModel([
                ("status", ASCENDING),
                ("total_return_percent", DESCENDING),
                ("created_at", DESCENDING)
            ])
        ]
