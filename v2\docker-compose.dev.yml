# ===========================================
# 🧪 TOKENTRACKER V2 DEVELOPMENT COMPOSE
# ===========================================
# Development environment with hot reload and debugging

version: '3.8'

services:
  # 🗄️ MongoDB Database (Development)
  mongodb:
    image: mongo:7
    container_name: tokentracker-v2-dev-mongodb
    restart: unless-stopped
    ports:
      - "27018:27017"  # Different port to avoid conflicts
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: devpassword
      MONGO_INITDB_DATABASE: tokentracker_v2_dev
    volumes:
      - mongodb_dev_data:/data/db
    networks:
      - tokentracker-dev-network

  # 🔄 Redis Cache (Development)
  redis:
    image: redis:7-alpine
    container_name: tokentracker-v2-dev-redis
    restart: unless-stopped
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    networks:
      - tokentracker-dev-network

  # 🚀 Main Application (Development)
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: tokentracker-v2-dev-app
    restart: unless-stopped
    depends_on:
      - mongodb
      - redis
    environment:
      # Development environment
      NODE_ENV: development
      APP_PORT: 3000
      DEBUG: true
      
      # Database
      MONGODB_URI: ******************************************************************************
      REDIS_URL: redis://redis:6379
      
      # External APIs (from .env file)
      DUNE_API_KEY: ${DUNE_API_KEY}
      DUNE_QUERY_ID: ${DUNE_QUERY_ID}
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN}
      TELEGRAM_CHANNEL_ID: ${TELEGRAM_CHANNEL_ID}
      SOLANA_RPC_URL: ${SOLANA_RPC_URL:-https://api.devnet.solana.com}
      JUPITER_API_URL: ${JUPITER_API_URL}
      RAYDIUM_API_URL: ${RAYDIUM_API_URL}
      
      # Security (development keys)
      JWT_SECRET: dev-jwt-secret-key-not-for-production
      ENCRYPTION_KEY: dev-encryption-key-32-chars-long
      
      # Logging
      LOG_LEVEL: debug
      LOG_FORMAT: console
      
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot reload
      - .:/app
      - /app/node_modules  # Prevent overwriting node_modules
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    networks:
      - tokentracker-dev-network
    stdin_open: true
    tty: true

  # 🧪 Test Runner
  test:
    build:
      context: .
      dockerfile: Dockerfile
      target: testing
    container_name: tokentracker-v2-test
    depends_on:
      - mongodb
      - redis
    environment:
      NODE_ENV: test
      MONGODB_URI: *******************************************************************************
      REDIS_URL: redis://redis:6379/1
      LOG_LEVEL: error
    volumes:
      - .:/app
      - ./test-results:/app/test-results
    networks:
      - tokentracker-dev-network
    profiles:
      - testing
    command: ["python", "-m", "pytest", "tests/", "-v", "--cov=src", "--cov-report=html:/app/test-results/coverage"]

  # 📊 Development Database Admin (MongoDB Express)
  mongo-express:
    image: mongo-express:latest
    container_name: tokentracker-v2-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: devpassword
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin
    depends_on:
      - mongodb
    networks:
      - tokentracker-dev-network
    profiles:
      - admin

  # 🔍 Redis Admin (RedisInsight)
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: tokentracker-v2-redis-insight
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - redis_insight_data:/db
    depends_on:
      - redis
    networks:
      - tokentracker-dev-network
    profiles:
      - admin

  # 📈 Development Metrics (Prometheus)
  prometheus:
    image: prom/prometheus:latest
    container_name: tokentracker-v2-dev-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"  # Different port for dev
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=24h'  # Shorter retention for dev
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus-dev.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_dev_data:/prometheus
    networks:
      - tokentracker-dev-network
    depends_on:
      - app
    profiles:
      - monitoring

  # 📊 Development Dashboard (Grafana)
  grafana:
    image: grafana/grafana:latest
    container_name: tokentracker-v2-dev-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_dev_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - tokentracker-dev-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

  # 🔧 Development Tools Container
  tools:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: tokentracker-v2-tools
    depends_on:
      - mongodb
      - redis
    environment:
      NODE_ENV: development
      MONGODB_URI: ******************************************************************************
      REDIS_URL: redis://redis:6379
    volumes:
      - .:/app
    networks:
      - tokentracker-dev-network
    profiles:
      - tools
    command: ["tail", "-f", "/dev/null"]  # Keep container running

# 🌐 Networks
networks:
  tokentracker-dev-network:
    driver: bridge

# 💾 Volumes
volumes:
  mongodb_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  redis_insight_data:
    driver: local
  prometheus_dev_data:
    driver: local
  grafana_dev_data:
    driver: local
