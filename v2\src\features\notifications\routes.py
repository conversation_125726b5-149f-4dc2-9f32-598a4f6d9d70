"""
🛣️ Notifications Routes

FastAPI routes for notification management including subscription management,
message sending, and Telegram webhook handling.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, Query, Path, Request
from pydantic import BaseModel, Field

from ...config.logging_config import get_logger
from ...shared.types import APIResponse, SignalData
from ...database.models import User

from .notification_manager import NotificationManager, NotificationType, NotificationPriority
from .subscription_manager import SubscriptionManager, SubscriptionType, NotificationPreferences
from .telegram_client import TelegramClient
from .message_formatter import MessageFormatter, MessageStyle

logger = get_logger(__name__)
router = APIRouter()

# Initialize services
notification_manager = NotificationManager()
subscription_manager = SubscriptionManager()
telegram_client = TelegramClient()
message_formatter = MessageFormatter()


# Request/Response Models
class SubscribeRequest(BaseModel):
    """Request model for user subscription"""
    user_id: str = Field(..., description="User ID (Telegram user ID)")
    subscription_types: List[str] = Field(..., description="List of subscription types")
    preferences: Optional[Dict[str, Any]] = Field(None, description="Notification preferences")


class SendNotificationRequest(BaseModel):
    """Request model for sending notifications"""
    type: str = Field(..., description="Notification type")
    priority: str = Field(default="medium", description="Notification priority")
    recipients: List[str] = Field(..., description="List of recipient user IDs")
    data: Dict[str, Any] = Field(..., description="Notification data")
    channels: Optional[List[str]] = Field(default=["telegram"], description="Delivery channels")


class UpdatePreferencesRequest(BaseModel):
    """Request model for updating user preferences"""
    user_id: str = Field(..., description="User ID")
    preferences: Dict[str, Any] = Field(..., description="Updated preferences")


@router.post("/subscribe", response_model=APIResponse)
async def subscribe_user(request: SubscribeRequest):
    """Subscribe user to notification types"""
    try:
        logger.info(f"Subscribing user {request.user_id} to {len(request.subscription_types)} types")
        
        # Convert string subscription types to enum
        subscription_types = []
        for sub_type_str in request.subscription_types:
            try:
                sub_type = SubscriptionType(sub_type_str)
                subscription_types.append(sub_type)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid subscription type: {sub_type_str}"
                )
        
        # Convert preferences if provided
        preferences = None
        if request.preferences:
            preferences = NotificationPreferences(**request.preferences)
        
        # Subscribe user
        success = await subscription_manager.subscribe_user(
            request.user_id,
            subscription_types,
            preferences
        )
        
        if success:
            return APIResponse(
                success=True,
                message=f"User subscribed to {len(subscription_types)} notification types",
                data={"user_id": request.user_id, "subscriptions": request.subscription_types}
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to subscribe user")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error subscribing user: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/unsubscribe", response_model=APIResponse)
async def unsubscribe_user(
    user_id: str = Query(..., description="User ID"),
    subscription_types: List[str] = Query(..., description="Subscription types to remove")
):
    """Unsubscribe user from notification types"""
    try:
        logger.info(f"Unsubscribing user {user_id} from {len(subscription_types)} types")
        
        # Convert string subscription types to enum
        sub_types = []
        for sub_type_str in subscription_types:
            try:
                sub_type = SubscriptionType(sub_type_str)
                sub_types.append(sub_type)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid subscription type: {sub_type_str}"
                )
        
        # Unsubscribe user
        success = await subscription_manager.unsubscribe_user(user_id, sub_types)
        
        if success:
            return APIResponse(
                success=True,
                message=f"User unsubscribed from {len(sub_types)} notification types",
                data={"user_id": user_id, "unsubscribed": subscription_types}
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to unsubscribe user")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error unsubscribing user: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/subscriptions/{user_id}", response_model=APIResponse)
async def get_user_subscriptions(
    user_id: str = Path(..., description="User ID")
):
    """Get user's current subscriptions"""
    try:
        subscriptions = await subscription_manager.get_user_subscriptions(user_id)
        preferences = await subscription_manager.get_user_preferences(user_id)
        
        return APIResponse(
            success=True,
            message="User subscriptions retrieved",
            data={
                "user_id": user_id,
                "subscriptions": subscriptions,
                "preferences": preferences
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting user subscriptions: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/preferences", response_model=APIResponse)
async def update_user_preferences(request: UpdatePreferencesRequest):
    """Update user notification preferences"""
    try:
        logger.info(f"Updating preferences for user {request.user_id}")
        
        # Convert preferences dict to NotificationPreferences object
        preferences = NotificationPreferences(**request.preferences)
        
        # Update preferences
        success = await subscription_manager.update_user_preferences(
            request.user_id,
            preferences
        )
        
        if success:
            return APIResponse(
                success=True,
                message="User preferences updated successfully",
                data={"user_id": request.user_id}
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to update preferences")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user preferences: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/send", response_model=APIResponse)
async def send_notification(request: SendNotificationRequest):
    """Send notification to specified recipients"""
    try:
        logger.info(f"Sending {request.type} notification to {len(request.recipients)} recipients")
        
        # Convert string types to enums
        try:
            notification_type = NotificationType(request.type)
            priority = NotificationPriority(request.priority)
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid type or priority: {str(e)}")
        
        # Send notification based on type
        if notification_type == NotificationType.SIGNAL_ALERT:
            # Expect signal data in request.data
            signal_data = request.data.get("signal")
            if not signal_data:
                raise HTTPException(status_code=400, detail="Signal data required for signal alerts")
            
            # Convert to SignalData object if needed
            if isinstance(signal_data, dict):
                from ...shared.types import SignalData, SignalType, SignalStrength
                signal = SignalData(**signal_data)
            else:
                signal = signal_data
            
            results = await notification_manager.send_signal_alert(
                signal,
                request.recipients,
                priority
            )
        
        elif notification_type == NotificationType.PORTFOLIO_UPDATE:
            portfolio_data = request.data.get("portfolio_data")
            if not portfolio_data:
                raise HTTPException(status_code=400, detail="Portfolio data required")
            
            results = await notification_manager.send_portfolio_update(
                portfolio_data,
                request.recipients,
                priority
            )
        
        elif notification_type == NotificationType.TRADE_EXECUTION:
            trade_data = request.data.get("trade_data")
            if not trade_data:
                raise HTTPException(status_code=400, detail="Trade data required")
            
            results = await notification_manager.send_trade_notification(
                trade_data,
                request.recipients,
                priority
            )
        
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported notification type: {request.type}")
        
        return APIResponse(
            success=True,
            message="Notification sent successfully",
            data=results
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending notification: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/analytics", response_model=APIResponse)
async def get_subscription_analytics():
    """Get subscription analytics and insights"""
    try:
        analytics = await subscription_manager.get_subscription_analytics()
        
        return APIResponse(
            success=True,
            message="Subscription analytics retrieved",
            data=analytics
        )
        
    except Exception as e:
        logger.error(f"Error getting subscription analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/test/signal", response_model=APIResponse)
async def test_signal_notification(
    chat_id: str = Query(..., description="Telegram chat ID"),
    style: str = Query(default="standard", description="Message style")
):
    """Test signal notification formatting"""
    try:
        # Create test signal data
        from ...shared.types import SignalData, SignalType, SignalStrength
        from decimal import Decimal
        
        test_signal = SignalData(
            id="test_signal_123",
            token_address="So11111111111111111111111111111111111111112",
            signal_type=SignalType.BUY,
            strength=SignalStrength.STRONG,
            confidence=0.85,
            entry_price=Decimal("100.50"),
            stop_loss=Decimal("95.25"),
            take_profit=Decimal("115.75"),
            position_size_usd=Decimal("1000"),
            reasoning="Strong technical indicators with RSI oversold and MACD bullish crossover",
            risk_score=0.3,
            expires_at=datetime.utcnow().replace(hour=23, minute=59),
            indicators={
                "rsi": 28.5,
                "macd": 0.0045,
                "macd_signal": 0.0032,
                "volume_ratio": 2.3,
                "trend_direction": "uptrend",
                "momentum_score": 78
            }
        )
        
        # Send test notification
        async with telegram_client as client:
            result = await client.send_signal_alert(chat_id, test_signal, include_actions=True)
        
        return APIResponse(
            success=True,
            message="Test signal notification sent",
            data={"chat_id": chat_id, "result": result}
        )
        
    except Exception as e:
        logger.error(f"Error sending test signal: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
