# ===========================================
# 🚀 TOKENTRACKER V2 PYTHON DEPENDENCIES
# ===========================================

# 🔧 Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 🗄️ Database & Caching
pymongo==4.6.1
motor==3.3.2
redis==5.0.1
beanie==1.23.6

# 🌐 HTTP & API Clients
httpx==0.25.2
aiohttp==3.9.1
websockets==12.0
requests==2.31.0

# 📊 Data Processing & Analysis
pandas==2.1.4
numpy==1.25.2
ta-lib==0.4.28
scikit-learn==1.3.2
scipy==1.11.4

# 📈 Financial & Trading
ccxt==4.1.64
solana==0.30.2
solders==0.18.1
anchorpy==0.19.1

# 📱 Notifications
python-telegram-bot==20.7
discord-webhook==1.3.0
slack-sdk==3.26.1

# ⏰ Scheduling & Background Tasks
apscheduler==3.10.4
celery==5.3.4
redis-py-cluster==2.1.3

# 🔐 Security & Authentication
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# 📝 Logging & Monitoring
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.39.2

# 🧪 Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2
factory-boy==3.3.0

# 🔧 Development Tools
python-dotenv==1.0.0
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7
rich==13.7.0

# 📊 Data Visualization (Optional)
plotly==5.17.0
matplotlib==3.8.2

# 🤖 AI Integration (Optional)
openai==1.3.8
anthropic==0.7.8

# 🔄 Async & Concurrency
asyncio-mqtt==0.16.1
aiofiles==23.2.1
aiocache==0.12.2

# 📧 Email
aiosmtplib==3.0.1
jinja2==3.1.2

# 🔍 Validation & Parsing
marshmallow==3.20.1
cerberus==1.3.5
jsonschema==4.20.0

# 📦 Utilities
python-slugify==8.0.1
arrow==1.3.0
humanize==4.8.0
tenacity==8.2.3

# 🌍 Environment & Configuration
dynaconf==3.2.4
python-decouple==3.8

# 📈 Performance Monitoring
psutil==5.9.6
memory-profiler==0.61.0

# 🔒 Environment Security
python-dotenv==1.0.0
keyring==24.3.0
