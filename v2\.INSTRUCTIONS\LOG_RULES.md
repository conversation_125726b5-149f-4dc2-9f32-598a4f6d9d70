/*
===========================
📘 CLEAN LOGGING RULES FOR AI-GENERATED CODE
===========================

✅ PURPOSE OF LOGGING:
- Track application flow
- Debug errors effectively
- Monitor system behavior
- Provide useful insights in production without clutter

🔰 1. DO NOT USE console.log DIRECTLY
- ❌ Avoid using `console.log`, `console.error`, `console.warn` in production code.
- ✅ Use a centralized logger (e.g., <PERSON>, <PERSON><PERSON>, or custom logger).
  Example:
    logger.info("User created successfully", { userId });
    logger.error("Failed to connect to DB", { error });

🧱 2. LOG LEVELS (USE APPROPRIATELY)
- `trace` → very detailed internal steps (for debugging only)
- `debug` → development-level logs, function inputs, outputs
- `info` → general runtime events (e.g., user created, job started)
- `warn` → something went wrong but app can continue (e.g., deprecated API)
- `error` → failed operations that require attention
- `fatal` → system-critical issues that may cause crash

📏 3. LOG FORMAT
- Logs should be structured (JSON preferred).
- Include:
  - Timestamp
  - Log level
  - Message
  - Contextual metadata (e.g., user ID, request ID, error stack)
  Example:
    {
      "timestamp": "2025-07-10T08:33:20Z",
      "level": "error",
      "message": "User login failed",
      "userId": "123",
      "ip": "***********",
      "stack": "..."
    }

🎯 4. LOGGING RULES & BEST PRACTICES
- Do not log sensitive data (passwords, tokens, card numbers).
- Avoid logging entire objects unless needed — log summaries or IDs.
- Attach request ID or correlation ID to trace logs across services.
- Wrap logger usage in utility functions if needed to standardize behavior.

📦 5. LOGGER STRUCTURE EXAMPLE (Node.js with Winston)
  - /src
    /logger
      index.js         → Create and export logger
      middleware.js    → Express middleware to log incoming requests
      format.js        → Custom log format definition

🧼 6. CLEANUP & ENVIRONMENT HANDLING
- In development: show logs in console, use `debug` or lower levels.
- In production: write logs to files or monitoring system.
- Rotate logs and clear old files periodically.

🌐 7. EXAMPLE LOGGER USAGE

  logger.debug("Fetching user profile", { userId });
  logger.info("User login successful", { userId, device });
  logger.warn("API response delayed", { duration });
  logger.error("Database query failed", { error, query });
  logger.fatal("Uncaught exception", { error });

===========================
📌 TL;DR FOR AI PROMPTING:
"Use a centralized logger with proper levels (debug/info/warn/error). Format logs with timestamps and metadata. Never use console.log. Never log sensitive data. Log must be clean, structured, and environment-aware."
===========================
*/
