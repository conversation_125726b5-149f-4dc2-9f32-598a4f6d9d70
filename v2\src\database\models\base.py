"""
🏗️ Base Document Model

Base model following DATABASE_PATTERNS.md with common fields,
timestamps, and utility methods for all documents.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from beanie import Document
from pydantic import Field
from pymongo import IndexModel, ASCENDING, DESCENDING


class BaseDocument(Document):
    """
    🏗️ Base document with common fields and functionality
    """
    
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    is_active: bool = Field(default=True, description="Active status flag")
    version: int = Field(default=1, description="Document version")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    
    class Settings:
        """Beanie document settings"""
        use_state_management = True
        validate_on_save = True
        
        # Common indexes for all documents
        indexes = [
            IndexModel([("created_at", DESCENDING)], name="created_at_desc"),
            IndexModel([("updated_at", DESCENDING)], name="updated_at_desc"),
            IndexModel([("is_active", ASCENDING)], name="is_active_asc"),
            IndexModel(
                [("is_active", ASCENDING), ("created_at", DESCENDING)], 
                name="active_created_compound"
            ),
        ]
    
    async def save(self, *args, **kwargs):
        """Override save to update timestamp"""
        self.updated_at = datetime.utcnow()
        return await super().save(*args, **kwargs)
    
    async def update(self, *args, **kwargs):
        """Override update to update timestamp"""
        kwargs.setdefault("$set", {})["updated_at"] = datetime.utcnow()
        return await super().update(*args, **kwargs)
    
    async def soft_delete(self):
        """Soft delete by setting is_active to False"""
        self.is_active = False
        self.updated_at = datetime.utcnow()
        await self.save()
    
    async def restore(self):
        """Restore soft deleted document"""
        self.is_active = True
        self.updated_at = datetime.utcnow()
        await self.save()
    
    def increment_version(self):
        """Increment document version"""
        self.version += 1
        self.updated_at = datetime.utcnow()
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata field"""
        if self.metadata is None:
            self.metadata = {}
        self.metadata[key] = value
        self.updated_at = datetime.utcnow()
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata field"""
        if self.metadata is None:
            return default
        return self.metadata.get(key, default)
    
    @classmethod
    async def find_active(cls, *args, **kwargs):
        """Find only active documents"""
        return cls.find({"is_active": True}, *args, **kwargs)
    
    @classmethod
    async def count_active(cls, *args, **kwargs):
        """Count only active documents"""
        return await cls.find({"is_active": True}, *args, **kwargs).count()
    
    def to_dict(self, exclude_none: bool = True) -> Dict[str, Any]:
        """Convert document to dictionary"""
        return self.dict(exclude_none=exclude_none, by_alias=True)
    
    def __repr__(self) -> str:
        """String representation"""
        return f"<{self.__class__.__name__}(id={self.id}, created_at={self.created_at})>"
