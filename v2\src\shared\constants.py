"""
📋 Application Constants

Centralized constants following CLEAN_CODE_RULES.md guidelines
for maintainable and consistent application configuration.
"""

from enum import Enum
from typing import Dict, Any


# 🌐 API Constants
class API_CONSTANTS:
    """API-related constants"""
    
    # Rate Limiting
    DEFAULT_RATE_LIMIT = 100
    BURST_RATE_LIMIT = 200
    RATE_LIMIT_WINDOW = 3600  # 1 hour in seconds
    
    # Timeouts
    DEFAULT_TIMEOUT = 30
    LONG_TIMEOUT = 120
    SHORT_TIMEOUT = 10
    
    # Retry Configuration
    MAX_RETRIES = 3
    RETRY_DELAY = 1.0
    RETRY_BACKOFF = 2.0
    
    # Response Codes
    SUCCESS_CODES = [200, 201, 202]
    CLIENT_ERROR_CODES = range(400, 500)
    SERVER_ERROR_CODES = range(500, 600)


# ❌ Error Constants
class ERROR_CONSTANTS:
    """Error messages and codes"""
    
    # General Errors
    INTERNAL_SERVER_ERROR = "Internal server error occurred"
    VALIDATION_ERROR = "Validation error in request data"
    AUTHENTICATION_ERROR = "Authentication failed"
    AUTHORIZATION_ERROR = "Insufficient permissions"
    RATE_LIMIT_ERROR = "Rate limit exceeded"
    
    # Data Pipeline Errors
    DUNE_API_ERROR = "Failed to fetch data from Dune Analytics"
    SOLANA_RPC_ERROR = "Solana RPC connection failed"
    JUPITER_API_ERROR = "Jupiter API request failed"
    RAYDIUM_API_ERROR = "Raydium API request failed"
    
    # Trading Errors
    INSUFFICIENT_LIQUIDITY = "Insufficient liquidity for trade"
    INVALID_TOKEN_ADDRESS = "Invalid token address provided"
    POSITION_SIZE_EXCEEDED = "Position size exceeds maximum allowed"
    STOP_LOSS_INVALID = "Invalid stop loss percentage"
    
    # Database Errors
    DATABASE_CONNECTION_ERROR = "Database connection failed"
    DOCUMENT_NOT_FOUND = "Requested document not found"
    DUPLICATE_ENTRY_ERROR = "Duplicate entry detected"
    
    # Notification Errors
    TELEGRAM_SEND_ERROR = "Failed to send Telegram message"
    EMAIL_SEND_ERROR = "Failed to send email notification"
    WEBHOOK_ERROR = "Webhook delivery failed"


# 💰 Trading Constants
class TRADING_CONSTANTS:
    """Trading-related constants"""
    
    # Position Sizing
    MIN_POSITION_SIZE_USD = 10.0
    MAX_POSITION_SIZE_USD = 10000.0
    DEFAULT_POSITION_SIZE_USD = 100.0
    
    # Risk Management
    MIN_STOP_LOSS_PERCENT = 1.0
    MAX_STOP_LOSS_PERCENT = 50.0
    DEFAULT_STOP_LOSS_PERCENT = 5.0
    
    MIN_TAKE_PROFIT_PERCENT = 2.0
    MAX_TAKE_PROFIT_PERCENT = 1000.0
    DEFAULT_TAKE_PROFIT_PERCENT = 15.0
    
    # Liquidity Requirements
    MIN_LIQUIDITY_USD = 10000.0
    RECOMMENDED_LIQUIDITY_USD = 50000.0
    
    # Slippage Tolerance
    DEFAULT_SLIPPAGE_PERCENT = 1.0
    MAX_SLIPPAGE_PERCENT = 10.0
    
    # Time Constraints
    MAX_TRADE_AGE_HOURS = 24
    SIGNAL_VALIDITY_MINUTES = 30
    
    # Performance Metrics
    MIN_WIN_RATE_PERCENT = 40.0
    TARGET_SHARPE_RATIO = 1.5
    MAX_DRAWDOWN_PERCENT = 20.0


# 🔍 Signal Processing Constants
class SIGNAL_CONSTANTS:
    """Signal processing constants"""
    
    # Signal Types
    BUY_SIGNAL = "BUY"
    SELL_SIGNAL = "SELL"
    HOLD_SIGNAL = "HOLD"
    
    # Signal Strength
    WEAK_SIGNAL = 1
    MODERATE_SIGNAL = 2
    STRONG_SIGNAL = 3
    VERY_STRONG_SIGNAL = 4
    
    # Technical Indicators
    RSI_OVERSOLD = 30
    RSI_OVERBOUGHT = 70
    RSI_PERIOD = 14
    
    MACD_FAST_PERIOD = 12
    MACD_SLOW_PERIOD = 26
    MACD_SIGNAL_PERIOD = 9
    
    BOLLINGER_PERIOD = 20
    BOLLINGER_STD_DEV = 2
    
    # Volume Analysis
    VOLUME_SPIKE_THRESHOLD = 2.0
    VOLUME_AVERAGE_PERIOD = 20


# 🗄️ Database Constants
class DATABASE_CONSTANTS:
    """Database-related constants"""
    
    # Collection Names
    TOKENS_COLLECTION = "tokens"
    SIGNALS_COLLECTION = "signals"
    TRADES_COLLECTION = "trades"
    PORTFOLIOS_COLLECTION = "portfolios"
    USERS_COLLECTION = "users"
    QUERY_RESULTS_COLLECTION = "query_results"
    PERFORMANCE_METRICS_COLLECTION = "performance_metrics"
    
    # Index Names
    TOKEN_ADDRESS_INDEX = "token_address_1"
    TIMESTAMP_INDEX = "timestamp_-1"
    USER_ID_INDEX = "user_id_1"
    SIGNAL_TYPE_INDEX = "signal_type_1"
    
    # Document Limits
    MAX_DOCUMENT_SIZE_MB = 16
    MAX_ARRAY_SIZE = 1000
    
    # TTL Settings
    CACHE_TTL_SECONDS = 3600
    SESSION_TTL_SECONDS = 86400
    LOG_TTL_DAYS = 30


# 🌐 Network Constants
class NETWORK_CONSTANTS:
    """Blockchain and network constants"""
    
    # Solana
    SOLANA_MAINNET_RPC = "https://api.mainnet-beta.solana.com"
    SOLANA_DEVNET_RPC = "https://api.devnet.solana.com"
    SOLANA_TESTNET_RPC = "https://api.testnet.solana.com"
    
    # DEX Program IDs
    RAYDIUM_PROGRAM_ID = "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"
    JUPITER_PROGRAM_ID = "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4"
    ORCA_PROGRAM_ID = "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
    
    # Token Standards
    SPL_TOKEN_PROGRAM_ID = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
    ASSOCIATED_TOKEN_PROGRAM_ID = "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"
    
    # Common Token Addresses
    WSOL_ADDRESS = "So11111111111111111111111111111111111111112"
    USDC_ADDRESS = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    USDT_ADDRESS = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"


# 📊 Monitoring Constants
class MONITORING_CONSTANTS:
    """Monitoring and metrics constants"""
    
    # Health Check Intervals
    HEALTH_CHECK_INTERVAL = 60
    DEEP_HEALTH_CHECK_INTERVAL = 300
    
    # Performance Thresholds
    MAX_RESPONSE_TIME_MS = 1000
    MAX_ERROR_RATE_PERCENT = 1.0
    MIN_UPTIME_PERCENT = 99.9
    
    # Alert Thresholds
    HIGH_CPU_PERCENT = 80.0
    HIGH_MEMORY_PERCENT = 85.0
    HIGH_DISK_PERCENT = 90.0
    
    # Metrics Retention
    METRICS_RETENTION_DAYS = 30
    DETAILED_METRICS_RETENTION_DAYS = 7


# 🎯 Feature Flags
class FEATURE_FLAGS:
    """Feature toggle constants"""
    
    PAPER_TRADING_ENABLED = True
    LIVE_TRADING_ENABLED = False
    AI_SIGNALS_ENABLED = True
    ADVANCED_ANALYTICS_ENABLED = True
    MOBILE_NOTIFICATIONS_ENABLED = False
    BACKTESTING_ENABLED = True
    MULTI_DEX_ENABLED = True
    ARBITRAGE_ENABLED = False


# 📱 Notification Constants
class NOTIFICATION_CONSTANTS:
    """Notification system constants"""
    
    # Message Types
    SIGNAL_NOTIFICATION = "signal"
    TRADE_NOTIFICATION = "trade"
    ALERT_NOTIFICATION = "alert"
    SYSTEM_NOTIFICATION = "system"
    
    # Priority Levels
    LOW_PRIORITY = 1
    MEDIUM_PRIORITY = 2
    HIGH_PRIORITY = 3
    CRITICAL_PRIORITY = 4
    
    # Delivery Methods
    TELEGRAM_DELIVERY = "telegram"
    EMAIL_DELIVERY = "email"
    WEBHOOK_DELIVERY = "webhook"
    PUSH_DELIVERY = "push"
    
    # Rate Limits
    MAX_NOTIFICATIONS_PER_HOUR = 100
    MAX_CRITICAL_NOTIFICATIONS_PER_HOUR = 20
