version: '3.8'

services:
  mongodb:
    image: mongo:latest
    container_name: mongodb
    ports:
      - "27018:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_DATABASE=dune_analytics
    networks:
      - app-network

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dune-analytics-app
    depends_on:
      - mongodb
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/dune_analytics
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    networks:
      - app-network
    restart: unless-stopped

  web:
    build:
      context: .
      dockerfile: Dockerfile.web
    container_name: dune-analytics-web
    depends_on:
      - mongodb
      - app
    ports:
      - "5005:5005"
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/dune_analytics
    volumes:
      - ./.env:/app/.env
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge

volumes:
  mongodb_data:
