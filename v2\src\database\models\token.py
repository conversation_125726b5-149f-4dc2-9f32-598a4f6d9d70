"""
🪙 Token Model

Token data model with comprehensive information,
metrics, and validation following V2 patterns.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any
from pydantic import Field, validator
from pymongo import IndexModel, ASCENDING, DESCENDING, TEXT

from .base import BaseDocument


class Token(BaseDocument):
    """
    🪙 Token document model with comprehensive data
    """
    
    # Core token information
    address: str = Field(..., description="Token mint address", unique=True)
    symbol: str = Field(..., description="Token symbol")
    name: str = Field(..., description="Token name")
    decimals: int = Field(..., description="Token decimals")
    
    # Market data
    price_usd: Optional[Decimal] = Field(None, description="Current price in USD")
    market_cap: Optional[Decimal] = Field(None, description="Market capitalization")
    volume_24h: Optional[Decimal] = Field(None, description="24h trading volume")
    volume_7d: Optional[Decimal] = Field(None, description="7d trading volume")
    liquidity_usd: Optional[Decimal] = Field(None, description="Available liquidity")
    
    # Price changes
    price_change_1h: Optional[Decimal] = Field(None, description="1h price change %")
    price_change_24h: Optional[Decimal] = Field(None, description="24h price change %")
    price_change_7d: Optional[Decimal] = Field(None, description="7d price change %")
    
    # Trading metrics
    holder_count: Optional[int] = Field(None, description="Number of token holders")
    transaction_count_24h: Optional[int] = Field(None, description="24h transaction count")
    unique_traders_24h: Optional[int] = Field(None, description="24h unique traders")
    
    # DEX information
    raydium_pool_address: Optional[str] = Field(None, description="Raydium pool address")
    jupiter_supported: bool = Field(default=False, description="Available on Jupiter")
    orca_supported: bool = Field(default=False, description="Available on Orca")
    
    # Risk assessment
    risk_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Risk assessment score")
    liquidity_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Liquidity score")
    volatility_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Volatility score")
    
    # Metadata
    logo_url: Optional[str] = Field(None, description="Token logo URL")
    website: Optional[str] = Field(None, description="Official website")
    twitter: Optional[str] = Field(None, description="Twitter handle")
    telegram: Optional[str] = Field(None, description="Telegram channel")
    description: Optional[str] = Field(None, description="Token description")
    
    # Tracking
    first_seen: datetime = Field(default_factory=datetime.utcnow, description="First detection time")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last data update")
    data_sources: List[str] = Field(default_factory=list, description="Data source list")
    
    # Flags
    is_verified: bool = Field(default=False, description="Verification status")
    is_trending: bool = Field(default=False, description="Trending status")
    is_watchlisted: bool = Field(default=False, description="Watchlist status")
    
    class Settings:
        name = "tokens"
        indexes = [
            # Unique address index
            IndexModel([("address", ASCENDING)], unique=True, name="address_unique"),
            
            # Symbol and name search
            IndexModel([("symbol", ASCENDING)], name="symbol_asc"),
            IndexModel([("name", TEXT), ("symbol", TEXT)], name="name_symbol_text"),
            
            # Market data queries
            IndexModel([("market_cap", DESCENDING)], name="market_cap_desc"),
            IndexModel([("volume_24h", DESCENDING)], name="volume_24h_desc"),
            IndexModel([("liquidity_usd", DESCENDING)], name="liquidity_desc"),
            
            # Price change queries
            IndexModel([("price_change_24h", DESCENDING)], name="price_change_24h_desc"),
            IndexModel([("price_change_7d", DESCENDING)], name="price_change_7d_desc"),
            
            # Status and flags
            IndexModel([("is_verified", ASCENDING), ("market_cap", DESCENDING)], name="verified_market_cap"),
            IndexModel([("is_trending", ASCENDING), ("volume_24h", DESCENDING)], name="trending_volume"),
            IndexModel([("is_watchlisted", ASCENDING), ("last_updated", DESCENDING)], name="watchlist_updated"),
            
            # DEX support
            IndexModel([("jupiter_supported", ASCENDING)], name="jupiter_supported"),
            IndexModel([("raydium_pool_address", ASCENDING)], name="raydium_pool"),
            
            # Risk and scoring
            IndexModel([("risk_score", ASCENDING)], name="risk_score_asc"),
            IndexModel([("liquidity_score", DESCENDING)], name="liquidity_score_desc"),
            
            # Time-based queries
            IndexModel([("first_seen", DESCENDING)], name="first_seen_desc"),
            IndexModel([("last_updated", DESCENDING)], name="last_updated_desc"),
            
            # Compound indexes for common queries
            IndexModel([
                ("is_active", ASCENDING),
                ("jupiter_supported", ASCENDING),
                ("liquidity_usd", DESCENDING)
            ], name="active_jupiter_liquidity"),
            
            IndexModel([
                ("is_verified", ASCENDING),
                ("is_active", ASCENDING),
                ("volume_24h", DESCENDING)
            ], name="verified_active_volume"),
        ]
    
    @validator("address")
    def validate_address(cls, v):
        """Validate Solana address format"""
        if not v or len(v) < 32 or len(v) > 44:
            raise ValueError("Invalid Solana token address")
        return v
    
    @validator("symbol")
    def validate_symbol(cls, v):
        """Validate token symbol"""
        if not v or len(v) > 20:
            raise ValueError("Token symbol must be 1-20 characters")
        return v.upper()
    
    @validator("decimals")
    def validate_decimals(cls, v):
        """Validate token decimals"""
        if v < 0 or v > 18:
            raise ValueError("Token decimals must be between 0 and 18")
        return v
    
    @validator("risk_score", "liquidity_score", "volatility_score")
    def validate_scores(cls, v):
        """Validate score values"""
        if v is not None and (v < 0.0 or v > 1.0):
            raise ValueError("Score must be between 0.0 and 1.0")
        return v
    
    async def update_market_data(self, market_data: Dict[str, Any]) -> None:
        """Update market data fields"""
        update_fields = {}
        
        # Map market data fields
        field_mapping = {
            "price": "price_usd",
            "market_cap": "market_cap",
            "volume_24h": "volume_24h",
            "volume_7d": "volume_7d",
            "liquidity": "liquidity_usd",
            "price_change_1h": "price_change_1h",
            "price_change_24h": "price_change_24h",
            "price_change_7d": "price_change_7d",
            "holder_count": "holder_count",
            "transaction_count_24h": "transaction_count_24h",
            "unique_traders_24h": "unique_traders_24h"
        }
        
        for source_field, target_field in field_mapping.items():
            if source_field in market_data and market_data[source_field] is not None:
                update_fields[target_field] = market_data[source_field]
        
        if update_fields:
            update_fields["last_updated"] = datetime.utcnow()
            await self.update({"$set": update_fields})
    
    async def calculate_scores(self) -> None:
        """Calculate risk, liquidity, and volatility scores"""
        # Risk score calculation (simplified)
        risk_factors = []
        
        if self.holder_count and self.holder_count < 100:
            risk_factors.append(0.3)
        if self.liquidity_usd and self.liquidity_usd < 10000:
            risk_factors.append(0.4)
        if self.volume_24h and self.volume_24h < 1000:
            risk_factors.append(0.2)
        if not self.is_verified:
            risk_factors.append(0.1)
        
        self.risk_score = min(1.0, sum(risk_factors))
        
        # Liquidity score (0 = low, 1 = high)
        if self.liquidity_usd:
            if self.liquidity_usd >= 1000000:
                self.liquidity_score = 1.0
            elif self.liquidity_usd >= 100000:
                self.liquidity_score = 0.8
            elif self.liquidity_usd >= 50000:
                self.liquidity_score = 0.6
            elif self.liquidity_usd >= 10000:
                self.liquidity_score = 0.4
            else:
                self.liquidity_score = 0.2
        
        # Volatility score based on price changes
        volatility_factors = []
        if self.price_change_24h:
            volatility_factors.append(abs(float(self.price_change_24h)) / 100)
        if self.price_change_7d:
            volatility_factors.append(abs(float(self.price_change_7d)) / 100)
        
        if volatility_factors:
            self.volatility_score = min(1.0, sum(volatility_factors) / len(volatility_factors))
        
        await self.save()
    
    def is_tradeable(self) -> bool:
        """Check if token is suitable for trading"""
        return (
            self.is_active and
            self.jupiter_supported and
            self.liquidity_usd and self.liquidity_usd >= 10000 and
            self.risk_score and self.risk_score <= 0.7
        )
    
    def get_display_name(self) -> str:
        """Get display name for UI"""
        return f"{self.symbol} ({self.name})" if self.name != self.symbol else self.symbol
    
    class Config:
        json_encoders = {
            Decimal: str,
            datetime: lambda v: v.isoformat()
        }
