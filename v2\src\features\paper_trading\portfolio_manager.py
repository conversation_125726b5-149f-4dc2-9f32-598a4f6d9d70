"""
💼 Portfolio Manager

Virtual portfolio management with position tracking, balance calculation,
and performance metrics following V2 architecture patterns.
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from uuid import uuid4

from ...config.logging_config import get_logger
from ...shared.types import PortfolioStatus, SignalType, OrderType, TradeStatus
from ...database.models import Portfolio, Trade, Signal
from ...features.data_pipeline import DataAggregator

logger = get_logger(__name__)


class PortfolioManager:
    """
    💼 Portfolio Manager
    
    Manages virtual trading portfolios including:
    - Portfolio creation and configuration
    - Position tracking and management
    - Balance and P&L calculation
    - Risk management
    - Performance monitoring
    """
    
    def __init__(self):
        self.logger = logger
        self.data_aggregator = DataAggregator()
    
    async def create_portfolio(
        self,
        name: str,
        initial_balance: Decimal,
        user_id: Optional[str] = None,
        description: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Portfolio:
        """
        Create a new virtual trading portfolio
        
        Args:
            name: Portfolio name
            initial_balance: Starting balance in USD
            user_id: Owner user ID
            description: Portfolio description
            config: Portfolio configuration
            
        Returns:
            Created Portfolio object
        """
        try:
            self.logger.info(f"Creating portfolio: {name} with balance ${initial_balance}")
            
            # Default configuration
            default_config = {
                "max_position_size": float(initial_balance * Decimal("0.1")),  # 10% max position
                "max_portfolio_risk": 0.02,  # 2% portfolio risk
                "stop_loss_percent": 0.05,   # 5% stop loss
                "take_profit_percent": 0.15, # 15% take profit
                "risk_tolerance": "moderate"
            }
            
            if config:
                default_config.update(config)
            
            # Create portfolio document
            portfolio = Portfolio(
                name=name,
                description=description,
                user_id=user_id,
                initial_balance=initial_balance,
                current_balance=initial_balance,
                status=PortfolioStatus.ACTIVE,
                max_position_size=Decimal(str(default_config["max_position_size"])),
                max_portfolio_risk=Decimal(str(default_config["max_portfolio_risk"])),
                stop_loss_percent=Decimal(str(default_config["stop_loss_percent"])),
                take_profit_percent=Decimal(str(default_config["take_profit_percent"])),
                risk_tolerance=default_config["risk_tolerance"]
            )
            
            await portfolio.save()
            
            # Add initial balance snapshot
            portfolio.add_daily_snapshot()
            await portfolio.save()
            
            self.logger.info(f"Portfolio created successfully: {portfolio.id}")
            return portfolio
            
        except Exception as e:
            self.logger.error(f"Error creating portfolio: {str(e)}")
            raise
    
    async def get_portfolio(self, portfolio_id: str) -> Optional[Portfolio]:
        """Get portfolio by ID"""
        try:
            return await Portfolio.find_one({"_id": portfolio_id})
        except Exception as e:
            self.logger.error(f"Error retrieving portfolio {portfolio_id}: {str(e)}")
            return None
    
    async def get_user_portfolios(self, user_id: str) -> List[Portfolio]:
        """Get all portfolios for a user"""
        try:
            return await Portfolio.find({"user_id": user_id}).sort("-created_at").to_list()
        except Exception as e:
            self.logger.error(f"Error retrieving portfolios for user {user_id}: {str(e)}")
            return []
    
    async def update_portfolio_balance(self, portfolio_id: str, amount: Decimal, operation: str = "add") -> bool:
        """
        Update portfolio cash balance
        
        Args:
            portfolio_id: Portfolio ID
            amount: Amount to add or subtract
            operation: "add" or "subtract"
            
        Returns:
            Success status
        """
        try:
            portfolio = await self.get_portfolio(portfolio_id)
            if not portfolio:
                return False
            
            if operation == "add":
                portfolio.current_balance += amount
            elif operation == "subtract":
                if portfolio.current_balance >= amount:
                    portfolio.current_balance -= amount
                else:
                    self.logger.warning(f"Insufficient balance for operation: {portfolio.current_balance} < {amount}")
                    return False
            
            portfolio.last_updated_at = datetime.utcnow()
            await portfolio.save()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating portfolio balance: {str(e)}")
            return False
    
    async def get_portfolio_positions(self, portfolio_id: str) -> List[Dict[str, Any]]:
        """
        Get current positions for a portfolio
        
        Args:
            portfolio_id: Portfolio ID
            
        Returns:
            List of position data
        """
        try:
            # Get all executed trades for the portfolio
            trades = await Trade.find({
                "portfolio_id": portfolio_id,
                "status": TradeStatus.EXECUTED
            }).sort("execution_time").to_list()
            
            # Calculate positions by token
            positions = {}
            
            for trade in trades:
                token = trade.token_address
                
                if token not in positions:
                    positions[token] = {
                        "token_address": token,
                        "quantity": Decimal("0"),
                        "total_cost": Decimal("0"),
                        "average_price": Decimal("0"),
                        "realized_pnl": Decimal("0"),
                        "trades": []
                    }
                
                position = positions[token]
                
                if trade.side == SignalType.BUY:
                    # Add to position
                    total_quantity = position["quantity"] + trade.quantity
                    total_cost = position["total_cost"] + trade.value_usd
                    
                    if total_quantity > 0:
                        position["average_price"] = total_cost / total_quantity
                    
                    position["quantity"] = total_quantity
                    position["total_cost"] = total_cost
                    
                elif trade.side == SignalType.SELL:
                    # Reduce position
                    if position["quantity"] >= trade.quantity:
                        # Calculate realized P&L
                        sell_value = trade.quantity * position["average_price"]
                        realized_pnl = trade.value_usd - sell_value
                        position["realized_pnl"] += realized_pnl
                        
                        # Reduce position
                        position["quantity"] -= trade.quantity
                        position["total_cost"] -= sell_value
                    else:
                        self.logger.warning(f"Sell quantity exceeds position: {trade.quantity} > {position['quantity']}")
                
                position["trades"].append({
                    "trade_id": str(trade.id),
                    "side": trade.side,
                    "quantity": float(trade.quantity),
                    "price": float(trade.price),
                    "value": float(trade.value_usd),
                    "execution_time": trade.execution_time
                })
            
            # Filter out zero positions and add current market data
            active_positions = []
            for token, position in positions.items():
                if position["quantity"] > 0:
                    # Get current market price
                    market_data = await self.data_aggregator.get_token_data(token)
                    current_price = market_data.price if market_data else position["average_price"]
                    
                    # Calculate unrealized P&L
                    current_value = position["quantity"] * current_price
                    unrealized_pnl = current_value - position["total_cost"]
                    
                    position_data = {
                        "token_address": token,
                        "quantity": float(position["quantity"]),
                        "average_price": float(position["average_price"]),
                        "current_price": float(current_price),
                        "current_value": float(current_value),
                        "total_cost": float(position["total_cost"]),
                        "unrealized_pnl": float(unrealized_pnl),
                        "unrealized_pnl_percent": float((unrealized_pnl / position["total_cost"]) * 100) if position["total_cost"] > 0 else 0,
                        "realized_pnl": float(position["realized_pnl"]),
                        "trade_count": len(position["trades"]),
                        "last_trade": position["trades"][-1] if position["trades"] else None
                    }
                    
                    active_positions.append(position_data)
            
            return active_positions
            
        except Exception as e:
            self.logger.error(f"Error getting portfolio positions: {str(e)}")
            return []
    
    async def calculate_portfolio_value(self, portfolio_id: str) -> Dict[str, Any]:
        """
        Calculate total portfolio value and metrics
        
        Args:
            portfolio_id: Portfolio ID
            
        Returns:
            Portfolio value breakdown
        """
        try:
            portfolio = await self.get_portfolio(portfolio_id)
            if not portfolio:
                return {}
            
            positions = await self.get_portfolio_positions(portfolio_id)
            
            # Calculate totals
            total_position_value = sum(pos["current_value"] for pos in positions)
            total_unrealized_pnl = sum(pos["unrealized_pnl"] for pos in positions)
            total_realized_pnl = sum(pos["realized_pnl"] for pos in positions)
            
            total_value = portfolio.current_balance + Decimal(str(total_position_value))
            total_pnl = Decimal(str(total_realized_pnl + total_unrealized_pnl))
            
            # Calculate return percentage
            if portfolio.initial_balance > 0:
                total_return_percent = ((total_value - portfolio.initial_balance) / portfolio.initial_balance) * 100
            else:
                total_return_percent = Decimal("0")
            
            return {
                "portfolio_id": portfolio_id,
                "cash_balance": float(portfolio.current_balance),
                "position_value": total_position_value,
                "total_value": float(total_value),
                "initial_balance": float(portfolio.initial_balance),
                "total_pnl": float(total_pnl),
                "realized_pnl": total_realized_pnl,
                "unrealized_pnl": total_unrealized_pnl,
                "total_return_percent": float(total_return_percent),
                "position_count": len(positions),
                "positions": positions,
                "last_updated": datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating portfolio value: {str(e)}")
            return {}
    
    async def update_portfolio_metrics(self, portfolio_id: str) -> bool:
        """Update portfolio performance metrics"""
        try:
            portfolio = await self.get_portfolio(portfolio_id)
            if not portfolio:
                return False
            
            # Get portfolio value breakdown
            value_data = await self.calculate_portfolio_value(portfolio_id)
            if not value_data:
                return False
            
            # Update portfolio metrics
            portfolio.total_pnl = Decimal(str(value_data["total_pnl"]))
            portfolio.unrealized_pnl = Decimal(str(value_data["unrealized_pnl"]))
            portfolio.total_return_percent = Decimal(str(value_data["total_return_percent"]))
            portfolio.last_updated_at = datetime.utcnow()
            
            # Get trade statistics
            trades = await Trade.find({
                "portfolio_id": portfolio_id,
                "status": TradeStatus.EXECUTED
            }).to_list()
            
            portfolio.total_trades = len(trades)
            
            # Calculate win/loss statistics
            profitable_trades = [t for t in trades if t.pnl > 0]
            losing_trades = [t for t in trades if t.pnl < 0]
            
            portfolio.winning_trades = len(profitable_trades)
            portfolio.losing_trades = len(losing_trades)
            
            if profitable_trades:
                portfolio.average_win = sum(t.pnl for t in profitable_trades) / len(profitable_trades)
                portfolio.largest_win = max(t.pnl for t in profitable_trades)
            
            if losing_trades:
                portfolio.average_loss = sum(t.pnl for t in losing_trades) / len(losing_trades)
                portfolio.largest_loss = min(t.pnl for t in losing_trades)
            
            # Calculate win rate
            if portfolio.total_trades > 0:
                portfolio.win_rate = (portfolio.winning_trades / portfolio.total_trades) * 100
            
            await portfolio.save()
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating portfolio metrics: {str(e)}")
            return False
    
    async def check_risk_limits(self, portfolio_id: str, trade_value: Decimal) -> Dict[str, Any]:
        """
        Check if a trade would violate risk limits
        
        Args:
            portfolio_id: Portfolio ID
            trade_value: Proposed trade value
            
        Returns:
            Risk check results
        """
        try:
            portfolio = await self.get_portfolio(portfolio_id)
            if not portfolio:
                return {"valid": False, "reason": "Portfolio not found"}
            
            value_data = await self.calculate_portfolio_value(portfolio_id)
            total_value = Decimal(str(value_data.get("total_value", 0)))
            
            # Check position size limit
            if trade_value > portfolio.max_position_size:
                return {
                    "valid": False,
                    "reason": f"Trade value ${trade_value} exceeds max position size ${portfolio.max_position_size}"
                }
            
            # Check portfolio risk limit
            risk_percent = trade_value / total_value if total_value > 0 else 1
            if risk_percent > portfolio.max_portfolio_risk:
                return {
                    "valid": False,
                    "reason": f"Trade risk {risk_percent:.1%} exceeds max portfolio risk {portfolio.max_portfolio_risk:.1%}"
                }
            
            # Check available balance for buy orders
            if trade_value > portfolio.current_balance:
                return {
                    "valid": False,
                    "reason": f"Insufficient balance: ${portfolio.current_balance} < ${trade_value}"
                }
            
            return {"valid": True}
            
        except Exception as e:
            self.logger.error(f"Error checking risk limits: {str(e)}")
            return {"valid": False, "reason": "Risk check error"}
