"""
🧪 Basic Validation Tests

Simple tests for data validation functionality that don't require external dependencies.
"""

import re
from datetime import datetime, timedelta
from decimal import Decimal


class ValidationResult:
    """Simple validation result for testing"""
    def __init__(self):
        self.is_valid = True
        self.errors = []
        self.warnings = []
        self.confidence_score = 1.0
    
    def add_error(self, error: str):
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str):
        self.warnings.append(warning)


class SimpleDataValidator:
    """Simplified data validator for testing"""
    
    def __init__(self):
        self.solana_address_pattern = re.compile(r'^[1-9A-HJ-NP-Za-km-z]{32,44}$')
        self.min_liquidity_usd = 50000.0
        self.max_price_deviation = 0.10
        self.max_data_age_minutes = 30
    
    def validate_token_address(self, token_address: str) -> ValidationResult:
        """Validate Solana token address format"""
        result = ValidationResult()
        
        if not token_address:
            result.add_error("Token address is required")
            return result
        
        if not self.solana_address_pattern.match(token_address):
            result.add_error("Invalid Solana address format")
            return result
        
        if len(token_address) < 32 or len(token_address) > 44:
            result.add_error("Invalid Solana address length")
            return result
        
        # Check for common invalid addresses
        invalid_addresses = [
            "11111111111111111111111111111111",
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        ]
        
        if token_address in invalid_addresses:
            result.add_error("Address is a system program, not a token")
            return result
        
        return result
    
    def validate_liquidity_threshold(self, liquidity_usd: float) -> ValidationResult:
        """Validate liquidity meets minimum threshold"""
        result = ValidationResult()
        
        if liquidity_usd is None:
            result.add_warning("No liquidity data available")
            result.confidence_score = 0.5
            return result
        
        if liquidity_usd <= 0:
            result.add_error("Invalid liquidity value")
            return result
        
        if liquidity_usd < self.min_liquidity_usd:
            result.add_error(
                f"Liquidity ${liquidity_usd:,.2f} below minimum threshold ${self.min_liquidity_usd:,.2f}"
            )
            return result
        
        # Warning for low liquidity
        warning_threshold = self.min_liquidity_usd * 2
        if liquidity_usd < warning_threshold:
            result.add_warning(f"Low liquidity detected: ${liquidity_usd:,.2f}")
            result.confidence_score = 0.8
        
        return result
    
    def validate_data_freshness(self, timestamp: datetime) -> ValidationResult:
        """Validate data freshness"""
        result = ValidationResult()
        
        if not timestamp:
            result.add_error("No timestamp provided")
            return result
        
        now = datetime.utcnow()
        age = now - timestamp
        max_age = timedelta(minutes=self.max_data_age_minutes)
        
        if age > max_age:
            result.add_error(f"Data is stale: {age.total_seconds()/60:.1f} minutes old")
            return result
        
        # Warning for data approaching staleness
        warning_threshold = max_age * 0.8
        if age > warning_threshold:
            result.add_warning(f"Data approaching staleness: {age.total_seconds()/60:.1f} minutes old")
            result.confidence_score = 0.9
        
        return result


def test_token_address_validation():
    """Test token address validation"""
    validator = SimpleDataValidator()
    
    # Test valid addresses
    valid_addresses = [
        "So11111111111111111111111111111111111111112",  # SOL
        "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
        "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",  # USDT
    ]
    
    for address in valid_addresses:
        result = validator.validate_token_address(address)
        assert result.is_valid, f"Valid address {address} should pass validation"
        print(f"✅ Valid address test passed: {address[:8]}...")
    
    # Test invalid addresses
    invalid_addresses = [
        "",  # Empty
        "invalid",  # Too short
        "11111111111111111111111111111111",  # System program
        "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",  # Token program
        "not_a_valid_solana_address_at_all",  # Invalid format
    ]
    
    for address in invalid_addresses:
        result = validator.validate_token_address(address)
        assert not result.is_valid, f"Invalid address {address} should fail validation"
        print(f"❌ Invalid address test passed: {address[:20]}...")


def test_liquidity_validation():
    """Test liquidity threshold validation"""
    validator = SimpleDataValidator()
    
    # Test valid liquidity (above threshold)
    result = validator.validate_liquidity_threshold(100000.0)
    assert result.is_valid, "High liquidity should pass validation"
    print("✅ High liquidity test passed")
    
    # Test low liquidity (below threshold)
    result = validator.validate_liquidity_threshold(1000.0)
    assert not result.is_valid, "Low liquidity should fail validation"
    assert len(result.errors) > 0, "Low liquidity should have errors"
    print("❌ Low liquidity test passed")
    
    # Test warning threshold
    result = validator.validate_liquidity_threshold(75000.0)  # Between min and warning
    assert result.is_valid, "Medium liquidity should pass validation"
    assert len(result.warnings) > 0, "Medium liquidity should have warnings"
    print("⚠️ Medium liquidity test passed")
    
    # Test invalid liquidity
    result = validator.validate_liquidity_threshold(-1000.0)
    assert not result.is_valid, "Negative liquidity should fail validation"
    print("❌ Negative liquidity test passed")


def test_data_freshness():
    """Test data freshness validation"""
    validator = SimpleDataValidator()
    
    # Test fresh data
    fresh_timestamp = datetime.utcnow() - timedelta(minutes=5)
    result = validator.validate_data_freshness(fresh_timestamp)
    assert result.is_valid, "Fresh data should pass validation"
    print("✅ Fresh data test passed")
    
    # Test stale data
    stale_timestamp = datetime.utcnow() - timedelta(minutes=45)
    result = validator.validate_data_freshness(stale_timestamp)
    assert not result.is_valid, "Stale data should fail validation"
    assert len(result.errors) > 0, "Stale data should have errors"
    print("❌ Stale data test passed")
    
    # Test approaching staleness
    approaching_stale = datetime.utcnow() - timedelta(minutes=25)
    result = validator.validate_data_freshness(approaching_stale)
    assert result.is_valid, "Approaching stale data should still pass"
    assert len(result.warnings) > 0, "Approaching stale data should have warnings"
    print("⚠️ Approaching stale data test passed")


def test_rate_limiting_logic():
    """Test rate limiting logic"""
    import time
    
    class SimpleRateLimiter:
        def __init__(self, max_requests=5, window_seconds=10):
            self.max_requests = max_requests
            self.window_seconds = window_seconds
            self.request_timestamps = []
        
        def check_rate_limit(self):
            now = time.time()
            
            # Remove old timestamps
            self.request_timestamps = [
                ts for ts in self.request_timestamps 
                if now - ts < self.window_seconds
            ]
            
            # Check if within limits
            if len(self.request_timestamps) >= self.max_requests:
                return False
            
            # Add current timestamp
            self.request_timestamps.append(now)
            return True
    
    limiter = SimpleRateLimiter(max_requests=3, window_seconds=1)
    
    # Should allow first few requests
    assert limiter.check_rate_limit() == True
    assert limiter.check_rate_limit() == True
    assert limiter.check_rate_limit() == True
    
    # Should block after limit
    assert limiter.check_rate_limit() == False
    print("✅ Rate limiting test passed")


def run_all_tests():
    """Run all basic tests"""
    print("🧪 Running basic validation tests...")
    print()
    
    try:
        test_token_address_validation()
        print()
        
        test_liquidity_validation()
        print()
        
        test_data_freshness()
        print()
        
        test_rate_limiting_logic()
        print()
        
        print("🎉 All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
