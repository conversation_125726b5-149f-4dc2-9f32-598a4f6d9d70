import pytest
from unittest.mock import Mock, patch
from src.dune_client import DuneClient
from src.mongo_client import DuneMongoClient
from src.telegram_client import TelegramClient

@pytest.fixture
def mock_dune_response():
    return {
        "rows": [
            {
                "trader_id": "GbiKcGuQeYJuAhmmVzaM9D4ivLRM8q7o7a2gvRevmBVW",
                "token_bought_symbol": "YzY",
                "token_bought_mint_address": "4NBTf8PfLH4oLFnwf3knv46FY9i5oXjDxffCetXRpump",
                "total_token_bought": "5287086.953291",
                "total_usd_spent": "43471.95293274372",
                "first_trade": "2025-03-21 16:09",
                "last_trade": "2025-03-21 16:09",
                "unique_traders_per_token": "1"
            }
        ]
    }

@pytest.fixture
def dune_client():
    with patch('src.dune_client.requests') as mock_requests:
        client = DuneClient()
        yield client

@pytest.fixture
def mongo_client():
    with patch('src.mongo_client.MongoClient') as mock_mongo:
        client = DuneMongoClient()
        yield client

@pytest.fixture
def telegram_client():
    with patch('src.telegram_client.Bot') as mock_bot:
        client = TelegramClient()
        yield client 