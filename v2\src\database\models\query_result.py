"""
🔍 Query Result Database Model

Beanie ODM model for storing Dune Analytics query results following DATABASE_PATTERNS.md
with proper indexing, validation, and caching.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from beanie import Document, Indexed
from pydantic import Field, validator
from pymongo import IndexModel, ASCENDING, DESCENDING

from .base import BaseDocument


class QueryResult(BaseDocument):
    """
    🔍 Query Result Model
    
    Stores Dune Analytics query results with metadata and caching
    following V2 architecture patterns.
    """
    
    # 🎯 Core Query Data
    query_id: Indexed(str) = Field(..., description="Dune query ID")
    execution_id: Indexed(str) = Field(..., description="Dune execution ID")
    query_name: str = Field(..., description="Query name/description")
    
    # 📊 Result Data
    result_data: List[Dict[str, Any]] = Field(..., description="Query result rows")
    row_count: int = Field(..., description="Number of result rows")
    column_names: List[str] = Field(..., description="Column names")
    
    # ⏰ Execution Metadata
    execution_started_at: datetime = Field(..., description="Execution start time")
    execution_finished_at: datetime = Field(..., description="Execution finish time")
    execution_duration_seconds: float = Field(..., description="Execution duration")
    
    # 📈 Query Performance
    query_runtime_ms: Optional[int] = Field(None, description="Query runtime in milliseconds")
    result_size_bytes: Optional[int] = Field(None, description="Result size in bytes")
    credits_used: Optional[float] = Field(None, description="Dune credits consumed")
    
    # 🔍 Query Parameters
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Query parameters")
    filters_applied: Dict[str, Any] = Field(default_factory=dict, description="Applied filters")
    
    # 📊 Data Quality
    data_freshness: Optional[datetime] = Field(None, description="Data freshness timestamp")
    data_source: str = Field(default="dune", description="Data source identifier")
    data_version: Optional[str] = Field(None, description="Data version/hash")
    
    # 🔄 Caching
    cache_key: Optional[str] = Field(None, description="Cache key for result")
    cache_ttl: Optional[int] = Field(None, description="Cache TTL in seconds")
    is_cached: bool = Field(default=False, description="Result from cache flag")
    
    # 📋 Status and Metadata
    status: str = Field(default="completed", description="Query execution status")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    tags: List[str] = Field(default_factory=list, description="Query tags")
    
    # 🔗 Related Data
    related_tokens: List[str] = Field(default_factory=list, description="Related token addresses")
    related_queries: List[str] = Field(default_factory=list, description="Related query IDs")
    
    @validator("row_count")
    def validate_row_count(cls, v, values):
        """Validate row count matches result data"""
        if "result_data" in values and len(values["result_data"]) != v:
            raise ValueError("Row count must match result data length")
        return v
    
    @validator("execution_duration_seconds")
    def validate_execution_duration(cls, v, values):
        """Validate execution duration"""
        if v < 0:
            raise ValueError("Execution duration must be positive")
        
        # Validate against start/finish times if available
        if "execution_started_at" in values and "execution_finished_at" in values:
            calculated_duration = (values["execution_finished_at"] - values["execution_started_at"]).total_seconds()
            if abs(calculated_duration - v) > 1:  # Allow 1 second tolerance
                raise ValueError("Execution duration doesn't match start/finish times")
        
        return v
    
    @property
    def is_fresh(self) -> bool:
        """Check if data is fresh (less than 1 hour old)"""
        if not self.data_freshness:
            return False
        return (datetime.utcnow() - self.data_freshness).total_seconds() < 3600
    
    @property
    def result_summary(self) -> Dict[str, Any]:
        """Get result summary statistics"""
        if not self.result_data:
            return {"empty": True}
        
        return {
            "row_count": self.row_count,
            "column_count": len(self.column_names),
            "execution_time": self.execution_duration_seconds,
            "data_size_mb": (self.result_size_bytes / 1024 / 1024) if self.result_size_bytes else None,
            "is_fresh": self.is_fresh,
            "has_errors": bool(self.error_message)
        }
    
    def get_column_data(self, column_name: str) -> List[Any]:
        """
        Extract data for a specific column
        
        Args:
            column_name: Name of the column to extract
            
        Returns:
            List of values for the specified column
        """
        if column_name not in self.column_names:
            raise ValueError(f"Column '{column_name}' not found in result")
        
        return [row.get(column_name) for row in self.result_data]
    
    def filter_results(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Filter result data based on criteria
        
        Args:
            filters: Dictionary of column_name: value filters
            
        Returns:
            Filtered result data
        """
        filtered_data = []
        
        for row in self.result_data:
            match = True
            for column, value in filters.items():
                if column not in row or row[column] != value:
                    match = False
                    break
            
            if match:
                filtered_data.append(row)
        
        return filtered_data
    
    def to_dataframe(self):
        """Convert result to pandas DataFrame (if pandas is available)"""
        try:
            import pandas as pd
            return pd.DataFrame(self.result_data)
        except ImportError:
            raise ImportError("pandas is required to convert to DataFrame")
    
    class Settings:
        name = "query_results"
        indexes = [
            # Core query indexes
            IndexModel([("query_id", ASCENDING), ("created_at", DESCENDING)]),
            IndexModel([("execution_id", ASCENDING)], unique=True),
            IndexModel([("query_name", ASCENDING)]),
            
            # Performance indexes
            IndexModel([("execution_duration_seconds", ASCENDING)]),
            IndexModel([("row_count", DESCENDING)]),
            IndexModel([("result_size_bytes", DESCENDING)]),
            
            # Caching indexes
            IndexModel([("cache_key", ASCENDING)]),
            IndexModel([("is_cached", ASCENDING), ("created_at", DESCENDING)]),
            IndexModel([("data_freshness", DESCENDING)]),
            
            # Status indexes
            IndexModel([("status", ASCENDING), ("created_at", DESCENDING)]),
            IndexModel([("tags", ASCENDING)]),
            
            # Related data indexes
            IndexModel([("related_tokens", ASCENDING)]),
            IndexModel([("related_queries", ASCENDING)]),
            
            # Compound indexes
            IndexModel([
                ("query_id", ASCENDING),
                ("status", ASCENDING),
                ("created_at", DESCENDING)
            ]),
            IndexModel([
                ("data_freshness", DESCENDING),
                ("is_cached", ASCENDING),
                ("row_count", DESCENDING)
            ])
        ]
