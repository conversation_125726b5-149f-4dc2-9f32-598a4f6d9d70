{% extends "base.html" %}

{% block title %}Dashboard - Token Tracker{% endblock %}

{% block content %}
<h1 class="mb-4">Token Trade Dashboard</h1>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">Trades Over Time (Last 1000)</div>
            <div class="card-body">
                <canvas id="tradesOverTimeChart"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">Top 10 Tokens by USD Spent (Last 1000)</div>
            <div class="card-body">
                <canvas id="topTokensByUsdChart"></canvas>
            </div>
        </div>
    </div>
</div>


<h2>Recent Trades (Latest Query Results)</h2>
{% if trades %}
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Token Symbol</th>
                <th>USD Spent</th>
                <th>Tokens Bought</th>
                <th>Unique Traders</th>
                <th>Trader ID</th>
                <th>First Trade</th>
                <th>Last Trade</th>
                <th>Token Address</th>
            </tr>
        </thead>
        <tbody>
            {% for trade in trades %}
            <tr>
                <td>{{ trade.token_bought_symbol | default('N/A') }}</td>
                <td>${{ "%.2f"|format(trade.total_usd_spent | default(0)) }}</td>
                <td>{{ "%.4f"|format(trade.total_token_bought | default(0)) }}</td>
                <td>{{ trade.unique_traders_per_token | default('N/A') }}</td>
                <td><code>{{ trade.trader_id | default('N/A') }}</code></td>
                <td>{{ trade.first_trade | default('N/A') }}</td>
                <td>{{ trade.last_trade | default('N/A') }}</td>
                <td><code>{{ trade.token_bought_mint_address | default('N/A') }}</code></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="alert alert-warning" role="alert">
    No recent trade data found.
</div>
{% endif %}

{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='chart.js') }}"></script>
{% endblock %} 