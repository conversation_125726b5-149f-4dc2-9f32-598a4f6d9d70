"""
🧪 Paper Trading Tests

Comprehensive test suite for paper trading module including portfolio management,
trade execution, performance tracking, and backtesting.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, AsyncMock, patch
import numpy as np

# Import the modules we want to test
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.features.paper_trading import (
    PortfolioManager,
    TradeExecutor,
    PerformanceTracker,
    BacktestEngine
)
from src.shared.types import SignalType, OrderType, TradeStatus, PortfolioStatus, MarketData, SignalData
from src.database.models import Portfolio, Trade


class TestPortfolioManager:
    """Test suite for PortfolioManager"""
    
    @pytest.fixture
    def portfolio_manager(self):
        return PortfolioManager()
    
    @pytest.mark.asyncio
    async def test_create_portfolio_success(self, portfolio_manager):
        """Test successful portfolio creation"""
        with patch('src.database.models.Portfolio.save') as mock_save:
            mock_save.return_value = None
            
            portfolio = await portfolio_manager.create_portfolio(
                name="Test Portfolio",
                initial_balance=Decimal("10000"),
                description="Test portfolio for unit tests"
            )
            
            assert portfolio is not None
            assert portfolio.name == "Test Portfolio"
            assert portfolio.initial_balance == Decimal("10000")
            assert portfolio.current_balance == Decimal("10000")
            assert portfolio.status == PortfolioStatus.ACTIVE
            assert portfolio.max_position_size > 0
            mock_save.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_portfolio_balance_add(self, portfolio_manager):
        """Test adding to portfolio balance"""
        mock_portfolio = Mock()
        mock_portfolio.current_balance = Decimal("10000")
        mock_portfolio.save = AsyncMock()
        
        with patch.object(portfolio_manager, 'get_portfolio', return_value=mock_portfolio):
            success = await portfolio_manager.update_portfolio_balance(
                "test_portfolio_id",
                Decimal("1000"),
                "add"
            )
            
            assert success
            assert mock_portfolio.current_balance == Decimal("11000")
            mock_portfolio.save.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_portfolio_balance_subtract_sufficient(self, portfolio_manager):
        """Test subtracting from portfolio balance with sufficient funds"""
        mock_portfolio = Mock()
        mock_portfolio.current_balance = Decimal("10000")
        mock_portfolio.save = AsyncMock()
        
        with patch.object(portfolio_manager, 'get_portfolio', return_value=mock_portfolio):
            success = await portfolio_manager.update_portfolio_balance(
                "test_portfolio_id",
                Decimal("5000"),
                "subtract"
            )
            
            assert success
            assert mock_portfolio.current_balance == Decimal("5000")
    
    @pytest.mark.asyncio
    async def test_update_portfolio_balance_subtract_insufficient(self, portfolio_manager):
        """Test subtracting from portfolio balance with insufficient funds"""
        mock_portfolio = Mock()
        mock_portfolio.current_balance = Decimal("1000")
        mock_portfolio.save = AsyncMock()
        
        with patch.object(portfolio_manager, 'get_portfolio', return_value=mock_portfolio):
            success = await portfolio_manager.update_portfolio_balance(
                "test_portfolio_id",
                Decimal("5000"),
                "subtract"
            )
            
            assert not success
            assert mock_portfolio.current_balance == Decimal("1000")  # Unchanged
    
    @pytest.mark.asyncio
    async def test_get_portfolio_positions(self, portfolio_manager):
        """Test getting portfolio positions"""
        # Mock trades
        mock_trades = [
            Mock(
                token_address="token1",
                side=SignalType.BUY,
                quantity=Decimal("100"),
                price=Decimal("10"),
                value_usd=Decimal("1000"),
                execution_time=datetime.utcnow()
            ),
            Mock(
                token_address="token1",
                side=SignalType.SELL,
                quantity=Decimal("50"),
                price=Decimal("12"),
                value_usd=Decimal("600"),
                execution_time=datetime.utcnow()
            )
        ]
        
        mock_market_data = MarketData(
            token_address="token1",
            price=Decimal("11"),
            volume_24h=Decimal("1000000"),
            timestamp=datetime.utcnow()
        )
        
        with patch('src.database.models.Trade.find') as mock_find, \
             patch.object(portfolio_manager.data_aggregator, 'get_token_data', return_value=mock_market_data):
            
            mock_find.return_value.sort.return_value.to_list.return_value = mock_trades
            
            positions = await portfolio_manager.get_portfolio_positions("test_portfolio_id")
            
            assert len(positions) == 1  # One token position
            position = positions[0]
            assert position["token_address"] == "token1"
            assert position["quantity"] == 50.0  # 100 bought - 50 sold
            assert position["current_price"] == 11.0
            assert position["unrealized_pnl"] > 0  # Should be profitable
    
    @pytest.mark.asyncio
    async def test_calculate_portfolio_value(self, portfolio_manager):
        """Test portfolio value calculation"""
        mock_portfolio = Mock()
        mock_portfolio.current_balance = Decimal("5000")
        mock_portfolio.initial_balance = Decimal("10000")
        
        mock_positions = [
            {
                "current_value": 3000,
                "unrealized_pnl": 500,
                "realized_pnl": 200
            },
            {
                "current_value": 2000,
                "unrealized_pnl": -100,
                "realized_pnl": 50
            }
        ]
        
        with patch.object(portfolio_manager, 'get_portfolio', return_value=mock_portfolio), \
             patch.object(portfolio_manager, 'get_portfolio_positions', return_value=mock_positions):
            
            value_data = await portfolio_manager.calculate_portfolio_value("test_portfolio_id")
            
            assert value_data["cash_balance"] == 5000.0
            assert value_data["position_value"] == 5000.0  # 3000 + 2000
            assert value_data["total_value"] == 10000.0  # 5000 cash + 5000 positions
            assert value_data["unrealized_pnl"] == 400.0  # 500 - 100
            assert value_data["realized_pnl"] == 250.0  # 200 + 50
    
    @pytest.mark.asyncio
    async def test_check_risk_limits_valid(self, portfolio_manager):
        """Test risk limits check with valid trade"""
        mock_portfolio = Mock()
        mock_portfolio.max_position_size = Decimal("1000")
        mock_portfolio.max_portfolio_risk = Decimal("0.1")  # 10%
        mock_portfolio.current_balance = Decimal("5000")
        
        mock_value_data = {"total_value": 10000}
        
        with patch.object(portfolio_manager, 'get_portfolio', return_value=mock_portfolio), \
             patch.object(portfolio_manager, 'calculate_portfolio_value', return_value=mock_value_data):
            
            result = await portfolio_manager.check_risk_limits("test_portfolio_id", Decimal("500"))
            
            assert result["valid"]
    
    @pytest.mark.asyncio
    async def test_check_risk_limits_position_size_exceeded(self, portfolio_manager):
        """Test risk limits check with position size exceeded"""
        mock_portfolio = Mock()
        mock_portfolio.max_position_size = Decimal("1000")
        mock_portfolio.current_balance = Decimal("5000")
        
        with patch.object(portfolio_manager, 'get_portfolio', return_value=mock_portfolio):
            result = await portfolio_manager.check_risk_limits("test_portfolio_id", Decimal("1500"))
            
            assert not result["valid"]
            assert "position size" in result["reason"].lower()


class TestTradeExecutor:
    """Test suite for TradeExecutor"""
    
    @pytest.fixture
    def trade_executor(self):
        return TradeExecutor()
    
    @pytest.fixture
    def mock_market_data(self):
        return MarketData(
            token_address="test_token",
            price=Decimal("100"),
            volume_24h=Decimal("1000000"),
            liquidity=Decimal("500000"),
            timestamp=datetime.utcnow()
        )
    
    @pytest.mark.asyncio
    async def test_execute_market_order_buy(self, trade_executor, mock_market_data):
        """Test executing a buy market order"""
        with patch.object(trade_executor.data_aggregator, 'get_token_data', return_value=mock_market_data), \
             patch.object(trade_executor.portfolio_manager, 'update_portfolio_balance', return_value=True), \
             patch('src.database.models.Trade.save') as mock_save:
            
            mock_save.return_value = None
            
            trade = await trade_executor.execute_market_order(
                portfolio_id="test_portfolio",
                token_address="test_token",
                side=SignalType.BUY,
                quantity=Decimal("10")
            )
            
            assert trade is not None
            assert trade.side == SignalType.BUY
            assert trade.quantity == Decimal("10")
            assert trade.price >= mock_market_data.price  # Should include slippage
            assert trade.status == TradeStatus.EXECUTED
            assert trade.fees > 0
            mock_save.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_market_order_sell(self, trade_executor, mock_market_data):
        """Test executing a sell market order"""
        with patch.object(trade_executor.data_aggregator, 'get_token_data', return_value=mock_market_data), \
             patch.object(trade_executor.portfolio_manager, 'update_portfolio_balance', return_value=True), \
             patch('src.database.models.Trade.save') as mock_save:
            
            mock_save.return_value = None
            
            trade = await trade_executor.execute_market_order(
                portfolio_id="test_portfolio",
                token_address="test_token",
                side=SignalType.SELL,
                quantity=Decimal("10")
            )
            
            assert trade is not None
            assert trade.side == SignalType.SELL
            assert trade.price <= mock_market_data.price  # Should include slippage
            assert trade.status == TradeStatus.EXECUTED
    
    @pytest.mark.asyncio
    async def test_execute_limit_order_immediate_fill(self, trade_executor, mock_market_data):
        """Test limit order that can be filled immediately"""
        with patch.object(trade_executor.data_aggregator, 'get_token_data', return_value=mock_market_data), \
             patch.object(trade_executor, 'execute_market_order') as mock_market_order:
            
            mock_trade = Mock()
            mock_market_order.return_value = mock_trade
            
            # Buy limit order above market price (should fill immediately)
            trade = await trade_executor.execute_limit_order(
                portfolio_id="test_portfolio",
                token_address="test_token",
                side=SignalType.BUY,
                quantity=Decimal("10"),
                limit_price=Decimal("105")  # Above market price
            )
            
            assert trade == mock_trade
            mock_market_order.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_limit_order_pending(self, trade_executor, mock_market_data):
        """Test limit order that remains pending"""
        with patch.object(trade_executor.data_aggregator, 'get_token_data', return_value=mock_market_data), \
             patch('src.database.models.Trade.save') as mock_save:
            
            mock_save.return_value = None
            
            # Buy limit order below market price (should remain pending)
            trade = await trade_executor.execute_limit_order(
                portfolio_id="test_portfolio",
                token_address="test_token",
                side=SignalType.BUY,
                quantity=Decimal("10"),
                limit_price=Decimal("95")  # Below market price
            )
            
            assert trade is not None
            assert trade.status == TradeStatus.PENDING
            assert trade.order_type == OrderType.LIMIT
    
    def test_calculate_execution_price_buy(self, trade_executor):
        """Test execution price calculation for buy order"""
        market_price = Decimal("100")
        liquidity = Decimal("500000")
        
        execution_price = trade_executor._calculate_execution_price(
            market_price, SignalType.BUY, Decimal("10"), liquidity
        )
        
        assert execution_price >= market_price  # Buy should get worse (higher) price
    
    def test_calculate_execution_price_sell(self, trade_executor):
        """Test execution price calculation for sell order"""
        market_price = Decimal("100")
        liquidity = Decimal("500000")
        
        execution_price = trade_executor._calculate_execution_price(
            market_price, SignalType.SELL, Decimal("10"), liquidity
        )
        
        assert execution_price <= market_price  # Sell should get worse (lower) price
    
    def test_calculate_fees(self, trade_executor):
        """Test fee calculation"""
        trade_value = Decimal("1000")
        liquidity = Decimal("500000")
        
        fees = trade_executor._calculate_fees(trade_value, liquidity)
        
        assert fees > 0
        assert fees < trade_value * Decimal("0.01")  # Should be reasonable percentage
    
    def test_calculate_market_impact(self, trade_executor):
        """Test market impact calculation"""
        quantity = Decimal("100")
        liquidity = Decimal("500000")
        
        impact = trade_executor._calculate_market_impact(quantity, liquidity)
        
        assert impact >= 0
        assert impact <= Decimal("0.05")  # Should be capped at 5%


class TestPerformanceTracker:
    """Test suite for PerformanceTracker"""
    
    @pytest.fixture
    def performance_tracker(self):
        return PerformanceTracker()
    
    @pytest.fixture
    def mock_portfolio(self):
        portfolio = Mock()
        portfolio.id = "test_portfolio"
        portfolio.initial_balance = Decimal("10000")
        portfolio.started_at = datetime.utcnow() - timedelta(days=30)
        portfolio.daily_balances = [
            {"date": "2024-01-01", "total_value": 10000},
            {"date": "2024-01-02", "total_value": 10200},
            {"date": "2024-01-03", "total_value": 10100},
            {"date": "2024-01-04", "total_value": 10300},
            {"date": "2024-01-05", "total_value": 10500}
        ]
        return portfolio
    
    @pytest.mark.asyncio
    async def test_calculate_performance_metrics(self, performance_tracker, mock_portfolio):
        """Test performance metrics calculation"""
        mock_value_data = {
            "total_value": 11000,
            "realized_pnl": 500,
            "unrealized_pnl": 500
        }
        
        mock_trades = [
            Mock(pnl=Decimal("100"), status="EXECUTED"),
            Mock(pnl=Decimal("200"), status="EXECUTED"),
            Mock(pnl=Decimal("-50"), status="EXECUTED"),
            Mock(pnl=Decimal("150"), status="EXECUTED")
        ]
        
        with patch.object(performance_tracker.portfolio_manager, 'get_portfolio', return_value=mock_portfolio), \
             patch.object(performance_tracker.portfolio_manager, 'calculate_portfolio_value', return_value=mock_value_data), \
             patch.object(performance_tracker, '_get_daily_returns', return_value=[0.02, -0.01, 0.02, 0.019]), \
             patch('src.database.models.Trade.find') as mock_find, \
             patch('src.database.models.PerformanceMetric.save') as mock_save:
            
            mock_find.return_value.to_list.return_value = mock_trades
            mock_save.return_value = None
            
            snapshot = await performance_tracker.calculate_performance_metrics("test_portfolio")
            
            assert snapshot.total_return == Decimal("1000")  # 11000 - 10000
            assert snapshot.total_return_percent == Decimal("10")  # 10%
            assert snapshot.win_rate == Decimal("75")  # 3 out of 4 trades profitable
            assert snapshot.volatility is not None
            assert snapshot.sharpe_ratio is not None
    
    def test_calculate_annualized_return(self, performance_tracker):
        """Test annualized return calculation"""
        total_return_percent = Decimal("20")  # 20% return
        days = 365  # 1 year
        
        annualized = performance_tracker._calculate_annualized_return(total_return_percent, days)
        
        assert annualized is not None
        assert abs(float(annualized) - 20.0) < 0.1  # Should be close to 20% for 1 year
    
    def test_calculate_volatility(self, performance_tracker):
        """Test volatility calculation"""
        returns = [0.01, -0.02, 0.015, -0.005, 0.02, -0.01, 0.008]
        
        volatility = performance_tracker._calculate_volatility(returns)
        
        assert volatility is not None
        assert volatility > 0
    
    def test_calculate_sharpe_ratio(self, performance_tracker):
        """Test Sharpe ratio calculation"""
        annualized_return = Decimal("15")  # 15% annual return
        volatility = Decimal("0.2")  # 20% volatility
        
        sharpe = performance_tracker._calculate_sharpe_ratio(annualized_return, volatility)
        
        assert sharpe is not None
        assert sharpe > 0  # Should be positive for good return vs risk-free rate
    
    @pytest.mark.asyncio
    async def test_calculate_win_rate(self, performance_tracker):
        """Test win rate calculation"""
        mock_trades = [
            Mock(pnl=Decimal("100")),  # Win
            Mock(pnl=Decimal("200")),  # Win
            Mock(pnl=Decimal("-50")),  # Loss
            Mock(pnl=Decimal("150")),  # Win
            Mock(pnl=Decimal("-25"))   # Loss
        ]
        
        with patch('src.database.models.Trade.find') as mock_find:
            mock_find.return_value.to_list.return_value = mock_trades
            
            win_rate = await performance_tracker._calculate_win_rate("test_portfolio")
            
            assert win_rate == Decimal("60")  # 3 wins out of 5 trades = 60%
    
    @pytest.mark.asyncio
    async def test_calculate_profit_factor(self, performance_tracker):
        """Test profit factor calculation"""
        mock_trades = [
            Mock(pnl=Decimal("100")),  # Profit
            Mock(pnl=Decimal("200")),  # Profit
            Mock(pnl=Decimal("-50")),  # Loss
            Mock(pnl=Decimal("-25"))   # Loss
        ]
        
        with patch('src.database.models.Trade.find') as mock_find:
            mock_find.return_value.to_list.return_value = mock_trades
            
            profit_factor = await performance_tracker._calculate_profit_factor("test_portfolio")
            
            assert profit_factor is not None
            assert profit_factor == Decimal("4")  # 300 profit / 75 loss = 4


class TestBacktestEngine:
    """Test suite for BacktestEngine"""
    
    @pytest.fixture
    def backtest_engine(self):
        return BacktestEngine()
    
    @pytest.fixture
    def sample_config(self):
        from src.features.paper_trading.backtest_engine import BacktestConfig
        return BacktestConfig(
            start_date=datetime.utcnow() - timedelta(days=30),
            end_date=datetime.utcnow() - timedelta(days=1),
            initial_balance=Decimal("10000"),
            tokens=["token1", "token2"],
            strategy_params={"rsi_period": 14, "macd_fast": 12}
        )
    
    def test_generate_parameter_combinations(self, backtest_engine):
        """Test parameter combination generation"""
        parameter_ranges = {
            "rsi_period": [14, 21],
            "macd_fast": [12, 26],
            "threshold": [0.7, 0.8]
        }
        
        combinations = backtest_engine._generate_parameter_combinations(parameter_ranges)
        
        assert len(combinations) == 8  # 2 * 2 * 2 = 8 combinations
        assert {"rsi_period": 14, "macd_fast": 12, "threshold": 0.7} in combinations
        assert {"rsi_period": 21, "macd_fast": 26, "threshold": 0.8} in combinations
    
    def test_get_optimization_score_sharpe_ratio(self, backtest_engine):
        """Test optimization score calculation for Sharpe ratio"""
        from src.features.paper_trading.backtest_engine import BacktestResult
        
        mock_result = Mock()
        mock_result.sharpe_ratio = Decimal("1.5")
        mock_result.total_return_percent = Decimal("20")
        mock_result.win_rate = Decimal("70")
        mock_result.trade_history = [
            {"pnl": 100}, {"pnl": -50}, {"pnl": 200}
        ]
        
        score = backtest_engine._get_optimization_score(mock_result, "sharpe_ratio")
        assert score == 1.5
        
        score = backtest_engine._get_optimization_score(mock_result, "total_return")
        assert score == 20.0
        
        score = backtest_engine._get_optimization_score(mock_result, "win_rate")
        assert score == 70.0
    
    def test_generate_backtest_report(self, backtest_engine, sample_config):
        """Test backtest report generation"""
        from src.features.paper_trading.backtest_engine import BacktestResult
        
        mock_result = BacktestResult(
            config=sample_config,
            total_return=Decimal("2000"),
            total_return_percent=Decimal("20"),
            sharpe_ratio=Decimal("1.5"),
            max_drawdown=Decimal("0.05"),
            win_rate=Decimal("70"),
            total_trades=50,
            profitable_trades=35,
            final_balance=Decimal("12000"),
            trade_history=[],
            daily_returns=[],
            performance_metrics={}
        )
        
        report = backtest_engine.generate_backtest_report(mock_result)
        
        assert "summary" in report
        assert "trading_stats" in report
        assert "config" in report
        assert report["summary"]["total_return"] == 2000.0
        assert report["summary"]["total_return_percent"] == 20.0
        assert report["trading_stats"]["total_trades"] == 50
        assert report["trading_stats"]["win_rate"] == 70.0


# Integration Tests
class TestPaperTradingIntegration:
    """Integration tests for paper trading workflow"""
    
    @pytest.mark.asyncio
    async def test_full_trading_workflow(self):
        """Test complete trading workflow from portfolio creation to performance tracking"""
        # This would test the full workflow:
        # 1. Create portfolio
        # 2. Execute trades
        # 3. Calculate performance
        # 4. Generate reports
        pass
    
    @pytest.mark.asyncio
    async def test_signal_to_trade_execution(self):
        """Test executing trades based on signals"""
        # This would test the integration between signal processing and trade execution
        pass


if __name__ == "__main__":
    pytest.main([__file__])
