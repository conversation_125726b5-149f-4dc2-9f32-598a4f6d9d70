"""
🎯 Features Module

Feature-based architecture following PROJECT_STRUCTURE.md guidelines
with modular, testable, and scalable components.
"""

from .data_pipeline import *
from .signal_processing import *
from .paper_trading import *
from .automation import *
from .monitoring import *
from .notifications import *

__all__ = [
    # Data Pipeline
    "DuneClient",
    "JupiterClient", 
    "RaydiumClient",
    "SolanaClient",
    "DataAggregator",
    
    # Signal Processing
    "SignalGenerator",
    "TechnicalAnalyzer",
    "RiskAssessor",
    "SignalValidator",
    
    # Paper Trading
    "PaperTradingEngine",
    "PortfolioManager",
    "PerformanceTracker",
    "BacktestEngine",
    
    # Automation
    "TradingBot",
    "OrderManager",
    "RiskManager",
    "ExecutionEngine",
    
    # Monitoring
    "HealthMonitor",
    "MetricsCollector",
    "AlertManager",
    "SystemMonitor",
    
    # Notifications
    "TelegramNotifier",
    "EmailNotifier",
    "WebhookNotifier",
    "NotificationManager"
]
