"""
🛣️ Paper Trading Routes

FastAPI routes for paper trading endpoints including portfolio management,
trade execution, performance tracking, and backtesting.
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, Query, Path
from pydantic import BaseModel, Field

from ...config.logging_config import get_logger
from ...shared.types import APIResponse, SignalType, OrderType, PortfolioStatus
from ...database.models import Portfolio, Trade

from .portfolio_manager import PortfolioManager
from .trade_executor import TradeExecutor
from .performance_tracker import PerformanceTracker
from .backtest_engine import BacktestEngine, BacktestConfig

logger = get_logger(__name__)
router = APIRouter()

# Initialize services
portfolio_manager = PortfolioManager()
trade_executor = TradeExecutor()
performance_tracker = PerformanceTracker()
backtest_engine = BacktestEngine()


# Request/Response Models
class CreatePortfolioRequest(BaseModel):
    """Request model for portfolio creation"""
    name: str = Field(..., description="Portfolio name")
    initial_balance: float = Field(..., gt=0, description="Initial balance in USD")
    description: Optional[str] = Field(None, description="Portfolio description")
    max_position_size: Optional[float] = Field(None, description="Maximum position size")
    stop_loss_percent: Optional[float] = Field(0.05, description="Default stop loss percentage")
    take_profit_percent: Optional[float] = Field(0.15, description="Default take profit percentage")


class ExecuteTradeRequest(BaseModel):
    """Request model for trade execution"""
    token_address: str = Field(..., description="Token contract address")
    side: SignalType = Field(..., description="Trade side (BUY/SELL)")
    quantity: float = Field(..., gt=0, description="Trade quantity")
    order_type: OrderType = Field(default=OrderType.MARKET, description="Order type")
    limit_price: Optional[float] = Field(None, description="Limit price for limit orders")
    stop_loss: Optional[float] = Field(None, description="Stop loss price")
    take_profit: Optional[float] = Field(None, description="Take profit price")


class BacktestRequest(BaseModel):
    """Request model for backtesting"""
    start_date: datetime = Field(..., description="Backtest start date")
    end_date: datetime = Field(..., description="Backtest end date")
    initial_balance: float = Field(..., gt=0, description="Initial balance")
    tokens: List[str] = Field(..., description="List of token addresses")
    strategy_params: Dict[str, Any] = Field(default_factory=dict, description="Strategy parameters")


class PortfolioResponse(BaseModel):
    """Response model for portfolio data"""
    id: str
    name: str
    description: Optional[str]
    initial_balance: float
    current_balance: float
    total_value: float
    total_pnl: float
    total_return_percent: float
    status: PortfolioStatus
    position_count: int
    total_trades: int
    win_rate: float
    created_at: datetime
    last_updated_at: datetime


@router.post("/portfolios", response_model=APIResponse)
async def create_portfolio(request: CreatePortfolioRequest):
    """Create a new paper trading portfolio"""
    try:
        logger.info(f"Creating portfolio: {request.name}")
        
        # Prepare configuration
        config = {}
        if request.max_position_size:
            config["max_position_size"] = request.max_position_size
        if request.stop_loss_percent:
            config["stop_loss_percent"] = request.stop_loss_percent
        if request.take_profit_percent:
            config["take_profit_percent"] = request.take_profit_percent
        
        # Create portfolio
        portfolio = await portfolio_manager.create_portfolio(
            name=request.name,
            initial_balance=Decimal(str(request.initial_balance)),
            description=request.description,
            config=config
        )
        
        # Calculate initial value
        value_data = await portfolio_manager.calculate_portfolio_value(str(portfolio.id))
        
        response_data = PortfolioResponse(
            id=str(portfolio.id),
            name=portfolio.name,
            description=portfolio.description,
            initial_balance=float(portfolio.initial_balance),
            current_balance=float(portfolio.current_balance),
            total_value=value_data.get("total_value", float(portfolio.current_balance)),
            total_pnl=float(portfolio.total_pnl),
            total_return_percent=float(portfolio.total_return_percent),
            status=portfolio.status,
            position_count=value_data.get("position_count", 0),
            total_trades=portfolio.total_trades,
            win_rate=float(portfolio.win_rate),
            created_at=portfolio.created_at,
            last_updated_at=portfolio.last_updated_at
        )
        
        return APIResponse(
            success=True,
            message="Portfolio created successfully",
            data=response_data.dict()
        )
        
    except Exception as e:
        logger.error(f"Error creating portfolio: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/portfolios", response_model=APIResponse)
async def get_portfolios(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of portfolios to return")
):
    """Get portfolios"""
    try:
        if user_id:
            portfolios = await portfolio_manager.get_user_portfolios(user_id)
        else:
            # Get all portfolios (limited)
            portfolios = await Portfolio.find().sort("-created_at").limit(limit).to_list()
        
        response_portfolios = []
        for portfolio in portfolios:
            value_data = await portfolio_manager.calculate_portfolio_value(str(portfolio.id))
            
            response_portfolios.append(PortfolioResponse(
                id=str(portfolio.id),
                name=portfolio.name,
                description=portfolio.description,
                initial_balance=float(portfolio.initial_balance),
                current_balance=float(portfolio.current_balance),
                total_value=value_data.get("total_value", float(portfolio.current_balance)),
                total_pnl=float(portfolio.total_pnl),
                total_return_percent=float(portfolio.total_return_percent),
                status=portfolio.status,
                position_count=value_data.get("position_count", 0),
                total_trades=portfolio.total_trades,
                win_rate=float(portfolio.win_rate),
                created_at=portfolio.created_at,
                last_updated_at=portfolio.last_updated_at
            ))
        
        return APIResponse(
            success=True,
            message=f"Retrieved {len(response_portfolios)} portfolios",
            data=response_portfolios
        )
        
    except Exception as e:
        logger.error(f"Error retrieving portfolios: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/portfolios/{portfolio_id}", response_model=APIResponse)
async def get_portfolio(
    portfolio_id: str = Path(..., description="Portfolio ID")
):
    """Get specific portfolio details"""
    try:
        portfolio = await portfolio_manager.get_portfolio(portfolio_id)
        if not portfolio:
            raise HTTPException(status_code=404, detail="Portfolio not found")
        
        # Get detailed value data
        value_data = await portfolio_manager.calculate_portfolio_value(portfolio_id)
        
        response_data = {
            "portfolio": PortfolioResponse(
                id=str(portfolio.id),
                name=portfolio.name,
                description=portfolio.description,
                initial_balance=float(portfolio.initial_balance),
                current_balance=float(portfolio.current_balance),
                total_value=value_data.get("total_value", float(portfolio.current_balance)),
                total_pnl=float(portfolio.total_pnl),
                total_return_percent=float(portfolio.total_return_percent),
                status=portfolio.status,
                position_count=value_data.get("position_count", 0),
                total_trades=portfolio.total_trades,
                win_rate=float(portfolio.win_rate),
                created_at=portfolio.created_at,
                last_updated_at=portfolio.last_updated_at
            ).dict(),
            "positions": value_data.get("positions", []),
            "value_breakdown": {
                "cash_balance": value_data.get("cash_balance", 0),
                "position_value": value_data.get("position_value", 0),
                "total_value": value_data.get("total_value", 0),
                "realized_pnl": value_data.get("realized_pnl", 0),
                "unrealized_pnl": value_data.get("unrealized_pnl", 0)
            }
        }
        
        return APIResponse(
            success=True,
            message="Portfolio retrieved successfully",
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving portfolio: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/portfolios/{portfolio_id}/trades", response_model=APIResponse)
async def execute_trade(
    portfolio_id: str = Path(..., description="Portfolio ID"),
    request: ExecuteTradeRequest = None
):
    """Execute a trade in the portfolio"""
    try:
        logger.info(f"Executing trade for portfolio {portfolio_id}")
        
        # Verify portfolio exists
        portfolio = await portfolio_manager.get_portfolio(portfolio_id)
        if not portfolio:
            raise HTTPException(status_code=404, detail="Portfolio not found")
        
        # Execute trade based on order type
        if request.order_type == OrderType.MARKET:
            trade = await trade_executor.execute_market_order(
                portfolio_id=portfolio_id,
                token_address=request.token_address,
                side=request.side,
                quantity=Decimal(str(request.quantity)),
                stop_loss=Decimal(str(request.stop_loss)) if request.stop_loss else None,
                take_profit=Decimal(str(request.take_profit)) if request.take_profit else None
            )
        elif request.order_type == OrderType.LIMIT:
            if not request.limit_price:
                raise HTTPException(status_code=400, detail="Limit price required for limit orders")
            
            trade = await trade_executor.execute_limit_order(
                portfolio_id=portfolio_id,
                token_address=request.token_address,
                side=request.side,
                quantity=Decimal(str(request.quantity)),
                limit_price=Decimal(str(request.limit_price))
            )
        else:
            raise HTTPException(status_code=400, detail="Unsupported order type")
        
        if not trade:
            raise HTTPException(status_code=400, detail="Trade execution failed")
        
        # Update portfolio metrics
        await portfolio_manager.update_portfolio_metrics(portfolio_id)
        
        trade_data = {
            "trade_id": str(trade.id),
            "token_address": trade.token_address,
            "side": trade.side,
            "order_type": trade.order_type,
            "quantity": float(trade.quantity),
            "price": float(trade.price),
            "value_usd": float(trade.value_usd),
            "fees": float(trade.fees),
            "status": trade.status,
            "execution_time": trade.execution_time,
            "created_at": trade.created_at
        }
        
        return APIResponse(
            success=True,
            message="Trade executed successfully",
            data=trade_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing trade: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/portfolios/{portfolio_id}/performance", response_model=APIResponse)
async def get_portfolio_performance(
    portfolio_id: str = Path(..., description="Portfolio ID")
):
    """Get portfolio performance metrics"""
    try:
        portfolio = await portfolio_manager.get_portfolio(portfolio_id)
        if not portfolio:
            raise HTTPException(status_code=404, detail="Portfolio not found")
        
        # Calculate performance metrics
        performance_snapshot = await performance_tracker.calculate_performance_metrics(portfolio_id)
        
        performance_data = {
            "total_return": float(performance_snapshot.total_return),
            "total_return_percent": float(performance_snapshot.total_return_percent),
            "annualized_return": float(performance_snapshot.annualized_return) if performance_snapshot.annualized_return else None,
            "sharpe_ratio": float(performance_snapshot.sharpe_ratio) if performance_snapshot.sharpe_ratio else None,
            "sortino_ratio": float(performance_snapshot.sortino_ratio) if performance_snapshot.sortino_ratio else None,
            "max_drawdown": float(performance_snapshot.max_drawdown),
            "current_drawdown": float(performance_snapshot.current_drawdown),
            "volatility": float(performance_snapshot.volatility) if performance_snapshot.volatility else None,
            "win_rate": float(performance_snapshot.win_rate),
            "profit_factor": float(performance_snapshot.profit_factor) if performance_snapshot.profit_factor else None,
            "var_95": float(performance_snapshot.var_95) if performance_snapshot.var_95 else None
        }
        
        return APIResponse(
            success=True,
            message="Performance metrics calculated",
            data=performance_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating performance: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/portfolios/{portfolio_id}/trades", response_model=APIResponse)
async def get_trade_history(
    portfolio_id: str = Path(..., description="Portfolio ID"),
    limit: int = Query(50, ge=1, le=200, description="Maximum number of trades to return"),
    token_address: Optional[str] = Query(None, description="Filter by token address")
):
    """Get trade history for a portfolio"""
    try:
        trades = await trade_executor.get_trade_history(portfolio_id, limit, token_address)
        
        trade_data = []
        for trade in trades:
            trade_data.append({
                "trade_id": str(trade.id),
                "token_address": trade.token_address,
                "side": trade.side,
                "order_type": trade.order_type,
                "quantity": float(trade.quantity),
                "price": float(trade.price),
                "value_usd": float(trade.value_usd),
                "fees": float(trade.fees),
                "pnl": float(trade.pnl),
                "status": trade.status,
                "execution_time": trade.execution_time,
                "created_at": trade.created_at
            })
        
        return APIResponse(
            success=True,
            message=f"Retrieved {len(trade_data)} trades",
            data=trade_data
        )
        
    except Exception as e:
        logger.error(f"Error retrieving trade history: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/backtest", response_model=APIResponse)
async def run_backtest(request: BacktestRequest):
    """Run a backtest with specified parameters"""
    try:
        logger.info(f"Running backtest from {request.start_date} to {request.end_date}")
        
        # Create backtest configuration
        config = BacktestConfig(
            start_date=request.start_date,
            end_date=request.end_date,
            initial_balance=Decimal(str(request.initial_balance)),
            tokens=request.tokens,
            strategy_params=request.strategy_params
        )
        
        # Run backtest
        result = await backtest_engine.run_backtest(config)
        
        # Generate report
        report = backtest_engine.generate_backtest_report(result)
        
        return APIResponse(
            success=True,
            message="Backtest completed successfully",
            data=report
        )
        
    except Exception as e:
        logger.error(f"Error running backtest: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/portfolios/{portfolio_id}/stop-loss-check", response_model=APIResponse)
async def check_stop_loss_take_profit(
    portfolio_id: str = Path(..., description="Portfolio ID")
):
    """Check and execute stop-loss and take-profit orders"""
    try:
        executed_trades = await trade_executor.check_stop_loss_take_profit(portfolio_id)
        
        trade_data = []
        for trade in executed_trades:
            trade_data.append({
                "trade_id": str(trade.id),
                "token_address": trade.token_address,
                "side": trade.side,
                "quantity": float(trade.quantity),
                "price": float(trade.price),
                "notes": trade.notes,
                "execution_time": trade.execution_time
            })
        
        return APIResponse(
            success=True,
            message=f"Executed {len(executed_trades)} stop-loss/take-profit orders",
            data=trade_data
        )
        
    except Exception as e:
        logger.error(f"Error checking stop-loss/take-profit: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
