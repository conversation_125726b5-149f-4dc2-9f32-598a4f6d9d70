"""
🔍 Risk Assessment Module

Comprehensive risk assessment for tokens including liquidity risk,
volatility analysis, market conditions, and overall risk scoring.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from ...config.logging_config import get_logger
from ...shared.constants import TRADING_CONSTANTS
from ...shared.types import MarketData

logger = get_logger(__name__)


@dataclass
class RiskMetrics:
    """Risk assessment metrics data structure"""
    liquidity_risk: float = 0.5
    volatility_risk: float = 0.5
    market_risk: float = 0.5
    concentration_risk: float = 0.5
    overall_risk_score: float = 0.5
    risk_factors: List[str] = None
    risk_level: str = "moderate"
    
    def __post_init__(self):
        if self.risk_factors is None:
            self.risk_factors = []


class RiskAssessor:
    """
    🔍 Risk Assessment Module
    
    Provides comprehensive risk assessment including:
    - Liquidity risk analysis
    - Volatility assessment
    - Market condition evaluation
    - Concentration risk
    - Overall risk scoring
    """
    
    def __init__(self):
        self.logger = logger
        
        # Risk assessment thresholds
        self.min_liquidity_usd = Decimal("50000")  # Minimum liquidity requirement
        self.high_volatility_threshold = 0.1  # 10% daily volatility
        self.low_liquidity_threshold = Decimal("100000")  # Low liquidity warning
        self.volume_liquidity_ratio_threshold = 0.1  # Volume should be <10% of liquidity
    
    async def assess_token_risk(
        self,
        token_address: str,
        current_market_data: MarketData,
        price_history: List[MarketData]
    ) -> Dict[str, Any]:
        """
        Perform comprehensive risk assessment for a token
        
        Args:
            token_address: Token contract address
            current_market_data: Current market data
            price_history: Historical price data
            
        Returns:
            Risk assessment results dictionary
        """
        try:
            self.logger.info(f"Assessing risk for token {token_address}")
            
            # Initialize risk metrics
            risk_metrics = RiskMetrics()
            
            # Assess liquidity risk
            risk_metrics.liquidity_risk = self._assess_liquidity_risk(current_market_data)
            
            # Assess volatility risk
            risk_metrics.volatility_risk = self._assess_volatility_risk(price_history)
            
            # Assess market risk
            risk_metrics.market_risk = self._assess_market_risk(current_market_data, price_history)
            
            # Assess concentration risk
            risk_metrics.concentration_risk = self._assess_concentration_risk(current_market_data)
            
            # Calculate overall risk score
            risk_metrics.overall_risk_score = self._calculate_overall_risk_score(risk_metrics)
            
            # Determine risk level
            risk_metrics.risk_level = self._determine_risk_level(risk_metrics.overall_risk_score)
            
            # Identify risk factors
            risk_metrics.risk_factors = self._identify_risk_factors(risk_metrics, current_market_data)
            
            result = {
                'liquidity_risk': risk_metrics.liquidity_risk,
                'volatility_risk': risk_metrics.volatility_risk,
                'market_risk': risk_metrics.market_risk,
                'concentration_risk': risk_metrics.concentration_risk,
                'overall_risk_score': risk_metrics.overall_risk_score,
                'risk_level': risk_metrics.risk_level,
                'risk_factors': risk_metrics.risk_factors,
                'assessment_timestamp': datetime.utcnow(),
                'liquidity_usd': float(current_market_data.liquidity) if current_market_data.liquidity else 0,
                'volume_24h_usd': float(current_market_data.volume_24h) if current_market_data.volume_24h else 0,
                'market_cap_usd': float(current_market_data.market_cap) if current_market_data.market_cap else 0
            }
            
            self.logger.info(
                f"Risk assessment completed for {token_address}. "
                f"Overall risk: {risk_metrics.overall_risk_score:.2f} ({risk_metrics.risk_level})"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error assessing risk for {token_address}: {str(e)}")
            return self._get_default_risk_assessment()
    
    def _assess_liquidity_risk(self, market_data: MarketData) -> float:
        """Assess liquidity risk based on available liquidity and volume"""
        try:
            if not market_data.liquidity or market_data.liquidity <= 0:
                return 1.0  # Maximum risk if no liquidity data
            
            liquidity_usd = market_data.liquidity
            volume_24h = market_data.volume_24h or Decimal("0")
            
            # Base liquidity risk
            if liquidity_usd < self.min_liquidity_usd:
                liquidity_risk = 0.9  # Very high risk
            elif liquidity_usd < self.low_liquidity_threshold:
                liquidity_risk = 0.7  # High risk
            elif liquidity_usd < Decimal("500000"):
                liquidity_risk = 0.5  # Moderate risk
            elif liquidity_usd < Decimal("1000000"):
                liquidity_risk = 0.3  # Low risk
            else:
                liquidity_risk = 0.1  # Very low risk
            
            # Adjust for volume/liquidity ratio
            if volume_24h > 0:
                volume_ratio = float(volume_24h / liquidity_usd)
                if volume_ratio > self.volume_liquidity_ratio_threshold:
                    liquidity_risk += 0.2  # Increase risk if high volume relative to liquidity
            
            return min(1.0, liquidity_risk)
            
        except Exception as e:
            self.logger.error(f"Error assessing liquidity risk: {str(e)}")
            return 0.8  # High risk on error
    
    def _assess_volatility_risk(self, price_history: List[MarketData]) -> float:
        """Assess volatility risk based on price history"""
        try:
            if len(price_history) < 2:
                return 0.8  # High risk if insufficient data
            
            # Calculate price returns
            prices = [float(data.price) for data in price_history]
            returns = []
            
            for i in range(1, len(prices)):
                if prices[i-1] > 0:
                    return_pct = (prices[i] - prices[i-1]) / prices[i-1]
                    returns.append(return_pct)
            
            if not returns:
                return 0.8
            
            # Calculate volatility (standard deviation of returns)
            volatility = np.std(returns)
            
            # Convert volatility to risk score
            if volatility > self.high_volatility_threshold:
                volatility_risk = 0.9  # Very high risk
            elif volatility > 0.05:  # 5%
                volatility_risk = 0.7  # High risk
            elif volatility > 0.03:  # 3%
                volatility_risk = 0.5  # Moderate risk
            elif volatility > 0.01:  # 1%
                volatility_risk = 0.3  # Low risk
            else:
                volatility_risk = 0.1  # Very low risk
            
            return volatility_risk
            
        except Exception as e:
            self.logger.error(f"Error assessing volatility risk: {str(e)}")
            return 0.8
    
    def _assess_market_risk(self, market_data: MarketData, price_history: List[MarketData]) -> float:
        """Assess market risk based on market conditions"""
        try:
            market_risk = 0.5  # Start with moderate risk
            
            # Market cap risk
            if market_data.market_cap:
                market_cap = market_data.market_cap
                if market_cap < Decimal("1000000"):  # < $1M
                    market_risk += 0.3
                elif market_cap < Decimal("10000000"):  # < $10M
                    market_risk += 0.2
                elif market_cap < Decimal("100000000"):  # < $100M
                    market_risk += 0.1
            else:
                market_risk += 0.2  # No market cap data
            
            # Price trend risk
            if len(price_history) >= 7:
                recent_prices = [float(data.price) for data in price_history[-7:]]
                price_change_7d = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                
                if price_change_7d < -0.5:  # > 50% drop
                    market_risk += 0.3
                elif price_change_7d < -0.3:  # > 30% drop
                    market_risk += 0.2
                elif price_change_7d > 2.0:  # > 200% gain (bubble risk)
                    market_risk += 0.2
            
            # Volume consistency risk
            if len(price_history) >= 3:
                recent_volumes = [
                    float(data.volume_24h) if data.volume_24h else 0
                    for data in price_history[-3:]
                ]
                if any(vol == 0 for vol in recent_volumes):
                    market_risk += 0.1
                else:
                    volume_cv = np.std(recent_volumes) / np.mean(recent_volumes)
                    if volume_cv > 1.0:  # High volume inconsistency
                        market_risk += 0.1
            
            return min(1.0, market_risk)
            
        except Exception as e:
            self.logger.error(f"Error assessing market risk: {str(e)}")
            return 0.7
    
    def _assess_concentration_risk(self, market_data: MarketData) -> float:
        """Assess concentration risk (placeholder for holder concentration analysis)"""
        try:
            # This would require on-chain analysis of token holders
            # For now, return moderate risk
            # In a full implementation, this would analyze:
            # - Top holder percentages
            # - Number of holders
            # - Distribution of holdings
            
            concentration_risk = 0.5  # Moderate default
            
            # If market cap is very small, assume higher concentration risk
            if market_data.market_cap and market_data.market_cap < Decimal("5000000"):
                concentration_risk = 0.7
            
            return concentration_risk
            
        except Exception as e:
            self.logger.error(f"Error assessing concentration risk: {str(e)}")
            return 0.6
    
    def _calculate_overall_risk_score(self, risk_metrics: RiskMetrics) -> float:
        """Calculate weighted overall risk score"""
        try:
            # Weighted average of risk components
            weights = {
                'liquidity': 0.35,
                'volatility': 0.25,
                'market': 0.25,
                'concentration': 0.15
            }
            
            overall_risk = (
                risk_metrics.liquidity_risk * weights['liquidity'] +
                risk_metrics.volatility_risk * weights['volatility'] +
                risk_metrics.market_risk * weights['market'] +
                risk_metrics.concentration_risk * weights['concentration']
            )
            
            return min(1.0, max(0.0, overall_risk))
            
        except Exception as e:
            self.logger.error(f"Error calculating overall risk score: {str(e)}")
            return 0.7
    
    def _determine_risk_level(self, risk_score: float) -> str:
        """Determine risk level based on score"""
        if risk_score >= 0.8:
            return "very_high"
        elif risk_score >= 0.6:
            return "high"
        elif risk_score >= 0.4:
            return "moderate"
        elif risk_score >= 0.2:
            return "low"
        else:
            return "very_low"
    
    def _identify_risk_factors(self, risk_metrics: RiskMetrics, market_data: MarketData) -> List[str]:
        """Identify specific risk factors"""
        factors = []
        
        if risk_metrics.liquidity_risk > 0.7:
            factors.append("Low liquidity")
        
        if risk_metrics.volatility_risk > 0.7:
            factors.append("High volatility")
        
        if risk_metrics.market_risk > 0.7:
            factors.append("Adverse market conditions")
        
        if risk_metrics.concentration_risk > 0.7:
            factors.append("High holder concentration")
        
        if market_data.liquidity and market_data.liquidity < self.min_liquidity_usd:
            factors.append("Insufficient liquidity")
        
        if not market_data.market_cap:
            factors.append("Unknown market cap")
        
        if not market_data.volume_24h or market_data.volume_24h == 0:
            factors.append("No trading volume")
        
        return factors
    
    def _get_default_risk_assessment(self) -> Dict[str, Any]:
        """Return default risk assessment on error"""
        return {
            'liquidity_risk': 0.8,
            'volatility_risk': 0.8,
            'market_risk': 0.8,
            'concentration_risk': 0.8,
            'overall_risk_score': 0.8,
            'risk_level': 'high',
            'risk_factors': ['Assessment error - high risk assumed'],
            'assessment_timestamp': datetime.utcnow(),
            'liquidity_usd': 0,
            'volume_24h_usd': 0,
            'market_cap_usd': 0
        }
