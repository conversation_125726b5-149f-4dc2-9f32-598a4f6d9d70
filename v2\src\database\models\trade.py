"""
💼 Trade Database Model

Beanie ODM model for paper trading trades following DATABASE_PATTERNS.md
with proper indexing, validation, and performance tracking.
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, Optional, Any
from beanie import Document, Indexed
from pydantic import Field, validator
from pymongo import IndexModel, ASCENDING, DESCENDING

from .base import BaseDocument
from ...shared.types import SignalType, OrderType, TradeStatus


class Trade(BaseDocument):
    """
    💼 Paper Trading Trade Model
    
    Stores individual trade executions with performance tracking,
    fees, slippage, and analysis following V2 architecture patterns.
    """
    
    # 🎯 Core Trade Data
    portfolio_id: Indexed(str) = Field(..., description="Portfolio ID")
    signal_id: Optional[str] = Field(None, description="Source signal ID")
    token_address: Indexed(str) = Field(..., description="Token address")
    
    # 📊 Trade Details
    side: SignalType = Field(..., description="Trade side (BUY/SELL)")
    order_type: OrderType = Field(..., description="Order type")
    quantity: Decimal = Field(..., description="Trade quantity")
    price: Decimal = Field(..., description="Execution price")
    value_usd: Decimal = Field(..., description="Trade value in USD")
    
    # 💰 Costs and Slippage
    fees: Decimal = Field(default=Decimal("0"), description="Trading fees")
    slippage: Decimal = Field(default=Decimal("0"), description="Price slippage")
    gas_fees: Decimal = Field(default=Decimal("0"), description="Gas fees")
    total_cost: Decimal = Field(default=Decimal("0"), description="Total trade cost")
    
    # 📈 Performance Data
    entry_price: Optional[Decimal] = Field(None, description="Position entry price")
    exit_price: Optional[Decimal] = Field(None, description="Position exit price")
    pnl: Decimal = Field(default=Decimal("0"), description="Realized P&L")
    pnl_percent: Decimal = Field(default=Decimal("0"), description="P&L percentage")
    
    # 🔍 Risk Management
    stop_loss: Optional[Decimal] = Field(None, description="Stop loss price")
    take_profit: Optional[Decimal] = Field(None, description="Take profit price")
    risk_amount: Decimal = Field(default=Decimal("0"), description="Risk amount")
    risk_percent: Decimal = Field(default=Decimal("0"), description="Risk percentage")
    
    # 📊 Execution Data
    status: TradeStatus = Field(..., description="Trade status")
    execution_time: Optional[datetime] = Field(None, description="Execution timestamp")
    fill_time: Optional[datetime] = Field(None, description="Fill timestamp")
    cancel_time: Optional[datetime] = Field(None, description="Cancel timestamp")
    
    # 🔗 Related Data
    position_id: Optional[str] = Field(None, description="Related position ID")
    parent_trade_id: Optional[str] = Field(None, description="Parent trade ID")
    child_trade_ids: list[str] = Field(default_factory=list, description="Child trade IDs")
    
    # 📋 Metadata
    strategy: Optional[str] = Field(None, description="Trading strategy")
    notes: Optional[str] = Field(None, description="Trade notes")
    tags: list[str] = Field(default_factory=list, description="Trade tags")
    
    # 🔍 Analysis Data
    market_conditions: Dict[str, Any] = Field(default_factory=dict, description="Market conditions")
    technical_indicators: Dict[str, float] = Field(default_factory=dict, description="Technical indicators")
    execution_quality: Dict[str, Any] = Field(default_factory=dict, description="Execution quality metrics")
    
    # 🤖 Automation Data
    is_automated: bool = Field(default=False, description="Automated trade flag")
    automation_source: Optional[str] = Field(None, description="Automation source")
    
    @validator("quantity", "price", "value_usd")
    def validate_positive_values(cls, v):
        """Validate positive decimal values"""
        if v <= 0:
            raise ValueError("Value must be positive")
        return v
    
    @validator("risk_percent")
    def validate_risk_percentage(cls, v):
        """Validate risk percentage"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("Risk percentage must be between 0.0 and 1.0")
        return v
    
    @property
    def is_profitable(self) -> bool:
        """Check if trade is profitable"""
        return self.pnl > 0
    
    @property
    def is_executed(self) -> bool:
        """Check if trade is executed"""
        return self.status == TradeStatus.EXECUTED
    
    @property
    def is_pending(self) -> bool:
        """Check if trade is pending"""
        return self.status == TradeStatus.PENDING
    
    @property
    def execution_duration(self) -> Optional[float]:
        """Calculate execution duration in seconds"""
        if self.execution_time and self.created_at:
            return (self.execution_time - self.created_at).total_seconds()
        return None
    
    def calculate_pnl(self, current_price: Optional[Decimal] = None) -> Dict[str, Any]:
        """
        Calculate trade P&L
        
        Args:
            current_price: Current market price for unrealized P&L
            
        Returns:
            P&L calculation results
        """
        if self.status != TradeStatus.EXECUTED:
            return {"status": "not_executed", "pnl": 0, "pnl_percent": 0}
        
        # For closed positions, use realized P&L
        if self.exit_price:
            if self.side == SignalType.BUY:
                pnl = (self.exit_price - self.price) * self.quantity
                pnl_percent = ((self.exit_price - self.price) / self.price) * 100
            else:  # SELL
                pnl = (self.price - self.exit_price) * self.quantity
                pnl_percent = ((self.price - self.exit_price) / self.price) * 100
            
            return {
                "status": "closed",
                "pnl": float(pnl - self.total_cost),
                "pnl_percent": float(pnl_percent),
                "is_profitable": pnl > self.total_cost
            }
        
        # For open positions, calculate unrealized P&L
        if current_price:
            if self.side == SignalType.BUY:
                unrealized_pnl = (current_price - self.price) * self.quantity
                pnl_percent = ((current_price - self.price) / self.price) * 100
            else:  # SELL
                unrealized_pnl = (self.price - current_price) * self.quantity
                pnl_percent = ((self.price - current_price) / self.price) * 100
            
            return {
                "status": "open",
                "unrealized_pnl": float(unrealized_pnl - self.total_cost),
                "pnl_percent": float(pnl_percent),
                "current_price": float(current_price),
                "is_profitable": unrealized_pnl > self.total_cost
            }
        
        return {"status": "open", "unrealized_pnl": 0, "pnl_percent": 0}
    
    def update_execution_quality(self, expected_price: Decimal, market_impact: Decimal) -> None:
        """Update execution quality metrics"""
        price_improvement = self.price - expected_price if self.side == SignalType.BUY else expected_price - self.price
        
        self.execution_quality = {
            "expected_price": float(expected_price),
            "actual_price": float(self.price),
            "price_improvement": float(price_improvement),
            "slippage_bps": float(self.slippage * 10000),
            "market_impact": float(market_impact),
            "execution_speed": self.execution_duration,
            "quality_score": self._calculate_quality_score(price_improvement, market_impact)
        }
    
    def _calculate_quality_score(self, price_improvement: Decimal, market_impact: Decimal) -> float:
        """Calculate execution quality score (0-100)"""
        # Base score
        score = 50.0
        
        # Price improvement bonus/penalty
        score += float(price_improvement) * 1000  # Scale to meaningful range
        
        # Market impact penalty
        score -= float(market_impact) * 500
        
        # Slippage penalty
        score -= float(self.slippage) * 1000
        
        # Execution speed bonus (faster is better)
        if self.execution_duration:
            score += max(0, 10 - self.execution_duration)
        
        return max(0.0, min(100.0, score))
    
    class Settings:
        name = "trades"
        indexes = [
            # Core query indexes
            IndexModel([("portfolio_id", ASCENDING), ("created_at", DESCENDING)]),
            IndexModel([("token_address", ASCENDING), ("created_at", DESCENDING)]),
            IndexModel([("signal_id", ASCENDING)]),
            
            # Performance indexes
            IndexModel([("pnl", DESCENDING)]),
            IndexModel([("pnl_percent", DESCENDING)]),
            IndexModel([("status", ASCENDING), ("execution_time", DESCENDING)]),
            
            # Trading indexes
            IndexModel([("side", ASCENDING), ("created_at", DESCENDING)]),
            IndexModel([("order_type", ASCENDING)]),
            IndexModel([("strategy", ASCENDING), ("created_at", DESCENDING)]),
            
            # Risk indexes
            IndexModel([("risk_percent", DESCENDING)]),
            IndexModel([("stop_loss", ASCENDING)]),
            
            # Compound indexes
            IndexModel([
                ("portfolio_id", ASCENDING),
                ("status", ASCENDING),
                ("created_at", DESCENDING)
            ]),
            IndexModel([
                ("token_address", ASCENDING),
                ("side", ASCENDING),
                ("execution_time", DESCENDING)
            ]),
            IndexModel([
                ("portfolio_id", ASCENDING),
                ("pnl", DESCENDING),
                ("created_at", DESCENDING)
            ])
        ]
