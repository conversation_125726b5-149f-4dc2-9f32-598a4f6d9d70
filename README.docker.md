# Running the Dune Analytics Token Tracker with <PERSON><PERSON>

This guide explains how to run the Dune Analytics Token Tracker using Docker and Docker Compose.

## Prerequisites

- Docker installed on your system
- Docker Compose installed on your system

## Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd dune-analytics-tracker
```

2. Make sure you have a `.env` file with the necessary configuration. The Docker setup will use a local MongoDB container, so the MongoDB URI should be set to:
```
MONGODB_URI=mongodb://mongodb:27017/dune_analytics
```

3. You still need to provide your own:
   - Dune Analytics API key
   - Telegram Bot <PERSON>ken and Channel ID

## Running with Docker Compose

1. Start all services:
```bash
docker-compose up -d
```

This will start:
- MongoDB container
- The main application service
- The web dashboard service

2. Access the web dashboard at:
```
http://localhost:5005
```

3. View logs:
```bash
# View logs for all services
docker-compose logs -f

# View logs for a specific service
docker-compose logs -f app
docker-compose logs -f web
docker-compose logs -f mongodb
```

4. Stop all services:
```bash
docker-compose down
```

## Container Details

- **MongoDB**: Runs on port 27017 and stores data in a persistent volume
- **App Service**: Runs the main Dune Analytics data collection service
- **Web Service**: Runs the Flask web dashboard on port 5005

## Data Persistence

MongoDB data is stored in a Docker volume named `mongodb_data`. This ensures your data persists even when containers are stopped or removed.

## Troubleshooting

1. If you encounter connection issues between services, ensure all containers are running:
```bash
docker-compose ps
```

2. If the app can't connect to MongoDB, check the MongoDB container logs:
```bash
docker-compose logs mongodb
```

3. To reset the database, you can remove the volume:
```bash
docker-compose down -v
```
Note: This will delete all stored data.

4. To rebuild the containers after code changes:
```bash
docker-compose build
docker-compose up -d
```
