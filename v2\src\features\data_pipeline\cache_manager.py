"""
🗄️ Cache Manager

Redis-based cache management for data pipeline with TTL support,
cache invalidation, and performance optimization following V2 architecture patterns.
"""

import asyncio
import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from decimal import Decimal
import redis.asyncio as redis
from dataclasses import asdict

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import API_CONSTANTS, ERROR_CONSTANTS
from ...shared.types import TokenData, TokenMetrics

logger = get_logger(__name__)


class CacheManager:
    """
    🗄️ Redis Cache Manager for data pipeline
    
    Provides comprehensive caching with:
    - TTL-based cache expiration
    - Cache invalidation strategies
    - Performance optimization
    - Data serialization/deserialization
    """
    
    def __init__(self, project_id: str = "v2"):
        self.settings = get_settings()
        self.project_id = project_id
        
        # Redis connection
        self.redis_client = None
        self.redis_url = self.settings.redis_url
        
        # Cache configuration
        self.default_ttl = 300  # 5 minutes default TTL
        self.key_prefix = f"tokentracker_v2_{project_id}"
        
        # Cache statistics
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "errors": 0
        }
        
        logger.info(
            "Cache manager initialized",
            project_id=self.project_id,
            redis_url=self.redis_url
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def connect(self):
        """Connect to Redis"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=False  # We'll handle encoding ourselves
            )
            
            # Test connection
            await self.redis_client.ping()
            
            logger.info("Connected to Redis successfully")
            
        except Exception as e:
            logger.error(
                "Failed to connect to Redis",
                error=str(e),
                error_type=type(e).__name__
            )
            raise
    
    async def close(self):
        """Close Redis connection"""
        try:
            if self.redis_client:
                await self.redis_client.close()
                self.redis_client = None
            
            logger.info("Redis connection closed")
            
        except Exception as e:
            logger.error(
                "Error closing Redis connection",
                error=str(e),
                error_type=type(e).__name__
            )
    
    def _make_key(self, key: str) -> str:
        """Create prefixed cache key"""
        return f"{self.key_prefix}:{key}"
    
    def _serialize_data(self, data: Any) -> bytes:
        """Serialize data for Redis storage"""
        try:
            # Handle special types
            if isinstance(data, (TokenData, TokenMetrics)):
                # Convert Pydantic models to dict
                data_dict = data.dict()
                # Convert Decimal to string for JSON serialization
                for key, value in data_dict.items():
                    if isinstance(value, Decimal):
                        data_dict[key] = str(value)
                    elif isinstance(value, datetime):
                        data_dict[key] = value.isoformat()
                return json.dumps(data_dict).encode('utf-8')
            
            elif isinstance(data, dict):
                # Handle dictionaries with special types
                serializable_data = {}
                for key, value in data.items():
                    if isinstance(value, Decimal):
                        serializable_data[key] = str(value)
                    elif isinstance(value, datetime):
                        serializable_data[key] = value.isoformat()
                    else:
                        serializable_data[key] = value
                return json.dumps(serializable_data).encode('utf-8')
            
            else:
                # Use pickle for complex objects
                return pickle.dumps(data)
                
        except Exception as e:
            logger.error(
                "Error serializing data for cache",
                error=str(e),
                error_type=type(e).__name__
            )
            raise
    
    def _deserialize_data(self, data: bytes, data_type: str = None) -> Any:
        """Deserialize data from Redis"""
        try:
            if data_type in ["TokenData", "TokenMetrics", "dict"]:
                # JSON deserialization
                data_dict = json.loads(data.decode('utf-8'))
                
                # Convert string decimals back to Decimal
                for key, value in data_dict.items():
                    if isinstance(value, str) and key in ['price', 'market_cap', 'volume_24h', 'liquidity']:
                        try:
                            data_dict[key] = Decimal(value)
                        except:
                            pass
                    elif isinstance(value, str) and key.endswith('_at'):
                        try:
                            data_dict[key] = datetime.fromisoformat(value)
                        except:
                            pass
                
                # Reconstruct specific types
                if data_type == "TokenData":
                    return TokenData(**data_dict)
                elif data_type == "TokenMetrics":
                    return TokenMetrics(**data_dict)
                else:
                    return data_dict
            else:
                # Pickle deserialization
                return pickle.loads(data)
                
        except Exception as e:
            logger.error(
                "Error deserializing data from cache",
                error=str(e),
                error_type=type(e).__name__
            )
            raise
    
    async def get(self, key: str, data_type: str = None) -> Optional[Any]:
        """
        📥 Get data from cache
        
        Args:
            key: Cache key
            data_type: Expected data type for deserialization
            
        Returns:
            Cached data or None if not found
        """
        try:
            if not self.redis_client:
                await self.connect()
            
            cache_key = self._make_key(key)
            data = await self.redis_client.get(cache_key)
            
            if data is None:
                self.stats["misses"] += 1
                logger.debug("Cache miss", key=key)
                return None
            
            self.stats["hits"] += 1
            logger.debug("Cache hit", key=key)
            
            return self._deserialize_data(data, data_type)
            
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(
                "Error getting data from cache",
                key=key,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def set(self, key: str, data: Any, ttl: Optional[int] = None) -> bool:
        """
        📤 Set data in cache
        
        Args:
            key: Cache key
            data: Data to cache
            ttl: Time to live in seconds (uses default if None)
            
        Returns:
            True if successful
        """
        try:
            if not self.redis_client:
                await self.connect()
            
            cache_key = self._make_key(key)
            serialized_data = self._serialize_data(data)
            
            if ttl is None:
                ttl = self.default_ttl
            
            await self.redis_client.setex(cache_key, ttl, serialized_data)
            
            self.stats["sets"] += 1
            logger.debug(
                "Data cached successfully",
                key=key,
                ttl=ttl
            )
            
            return True
            
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(
                "Error setting data in cache",
                key=key,
                error=str(e),
                error_type=type(e).__name__
            )
            return False
    
    async def delete(self, key: str) -> bool:
        """
        🗑️ Delete data from cache
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if successful
        """
        try:
            if not self.redis_client:
                await self.connect()
            
            cache_key = self._make_key(key)
            result = await self.redis_client.delete(cache_key)
            
            self.stats["deletes"] += 1
            logger.debug(
                "Cache key deleted",
                key=key,
                existed=result > 0
            )
            
            return result > 0
            
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(
                "Error deleting data from cache",
                key=key,
                error=str(e),
                error_type=type(e).__name__
            )
            return False
    
    async def exists(self, key: str) -> bool:
        """
        🔍 Check if key exists in cache
        
        Args:
            key: Cache key to check
            
        Returns:
            True if key exists
        """
        try:
            if not self.redis_client:
                await self.connect()
            
            cache_key = self._make_key(key)
            result = await self.redis_client.exists(cache_key)
            
            return result > 0
            
        except Exception as e:
            logger.error(
                "Error checking cache key existence",
                key=key,
                error=str(e),
                error_type=type(e).__name__
            )
            return False
    
    async def get_ttl(self, key: str) -> Optional[int]:
        """
        ⏰ Get TTL for cache key
        
        Args:
            key: Cache key
            
        Returns:
            TTL in seconds or None if key doesn't exist
        """
        try:
            if not self.redis_client:
                await self.connect()
            
            cache_key = self._make_key(key)
            ttl = await self.redis_client.ttl(cache_key)
            
            if ttl == -2:  # Key doesn't exist
                return None
            elif ttl == -1:  # Key exists but no TTL
                return -1
            else:
                return ttl
                
        except Exception as e:
            logger.error(
                "Error getting TTL for cache key",
                key=key,
                error=str(e),
                error_type=type(e).__name__
            )
            return None

    async def health_check(self) -> bool:
        """
        🏥 Check cache health

        Returns:
            True if cache is healthy
        """
        try:
            if not self.redis_client:
                await self.connect()

            # Test basic operations
            test_key = "health_check"
            test_value = {"timestamp": datetime.utcnow().isoformat()}

            # Set test data
            await self.set(test_key, test_value, ttl=60)

            # Get test data
            retrieved = await self.get(test_key, "dict")

            # Clean up
            await self.delete(test_key)

            if retrieved and retrieved.get("timestamp"):
                logger.info("Cache health check passed")
                return True
            else:
                logger.warning("Cache health check failed - data mismatch")
                return False

        except Exception as e:
            logger.error(
                "Cache health check failed",
                error=str(e),
                error_type=type(e).__name__
            )
            return False
