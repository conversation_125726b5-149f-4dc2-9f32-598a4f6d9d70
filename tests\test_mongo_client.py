import pytest
from unittest.mock import Mock, patch
from datetime import datetime
from src.mongo_client import MongoClient

def test_save_query_results(mongo_client, mock_dune_response):
    # Mock MongoDB operations
    mock_insert = Mock(return_value=Mock(inserted_id="test_id"))
    mongo_client.collection.insert_one = mock_insert
    
    # Save results
    doc_id = mongo_client.save_query_results(mock_dune_response)
    
    # Verify the save operation
    assert doc_id == "test_id"
    mock_insert.assert_called_once()
    saved_doc = mock_insert.call_args[0][0]
    assert saved_doc["results"] == mock_dune_response
    assert isinstance(saved_doc["timestamp"], datetime)
    assert not saved_doc["processed"]

def test_get_unprocessed_results(mongo_client, mock_dune_response):
    # Mock MongoDB query
    mock_results = [{"_id": "test_id", "results": mock_dune_response, "processed": False}]
    mongo_client.collection.find.return_value = mock_results
    
    # Get unprocessed results
    results = mongo_client.get_unprocessed_results()
    
    # Verify results
    assert results == mock_results
    mongo_client.collection.find.assert_called_once_with({"processed": False})

def test_mark_as_processed(mongo_client):
    # Mock MongoDB update
    mock_update = Mock()
    mongo_client.collection.update_one = mock_update
    
    # Mark document as processed
    mongo_client.mark_as_processed("test_id")
    
    # Verify update operation
    mock_update.assert_called_once_with(
        {"_id": "test_id"},
        {"$set": {"processed": True}}
    )

def test_get_latest_results(mongo_client, mock_dune_response):
    # Mock MongoDB query
    mock_result = {"_id": "test_id", "results": mock_dune_response}
    mongo_client.collection.find_one.return_value = mock_result
    
    # Get latest results
    result = mongo_client.get_latest_results()
    
    # Verify results
    assert result == mock_result
    mongo_client.collection.find_one.assert_called_once_with(
        sort=[("timestamp", -1)]
    ) 