"""
🔔 Signal Notifier Service

Automated signal notification service that integrates with the signal processing
engine to send real-time alerts to subscribed users.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal

from ...config.logging_config import get_logger
from ...shared.types import SignalData, SignalType, SignalStrength
from ...database.models import Signal, Portfolio, Trade

from .notification_manager import NotificationManager, NotificationPriority
from .subscription_manager import SubscriptionManager

logger = get_logger(__name__)


class SignalNotifier:
    """
    🔔 Signal Notifier Service
    
    Provides automated signal notifications with:
    - Real-time signal alerts
    - Portfolio-specific notifications
    - Trade execution confirmations
    - Performance updates
    - Risk warnings
    """
    
    def __init__(self):
        self.logger = logger
        self.notification_manager = NotificationManager()
        self.subscription_manager = SubscriptionManager()
        
        # Notification settings
        self.signal_notification_enabled = True
        self.portfolio_notification_enabled = True
        self.trade_notification_enabled = True
        
        # Batch processing settings
        self.batch_size = 10
        self.batch_delay_seconds = 1
    
    async def notify_signal_generated(self, signal: SignalData) -> Dict[str, Any]:
        """
        Send notification when a new signal is generated
        
        Args:
            signal: Generated signal data
            
        Returns:
            Notification delivery results
        """
        try:
            if not self.signal_notification_enabled:
                return {"status": "disabled"}
            
            self.logger.info(f"Sending signal notification for {signal.token_address}")
            
            # Determine priority based on signal strength and confidence
            priority = self._determine_signal_priority(signal)
            
            # Get subscribers based on signal characteristics
            subscribers = await self.subscription_manager.get_signal_subscribers(
                signal_strength=signal.strength,
                confidence_threshold=signal.confidence
            )
            
            if not subscribers:
                self.logger.info("No subscribers for signal notifications")
                return {"status": "no_subscribers"}
            
            # Send signal alert
            results = await self.notification_manager.send_signal_alert(
                signal,
                subscribers,
                priority
            )
            
            # Log notification results
            self.logger.info(
                f"Signal notification sent to {results.get('successful_deliveries', 0)} users. "
                f"Failed: {results.get('failed_deliveries', 0)}, "
                f"Rate limited: {results.get('rate_limited', 0)}"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending signal notification: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def notify_trade_executed(
        self,
        trade_data: Dict[str, Any],
        portfolio_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Send notification when a trade is executed
        
        Args:
            trade_data: Trade execution data
            portfolio_id: Portfolio ID (if applicable)
            
        Returns:
            Notification delivery results
        """
        try:
            if not self.trade_notification_enabled:
                return {"status": "disabled"}
            
            self.logger.info(f"Sending trade notification for {trade_data.get('trade_id')}")
            
            # Determine priority based on trade value and P&L
            priority = self._determine_trade_priority(trade_data)
            
            # Get subscribers
            subscribers = await self.subscription_manager.get_trade_subscribers(portfolio_id)
            
            if not subscribers:
                return {"status": "no_subscribers"}
            
            # Send trade notification
            results = await self.notification_manager.send_trade_notification(
                trade_data,
                subscribers,
                priority
            )
            
            self.logger.info(
                f"Trade notification sent to {results.get('successful_deliveries', 0)} users"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending trade notification: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def notify_portfolio_update(
        self,
        portfolio_data: Dict[str, Any],
        trigger_reason: str = "scheduled"
    ) -> Dict[str, Any]:
        """
        Send portfolio performance update notification
        
        Args:
            portfolio_data: Portfolio data including performance metrics
            trigger_reason: Reason for the update (scheduled, threshold, manual)
            
        Returns:
            Notification delivery results
        """
        try:
            if not self.portfolio_notification_enabled:
                return {"status": "disabled"}
            
            portfolio_id = portfolio_data["portfolio"]["id"]
            self.logger.info(f"Sending portfolio update for {portfolio_id} ({trigger_reason})")
            
            # Determine priority based on performance change
            priority = self._determine_portfolio_priority(portfolio_data, trigger_reason)
            
            # Get subscribers for this portfolio
            subscribers = await self.subscription_manager.get_portfolio_subscribers(portfolio_id)
            
            if not subscribers:
                return {"status": "no_subscribers"}
            
            # Send portfolio update
            results = await self.notification_manager.send_portfolio_update(
                portfolio_data,
                subscribers,
                priority
            )
            
            self.logger.info(
                f"Portfolio update sent to {results.get('successful_deliveries', 0)} users"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending portfolio update: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def notify_risk_warning(
        self,
        warning_data: Dict[str, Any],
        affected_portfolios: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Send risk warning notification
        
        Args:
            warning_data: Risk warning information
            affected_portfolios: List of affected portfolio IDs
            
        Returns:
            Notification delivery results
        """
        try:
            self.logger.warning(f"Sending risk warning: {warning_data.get('type', 'unknown')}")
            
            # Get all subscribers for risk warnings
            subscribers = set()
            
            # Add general risk warning subscribers
            general_subscribers = await self.subscription_manager.get_signal_subscribers()
            subscribers.update(general_subscribers)
            
            # Add portfolio-specific subscribers if portfolios are affected
            if affected_portfolios:
                for portfolio_id in affected_portfolios:
                    portfolio_subscribers = await self.subscription_manager.get_portfolio_subscribers(portfolio_id)
                    subscribers.update(portfolio_subscribers)
            
            if not subscribers:
                return {"status": "no_subscribers"}
            
            # Send risk warning with critical priority
            results = await self.notification_manager.send_risk_warning(
                warning_data,
                list(subscribers),
                NotificationPriority.CRITICAL
            )
            
            self.logger.warning(
                f"Risk warning sent to {results.get('successful_deliveries', 0)} users"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending risk warning: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def send_daily_performance_report(self) -> Dict[str, Any]:
        """Send daily performance report to subscribers"""
        try:
            self.logger.info("Generating daily performance report")
            
            # Get report subscribers
            subscribers = await self.subscription_manager.get_report_subscribers()
            
            if not subscribers:
                return {"status": "no_subscribers"}
            
            # Generate report data (simplified for now)
            report_data = await self._generate_daily_report()
            
            # Send performance report
            results = await self.notification_manager.send_performance_report(
                report_data,
                subscribers,
                NotificationPriority.LOW
            )
            
            self.logger.info(
                f"Daily report sent to {results.get('successful_deliveries', 0)} users"
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error sending daily report: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def process_signal_batch(self, signals: List[SignalData]) -> Dict[str, Any]:
        """
        Process a batch of signals for notification
        
        Args:
            signals: List of signals to process
            
        Returns:
            Batch processing results
        """
        try:
            self.logger.info(f"Processing batch of {len(signals)} signals")
            
            results = {
                "total_signals": len(signals),
                "successful_notifications": 0,
                "failed_notifications": 0,
                "details": []
            }
            
            # Process signals in batches to avoid overwhelming the system
            for i in range(0, len(signals), self.batch_size):
                batch = signals[i:i + self.batch_size]
                
                # Process each signal in the batch
                batch_tasks = []
                for signal in batch:
                    task = self.notify_signal_generated(signal)
                    batch_tasks.append(task)
                
                # Wait for batch completion
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # Process results
                for j, result in enumerate(batch_results):
                    signal = batch[j]
                    
                    if isinstance(result, Exception):
                        results["failed_notifications"] += 1
                        results["details"].append({
                            "signal_id": signal.id,
                            "status": "error",
                            "error": str(result)
                        })
                    elif result.get("status") == "error":
                        results["failed_notifications"] += 1
                        results["details"].append({
                            "signal_id": signal.id,
                            "status": "error",
                            "error": result.get("error")
                        })
                    else:
                        results["successful_notifications"] += 1
                        results["details"].append({
                            "signal_id": signal.id,
                            "status": "success",
                            "deliveries": result.get("successful_deliveries", 0)
                        })
                
                # Add delay between batches
                if i + self.batch_size < len(signals):
                    await asyncio.sleep(self.batch_delay_seconds)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error processing signal batch: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def _determine_signal_priority(self, signal: SignalData) -> NotificationPriority:
        """Determine notification priority for a signal"""
        # High priority for strong signals with high confidence
        if signal.strength in [SignalStrength.STRONG, SignalStrength.VERY_STRONG] and signal.confidence >= 0.8:
            return NotificationPriority.HIGH
        
        # Medium priority for moderate signals or good confidence
        elif signal.strength == SignalStrength.MODERATE or signal.confidence >= 0.7:
            return NotificationPriority.MEDIUM
        
        # Low priority for weak signals
        else:
            return NotificationPriority.LOW
    
    def _determine_trade_priority(self, trade_data: Dict[str, Any]) -> NotificationPriority:
        """Determine notification priority for a trade"""
        trade_value = trade_data.get("value_usd", 0)
        pnl = trade_data.get("pnl", 0)
        
        # High priority for large trades or significant P&L
        if trade_value > 5000 or abs(pnl) > 1000:
            return NotificationPriority.HIGH
        
        # Medium priority for moderate trades
        elif trade_value > 1000 or abs(pnl) > 100:
            return NotificationPriority.MEDIUM
        
        # Low priority for small trades
        else:
            return NotificationPriority.LOW
    
    def _determine_portfolio_priority(
        self,
        portfolio_data: Dict[str, Any],
        trigger_reason: str
    ) -> NotificationPriority:
        """Determine notification priority for portfolio update"""
        portfolio = portfolio_data["portfolio"]
        return_percent = abs(portfolio.get("total_return_percent", 0))
        
        # High priority for significant changes or manual triggers
        if trigger_reason == "threshold" or return_percent > 10:
            return NotificationPriority.HIGH
        
        # Medium priority for moderate changes
        elif return_percent > 5:
            return NotificationPriority.MEDIUM
        
        # Low priority for scheduled updates
        else:
            return NotificationPriority.LOW
    
    async def _generate_daily_report(self) -> Dict[str, Any]:
        """Generate daily performance report data"""
        try:
            # Get summary statistics (simplified)
            today = datetime.utcnow().date()
            
            # Count active signals
            active_signals = await Signal.find({
                "is_active": True,
                "expires_at": {"$gt": datetime.utcnow()}
            }).count()
            
            # Count today's trades
            today_start = datetime.combine(today, datetime.min.time())
            today_trades = await Trade.find({
                "execution_time": {"$gte": today_start}
            }).count()
            
            # Get active portfolios count
            active_portfolios = await Portfolio.find({
                "status": "ACTIVE"
            }).count()
            
            report_data = {
                "period": "Daily",
                "date": today.isoformat(),
                "summary": f"""
📊 <b>Daily Summary - {today.strftime('%B %d, %Y')}</b>

🎯 <b>Signals:</b> {active_signals} active
💼 <b>Portfolios:</b> {active_portfolios} active  
📈 <b>Trades:</b> {today_trades} executed today

Use the web interface for detailed analytics.
""",
                "metrics": f"""
• Active Signals: {active_signals}
• Active Portfolios: {active_portfolios}
• Today's Trades: {today_trades}
""",
                "generated_at": datetime.utcnow().isoformat()
            }
            
            return report_data
            
        except Exception as e:
            self.logger.error(f"Error generating daily report: {str(e)}")
            return {
                "period": "Daily",
                "summary": "Error generating daily report",
                "metrics": "Report generation failed",
                "generated_at": datetime.utcnow().isoformat()
            }
