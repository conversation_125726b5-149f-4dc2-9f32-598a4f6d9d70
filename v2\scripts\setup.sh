#!/bin/bash

# ===========================================
# 🚀 TOKENTRACKER V2 SETUP SCRIPT
# ===========================================
# Automated setup following V2 instructions

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check Python version
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    required_version="3.9"
    
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)"; then
        log_error "Python 3.9+ is required, found $python_version"
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_warning "Docker not found. Install Docker for containerized deployment"
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_warning "Docker Compose not found. Install Docker Compose for easy deployment"
    fi
    
    log_success "System requirements check completed"
}

# Setup Python virtual environment
setup_venv() {
    log_info "Setting up Python virtual environment..."
    
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        log_success "Virtual environment created"
    else
        log_info "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install requirements
    log_info "Installing Python dependencies..."
    pip install -r requirements.txt
    
    log_success "Python dependencies installed"
}

# Setup environment configuration
setup_env() {
    log_info "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        cp .env.example .env
        log_warning "Created .env file from template. Please update with your credentials!"
        log_info "Edit .env file with your API keys and configuration"
    else
        log_info ".env file already exists"
    fi
    
    # Create necessary directories
    mkdir -p logs data config/prometheus config/grafana/dashboards
    
    log_success "Environment configuration completed"
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
        log_info "Starting database services with Docker..."
        docker-compose -f docker-compose.dev.yml up -d mongodb redis
        
        # Wait for services to be ready
        log_info "Waiting for database services to be ready..."
        sleep 10
        
        # Test database connection
        if docker-compose -f docker-compose.dev.yml exec -T mongodb mongosh --eval "db.adminCommand('ping')" &> /dev/null; then
            log_success "MongoDB is ready"
        else
            log_warning "MongoDB may not be ready yet"
        fi
        
        if docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping &> /dev/null; then
            log_success "Redis is ready"
        else
            log_warning "Redis may not be ready yet"
        fi
        
    else
        log_warning "Docker not available. Please setup MongoDB and Redis manually"
        log_info "MongoDB: mongodb://localhost:27017/tokentracker_v2_dev"
        log_info "Redis: redis://localhost:6379"
    fi
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Run migrations (if any)
    python -m src.database.migrations.run_migrations
    
    log_success "Database migrations completed"
}

# Setup monitoring
setup_monitoring() {
    log_info "Setting up monitoring configuration..."
    
    # Create Prometheus config
    cat > config/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'tokentracker-v2'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
EOF

    # Create Grafana provisioning
    mkdir -p config/grafana/provisioning/datasources config/grafana/provisioning/dashboards
    
    cat > config/grafana/provisioning/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    cat > config/grafana/provisioning/dashboards/dashboard.yml << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF

    log_success "Monitoring configuration completed"
}

# Run tests
run_tests() {
    log_info "Running tests..."
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Run tests with coverage
    python -m pytest tests/ -v --cov=src --cov-report=term-missing --cov-report=html
    
    log_success "Tests completed"
}

# Main setup function
main() {
    log_info "Starting TokenTracker V2 setup..."
    
    check_root
    check_requirements
    setup_venv
    setup_env
    setup_database
    setup_monitoring
    
    log_success "Setup completed successfully!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Edit .env file with your API keys and configuration"
    log_info "2. Start the application:"
    log_info "   Development: docker-compose -f docker-compose.dev.yml up"
    log_info "   Production:  docker-compose up"
    log_info "3. Access the application at http://localhost:3000"
    log_info "4. View API documentation at http://localhost:3000/docs"
    log_info "5. Monitor with Grafana at http://localhost:3001"
    log_info ""
    log_info "For more information, see README.md"
}

# Handle script arguments
case "${1:-setup}" in
    "setup")
        main
        ;;
    "test")
        run_tests
        ;;
    "migrate")
        run_migrations
        ;;
    "clean")
        log_info "Cleaning up..."
        docker-compose -f docker-compose.dev.yml down -v
        rm -rf venv __pycache__ .pytest_cache htmlcov
        log_success "Cleanup completed"
        ;;
    "help")
        echo "TokenTracker V2 Setup Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  setup    - Full setup (default)"
        echo "  test     - Run tests"
        echo "  migrate  - Run database migrations"
        echo "  clean    - Clean up development environment"
        echo "  help     - Show this help"
        ;;
    *)
        log_error "Unknown command: $1"
        log_info "Use '$0 help' for available commands"
        exit 1
        ;;
esac
