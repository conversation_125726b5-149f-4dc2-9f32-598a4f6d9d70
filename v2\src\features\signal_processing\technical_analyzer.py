"""
📊 Technical Analysis Engine

Comprehensive technical analysis with RSI, MACD, Bollinger Bands,
volume analysis, and support/resistance detection following V2 patterns.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from ...config.logging_config import get_logger
from ...shared.constants import SIGNAL_CONSTANTS
from ...shared.types import TokenData, MarketData

logger = get_logger(__name__)


@dataclass
class TechnicalIndicators:
    """Technical indicators data structure"""
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None
    bb_width: Optional[float] = None
    volume_sma: Optional[float] = None
    volume_ratio: Optional[float] = None
    support_level: Optional[float] = None
    resistance_level: Optional[float] = None
    trend_direction: Optional[str] = None
    momentum_score: Optional[float] = None


class TechnicalAnalyzer:
    """
    📊 Technical Analysis Engine
    
    Provides comprehensive technical analysis including:
    - RSI (Relative Strength Index)
    - MACD (Moving Average Convergence Divergence)
    - Bollinger Bands
    - Volume analysis
    - Support/resistance levels
    - Momentum indicators
    """
    
    def __init__(self):
        self.logger = logger
        
        # Technical indicator parameters
        self.rsi_period = SIGNAL_CONSTANTS.RSI_PERIOD
        self.rsi_oversold = SIGNAL_CONSTANTS.RSI_OVERSOLD
        self.rsi_overbought = SIGNAL_CONSTANTS.RSI_OVERBOUGHT
        
        self.macd_fast = SIGNAL_CONSTANTS.MACD_FAST_PERIOD
        self.macd_slow = SIGNAL_CONSTANTS.MACD_SLOW_PERIOD
        self.macd_signal = SIGNAL_CONSTANTS.MACD_SIGNAL_PERIOD
        
        self.bb_period = SIGNAL_CONSTANTS.BOLLINGER_PERIOD
        self.bb_std_dev = SIGNAL_CONSTANTS.BOLLINGER_STD_DEV
        
        self.volume_period = SIGNAL_CONSTANTS.VOLUME_AVERAGE_PERIOD
        self.volume_spike_threshold = SIGNAL_CONSTANTS.VOLUME_SPIKE_THRESHOLD
    
    async def analyze_token(self, price_data: List[MarketData]) -> TechnicalIndicators:
        """
        Perform comprehensive technical analysis on token price data
        
        Args:
            price_data: List of market data points (chronological order)
            
        Returns:
            TechnicalIndicators object with calculated values
        """
        try:
            if len(price_data) < max(self.rsi_period, self.macd_slow, self.bb_period):
                self.logger.warning(f"Insufficient data for technical analysis. Need at least {max(self.rsi_period, self.macd_slow, self.bb_period)} points")
                return TechnicalIndicators()
            
            # Convert to pandas DataFrame for easier analysis
            df = self._prepare_dataframe(price_data)
            
            # Calculate all indicators
            indicators = TechnicalIndicators()
            
            # RSI calculation
            indicators.rsi = self._calculate_rsi(df['close'])
            
            # MACD calculation
            macd_data = self._calculate_macd(df['close'])
            indicators.macd = macd_data['macd']
            indicators.macd_signal = macd_data['signal']
            indicators.macd_histogram = macd_data['histogram']
            
            # Bollinger Bands calculation
            bb_data = self._calculate_bollinger_bands(df['close'])
            indicators.bb_upper = bb_data['upper']
            indicators.bb_middle = bb_data['middle']
            indicators.bb_lower = bb_data['lower']
            indicators.bb_width = bb_data['width']
            
            # Volume analysis
            volume_data = self._analyze_volume(df['volume'])
            indicators.volume_sma = volume_data['sma']
            indicators.volume_ratio = volume_data['ratio']
            
            # Support/Resistance levels
            sr_levels = self._find_support_resistance(df['high'], df['low'], df['close'])
            indicators.support_level = sr_levels['support']
            indicators.resistance_level = sr_levels['resistance']
            
            # Trend and momentum
            indicators.trend_direction = self._determine_trend(df['close'])
            indicators.momentum_score = self._calculate_momentum_score(indicators)
            
            self.logger.info(f"Technical analysis completed. RSI: {indicators.rsi:.2f}, MACD: {indicators.macd:.4f}")
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error in technical analysis: {str(e)}")
            return TechnicalIndicators()
    
    def _prepare_dataframe(self, price_data: List[MarketData]) -> pd.DataFrame:
        """Convert market data to pandas DataFrame"""
        data = []
        for point in price_data:
            data.append({
                'timestamp': point.timestamp,
                'close': float(point.price),
                'high': float(point.price * Decimal('1.01')),  # Approximate high
                'low': float(point.price * Decimal('0.99')),   # Approximate low
                'volume': float(point.volume_24h) if point.volume_24h else 0
            })
        
        df = pd.DataFrame(data)
        df = df.sort_values('timestamp').reset_index(drop=True)
        return df
    
    def _calculate_rsi(self, prices: pd.Series) -> Optional[float]:
        """Calculate Relative Strength Index"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else None
            
        except Exception as e:
            self.logger.error(f"Error calculating RSI: {str(e)}")
            return None
    
    def _calculate_macd(self, prices: pd.Series) -> Dict[str, Optional[float]]:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        try:
            ema_fast = prices.ewm(span=self.macd_fast).mean()
            ema_slow = prices.ewm(span=self.macd_slow).mean()
            
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=self.macd_signal).mean()
            histogram = macd_line - signal_line
            
            return {
                'macd': float(macd_line.iloc[-1]) if not pd.isna(macd_line.iloc[-1]) else None,
                'signal': float(signal_line.iloc[-1]) if not pd.isna(signal_line.iloc[-1]) else None,
                'histogram': float(histogram.iloc[-1]) if not pd.isna(histogram.iloc[-1]) else None
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating MACD: {str(e)}")
            return {'macd': None, 'signal': None, 'histogram': None}
    
    def _calculate_bollinger_bands(self, prices: pd.Series) -> Dict[str, Optional[float]]:
        """Calculate Bollinger Bands"""
        try:
            sma = prices.rolling(window=self.bb_period).mean()
            std = prices.rolling(window=self.bb_period).std()
            
            upper_band = sma + (std * self.bb_std_dev)
            lower_band = sma - (std * self.bb_std_dev)
            width = ((upper_band - lower_band) / sma) * 100
            
            return {
                'upper': float(upper_band.iloc[-1]) if not pd.isna(upper_band.iloc[-1]) else None,
                'middle': float(sma.iloc[-1]) if not pd.isna(sma.iloc[-1]) else None,
                'lower': float(lower_band.iloc[-1]) if not pd.isna(lower_band.iloc[-1]) else None,
                'width': float(width.iloc[-1]) if not pd.isna(width.iloc[-1]) else None
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating Bollinger Bands: {str(e)}")
            return {'upper': None, 'middle': None, 'lower': None, 'width': None}
    
    def _analyze_volume(self, volumes: pd.Series) -> Dict[str, Optional[float]]:
        """Analyze volume patterns"""
        try:
            volume_sma = volumes.rolling(window=self.volume_period).mean()
            current_volume = volumes.iloc[-1]
            avg_volume = volume_sma.iloc[-1]
            
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            return {
                'sma': float(avg_volume) if not pd.isna(avg_volume) else None,
                'ratio': float(volume_ratio) if not pd.isna(volume_ratio) else None
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing volume: {str(e)}")
            return {'sma': None, 'ratio': None}
    
    def _find_support_resistance(self, highs: pd.Series, lows: pd.Series, closes: pd.Series) -> Dict[str, Optional[float]]:
        """Find support and resistance levels using pivot points"""
        try:
            # Simple pivot point calculation
            recent_data = closes.tail(20)  # Last 20 periods
            
            # Support: recent low levels
            support = float(recent_data.min())
            
            # Resistance: recent high levels  
            resistance = float(highs.tail(20).max())
            
            return {
                'support': support,
                'resistance': resistance
            }
            
        except Exception as e:
            self.logger.error(f"Error finding support/resistance: {str(e)}")
            return {'support': None, 'resistance': None}
    
    def _determine_trend(self, prices: pd.Series) -> Optional[str]:
        """Determine overall trend direction"""
        try:
            # Use simple moving averages to determine trend
            sma_short = prices.rolling(window=10).mean()
            sma_long = prices.rolling(window=20).mean()
            
            if len(sma_short) < 2 or len(sma_long) < 2:
                return "sideways"
            
            current_short = sma_short.iloc[-1]
            current_long = sma_long.iloc[-1]
            prev_short = sma_short.iloc[-2]
            prev_long = sma_long.iloc[-2]
            
            # Trend determination logic
            if current_short > current_long and prev_short > prev_long:
                if current_short > prev_short:
                    return "strong_uptrend"
                else:
                    return "uptrend"
            elif current_short < current_long and prev_short < prev_long:
                if current_short < prev_short:
                    return "strong_downtrend"
                else:
                    return "downtrend"
            else:
                return "sideways"
                
        except Exception as e:
            self.logger.error(f"Error determining trend: {str(e)}")
            return "sideways"
    
    def _calculate_momentum_score(self, indicators: TechnicalIndicators) -> Optional[float]:
        """Calculate overall momentum score (0-100)"""
        try:
            score = 50.0  # Neutral starting point
            
            # RSI contribution (30% weight)
            if indicators.rsi is not None:
                if indicators.rsi > 70:
                    score += 15  # Overbought (positive momentum)
                elif indicators.rsi < 30:
                    score -= 15  # Oversold (negative momentum)
                else:
                    score += (indicators.rsi - 50) * 0.3
            
            # MACD contribution (25% weight)
            if indicators.macd is not None and indicators.macd_signal is not None:
                macd_diff = indicators.macd - indicators.macd_signal
                score += macd_diff * 1000  # Scale appropriately
            
            # Bollinger Bands contribution (20% weight)
            if all([indicators.bb_upper, indicators.bb_lower, indicators.bb_middle]):
                bb_position = (indicators.bb_middle - indicators.bb_lower) / (indicators.bb_upper - indicators.bb_lower)
                score += (bb_position - 0.5) * 20
            
            # Volume contribution (15% weight)
            if indicators.volume_ratio is not None:
                if indicators.volume_ratio > self.volume_spike_threshold:
                    score += 10  # High volume is positive
                elif indicators.volume_ratio < 0.5:
                    score -= 5   # Low volume is slightly negative
            
            # Trend contribution (10% weight)
            trend_scores = {
                "strong_uptrend": 10,
                "uptrend": 5,
                "sideways": 0,
                "downtrend": -5,
                "strong_downtrend": -10
            }
            if indicators.trend_direction:
                score += trend_scores.get(indicators.trend_direction, 0)
            
            # Ensure score is within bounds
            return max(0.0, min(100.0, score))
            
        except Exception as e:
            self.logger.error(f"Error calculating momentum score: {str(e)}")
            return 50.0  # Return neutral score on error
